"""
"""

# Created on 2014.10.21
#
# Author: <PERSON>
#
# Copyright 2014 - 2020 <PERSON>
#
# This file is part of ldap3.
#
# ldap3 is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published
# by the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# ldap3 is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with ldap3 in the COPYING and COPYING.LESSER files.
# If not, see <http://www.gnu.org/licenses/>.

ad_2012_r2_schema = """
{
    "raw": {
        "attributeTypes": [
            "( 1.2.840.11355*******49 NAME 'attributeSecurityGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******703 NAME 'msDS-FilterContainers' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.655 NAME 'legacyExchangeDN' SYNTAX '1.2.840.113556.1.4.905' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.21 NAME 'cOMProgID' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2147 NAME 'msDNS-PropagationTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.301 NAME 'msSFU30KeyAttributes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.686 NAME 'domainID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.23 NAME 'msDFSR-ReplicationGroupGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.818 NAME 'productCode' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.******** NAME 'oncRpcNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.221 NAME 'sAMAccountName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.375 NAME 'systemFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.814 NAME 'msiScript' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.880 NAME 'fRSTimeLastCommand' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******850 NAME 'msDS-TopQuotaUsage' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2052 NAME 'msDS-OIDToGroupLinkBl' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.965 NAME 'mSMQSiteName' SYNTAX '1.2.840.113556.1.4.905' SINGLE-VALUE )",
            "( 1.2.840.11355*******373 NAME 'mS-SQL-Clustered' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.624 NAME 'ipsecOwnersReference' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******353 NAME 'localizationDisplayId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******637 NAME 'msWMI-StringValidValues' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2103 NAME 'msDS-MembersOfResourcePropertyList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.480 NAME 'defaultGroup' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.55 NAME 'dBCSPwd' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******330 NAME 'pKICriticalExtensions' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.93 NAME 'pwdProperties' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******840 NAME 'msDS-ObjectReference' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.7 NAME 'subRefs' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.845 NAME 'msiScriptName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2242 NAME 'msDS-MaximumRegistrationInactivityPeriod' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.7 NAME 'photo' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.713 NAME 'optionsLocation' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.942 NAME 'mSMQVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2138 NAME 'msDNS-NSEC3Iterations' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.471 NAME 'trustParent' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******237 NAME 'mSMQRoutingService' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.649 NAME 'primaryInternationalISDNNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******627 NAME 'msWMI-ID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2006 NAME 'msTSExpireDate4' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2003 NAME 'msTSExpireDate3' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2000 NAME 'msTSExpireDate2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.12 NAME 'documentTitle' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113549.1.9.8 NAME 'unstructuredAddress' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.340 NAME 'msSFU30Domains' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2069 NAME 'msDS-EnabledFeatureBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( *******.******* NAME 'shadowMin' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******412 NAME 'primaryGroupToken' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.358 NAME 'netbootInitialization' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2136 NAME 'msDNS-NSEC3HashAlgorithm' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.1 NAME 'instanceType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.846 NAME 'msiScriptSize' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.20 NAME 'msDFSR-RdcMinFileSizeInKb' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.663 NAME 'partialAttributeDeletionList' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2078 NAME 'msTSSecondaryDesktopBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******995 NAME 'msTSManagingLS' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.371 NAME 'rIDAllocationPool' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.677 NAME 'replTopologyStayOfExecution' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.3 NAME 'replPropertyMetaData' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2036 NAME 'msDFS-Commentv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.329 NAME 'versionNumberLo' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.234 NAME 'printEndTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******673 NAME 'msPKI-OID-User-Notice' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.684 NAME 'certificateAuthorityObject' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.290 NAME 'printNumberUp' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******625 NAME 'msWMI-ClassDefinition' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******784 NAME 'msDS-LogonTimeSyncInterval' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******910 NAME 'unixUserPassword' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******29 NAME 'trustAuthIncoming' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******319 NAME 'aCSNonReservedTokenSize' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******628 NAME 'msWMI-IntDefault' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******249 NAME 'proxiedObjectName' SYNTAX '1.2.840.113556.1.4.903' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2173 NAME 'msKds-PublicKeyLength' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******7 NAME 'destinationIndicator' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2187 NAME 'msDS-ValueTypeReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.897 NAME 'aCSMaxAggregatePeakRatePerUser' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******335 NAME 'pKIEnrollmentAccess' SYNTAX '1.2.840.113556.1.4.907' )",
            "( 1.2.840.11355*******708 NAME 'msDS-ReplValueMetaData' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******690 NAME 'adminMultiselectPropertyPages' SYNTAX '*******.4.1.1466.************' )",
            "( *******5 NAME 'userPassword' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2200 NAME 'msDS-GroupMSAMembership' SYNTAX '1.2.840.113556.1.4.907' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.500 NAME 'fRSServiceCommand' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 2.16.840.1.113730.3.1.1 NAME 'carLicense' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2038 NAME 'msDFS-TargetListv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.27 NAME 'msDFSR-DeletedSizeInMb' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******648 NAME 'msWMI-TargetPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.******* NAME 'shadowLastChange' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******793 NAME 'msDS-NonMembers' SYNTAX '*******.4.1.1466.************' )",
            "( *******.******** NAME 'macAddress' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.265 NAME 'notes' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2274 NAME 'msDS-CloudIssuerPublicCertificates' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******982 NAME 'msTSMaxConnectionTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******959 NAME 'msDS-isGC' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******424 NAME 'msCOM-PartitionSetLink' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.516 NAME 'serverReferenceBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******977 NAME 'msTSHomeDirectory' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******369 NAME 'mS-SQL-ServiceAccount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.530 NAME 'nonSecurityMember' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.506 NAME 'objectCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******386 NAME 'mS-SQL-GPSLongitude' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******437 NAME 'msPKI-Supersede-Templates' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******707 NAME 'msDS-ReplAttributeMetaData' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.652 NAME 'assistant' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******644 NAME 'msWMI-SourceOrganization' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******443 NAME 'msDS-Site-Affinity' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.286 NAME 'printRateUnit' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******444 NAME 'msDS-Preferred-GC-Site' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.589 NAME 'meetingBandwidth' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******706 NAME 'msDS-NCReplOutboundNeighbors' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******709 NAME 'msDS-HasInstantiatedNCs' SYNTAX '1.2.840.113556.1.4.903' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.79 NAME 'minPwdLength' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******952 NAME 'ms-net-ieee-80211-GP-PolicyData' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.865 NAME 'pekList' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******6 NAME 'registeredAddress' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2179 NAME 'msKds-CreateTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2149 NAME 'msDNS-NSEC3CurrentSalt' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******815 NAME 'msDS-TasksForAzRoleBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2148 NAME 'msDNS-NSEC3UserSalt' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2197 NAME 'msDS-ManagedPasswordId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******407 NAME 'mS-SQL-ThirdParty' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.510 NAME 'serviceBindingInformation' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******416 NAME 'mSMQSiteNameEx' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******426 NAME 'msCOM-UserPartitionSetLink' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******303 NAME 'tokenGroupsNoGCAcceptable' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.596 NAME 'msExchHouseIdentifier' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2233 NAME 'msDS-cloudExtensionAttribute20' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.335 NAME 'currentLocation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.20 NAME 'homePhone' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******441 NAME 'msDS-Cached-Membership' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.14 NAME 'msDFSR-Schedule' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.622 NAME 'ipsecDataType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.645 NAME 'userCert' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.367 NAME 'rpcNsCodeset' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.223 NAME 'serverName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.950 NAME 'mSMQServices' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2250 NAME 'msDS-DeviceOSVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.332 NAME 'birthLocation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******440 NAME 'msDs-Schema-Extensions' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******348 NAME 'gPCMachineExtensionNames' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******833 NAME 'msDS-ExternalKey' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.858 NAME 'netbootTools' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******717 NAME 'msDS-AdditionalDnsHostName' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.770 NAME 'aCSEnableACSService' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******70 NAME 'systemOnly' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.32 NAME 'domainPolicyObject' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.766 NAME 'aCSAllocableRSVPBandwidth' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.9 NAME 'helpData32' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******805 NAME 'msDS-AzGenerateAudits' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.276 NAME 'driverVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******317 NAME 'aCSMinimumDelayVariation' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.302 NAME 'sAMAccountType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.610 NAME 'employeeNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.30 NAME 'attributeID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( *******.4.1.1466.101.119.3 NAME 'entryTTL' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******843 NAME 'msDRM-IdentityCertificate' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.103 NAME 'msDFSR-ComputerReferenceBL' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******989 NAME 'msTSWorkDirectory' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******674 NAME 'msPKI-Certificate-Application-Policy' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.716 NAME 'mscopeId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.514 NAME 'physicalLocationObject' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.570 NAME 'meetingProtocol' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.370 NAME 'objectClassCategory' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.15 NAME 'msDFSR-Keywords' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.812 NAME 'createWizardExt' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.61 NAME 'lockOutObservationWindow' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.750 NAME 'groupType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******459 NAME 'msDS-Behavior-Version' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.937 NAME 'mSMQSignKey' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.913 NAME 'allowedAttributes' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.120 NAME 'uSNChanged' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.340 NAME 'rightsGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.277 NAME 'otherHomePhone' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******309 NAME 'mSMQInterval2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******439 NAME 'msPKI-Certificate-Policy' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******308 NAME 'mSMQInterval1' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******383 NAME 'mS-SQL-ConnectionURL' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2176 NAME 'msKds-Version' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.859 NAME 'netbootLocallyInstalledOSes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.967 NAME 'mSMQSignCertificatesMig' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2232 NAME 'msDS-cloudExtensionAttribute19' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2231 NAME 'msDS-cloudExtensionAttribute18' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2230 NAME 'msDS-cloudExtensionAttribute17' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2229 NAME 'msDS-cloudExtensionAttribute16' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2228 NAME 'msDS-cloudExtensionAttribute15' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2227 NAME 'msDS-cloudExtensionAttribute14' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2226 NAME 'msDS-cloudExtensionAttribute13' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2225 NAME 'msDS-cloudExtensionAttribute12' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2142 NAME 'msDNS-SecureDelegationPollingPeriod' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2224 NAME 'msDS-cloudExtensionAttribute11' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.718 NAME 'dhcpProperties' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2223 NAME 'msDS-cloudExtensionAttribute10' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******57 NAME 'serverRole' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******394 NAME 'mS-SQL-AllowAnonymousSubscription' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.563 NAME 'shellPropertyPages' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******315 NAME 'aCSMinimumPolicedSize' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.273 NAME 'printStatus' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.644 NAME 'showInAddressBook' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.626 NAME 'ipsecISAKMPReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******925 NAME 'msDS-hasFullReplicaNCs' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.940 NAME 'mSMQCSPName' SYNTAX '1.2.840.113556.1.4.905' SINGLE-VALUE )",
            "( 1.2.840.113556.********.30 NAME 'msDFSR-MinDurationCacheInMin' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.243 NAME 'printColor' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2193 NAME 'msDS-TDOIngressBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( *******.******* NAME 'gidNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******993 NAME 'msTSExpireDate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******* NAME 'knowledgeInformation' SYNTAX '1.2.840.113556.1.4.905' )",
            "( 1.2.840.113556.1.4.908 NAME 'extendedClassInfo' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.953 NAME 'mSMQSiteID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2040 NAME 'msDFS-LinkSecurityDescriptorv2' SYNTAX '1.2.840.113556.1.4.907' SINGLE-VALUE )",
            "( 1.2.840.11355*******343 NAME 'dSUIAdminNotification' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******700 NAME 'msTAPI-ConferenceBlob' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.486 NAME 'fRSWorkingPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.62 NAME 'scriptPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******810 NAME 'msDS-TasksForAzTask' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.31 NAME 'msDFSR-MaxAgeInCacheInMin' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******9 NAME 'cOMClassID' SYNTAX '*******.4.1.1466.************' )",
            "( 2.16.840.1.113730.3.1.216 NAME 'userPKCS12' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******08 NAME 'remoteSourceType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.704 NAME 'dhcpServers' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.876 NAME 'fRSMemberReferenceBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2261 NAME 'msDS-DeviceLocation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.82 NAME 'moniker' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.289 NAME 'printMediaReady' SYNTAX '*******.4.1.1466.************' )",
            "( *******.******** NAME 'ipProtocolNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******209 NAME 'shortServerName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.910 NAME 'fromEntry' SYNTAX '*******.4.1.1466.***********' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.636 NAME 'privilegeAttributes' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2025 NAME 'msDS-IsUserCachableAtRodc' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******715 NAME 'msDS-SPNSuffixes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.562 NAME 'adminPropertyPages' SYNTAX '*******.4.1.1466.************' )",
            "( 0.9.2342.********.100.1.10 NAME 'manager' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'distinguishedName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******356 NAME 'validAccesses' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2053 NAME 'msImaging-PSPIdentifier' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.459 NAME 'machineWidePolicy' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******403 NAME 'mS-SQL-AllowKnownPullSubscription' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.283 NAME 'assetNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.885 NAME 'terminalServer' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2012 NAME 'msDS-MinimumPasswordAge' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.********.7 NAME 'msDFSR-ConflictPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******831 NAME 'msDS-ByteArray' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******35 NAME 'trustAuthOutgoing' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2258 NAME 'msDS-RegisteredOwner' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.608 NAME 'queryPolicyBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******09 NAME 'replicaSource' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2296 NAME 'msDS-AssignedAuthNPolicyBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.402 NAME 'helpData16' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.232 NAME 'defaultPriority' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******388 NAME 'mS-SQL-Version' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.364 NAME 'operatingSystemVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2284 NAME 'msDS-ServiceTGTLifetime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******460 NAME 'msDS-User-Account-Control-Computed' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.75 NAME 'maxRenewAge' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.285 NAME 'printRate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.911 NAME 'allowedChildClasses' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.615 NAME 'personalTitle' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******225 NAME 'mSMQPrevSiteGates' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2131 NAME 'msDNS-SignWithNSEC3' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2007 NAME 'msTSLicenseVersion4' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.13 NAME 'documentVersion' SYNTAX '*******.4.1.1466.************' )",
            "( 0.9.2342.********.100.1.3 NAME 'mail' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2004 NAME 'msTSLicenseVersion3' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2001 NAME 'msTSLicenseVersion2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.507 NAME 'volumeCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******37 NAME 'uNCName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2022 NAME 'msDS-ResultantPSO' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******68 NAME 'modifiedCount' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******809 NAME 'msDS-OperationsForAzTaskBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.328 NAME 'versionNumberHi' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2099 NAME 'msDS-ClaimAttributeSource' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.754 NAME 'rpcNsEntryFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.778 NAME 'aCSDSBMDeadTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.917 NAME 'mSMQQueueType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.326 NAME 'packageName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.422 NAME 'domainPolicyReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2241 NAME 'msDS-RegistrationQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.3 NAME 'msDFSR-RootPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******817 NAME 'msDS-AzApplicationVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.436 NAME 'directReports' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.240 NAME 'printOrientationsSupported' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.574 NAME 'meetingLanguage' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.43 NAME 'fRSVersionGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******0 NAME 'supportedApplicationContext' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.26 NAME 'rDNAttID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******409 NAME 'masteredBy' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.752 NAME 'userSharedFolderOther' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2199 NAME 'msDS-ManagedPasswordInterval' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******932 NAME 'msDS-IsFullReplicaFor' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.22 NAME 'msDFSR-RootFence' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.789 NAME 'transportDLLName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.499 NAME 'contextMenu' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.202 NAME 'auditingPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.11 NAME 'msDFSR-TombstoneExpiryInMin' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******364 NAME 'mS-SQL-RegisteredOwner' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.8 NAME 'userClass' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.775 NAME 'aCSMaxSizeOfRSVPLogFile' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******44 NAME 'operatorCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******238 NAME 'mSMQDsService' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******984 NAME 'msTSReconnectionAction' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2286 NAME 'msDS-AssignedAuthNPolicySiloBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2180 NAME 'msImaging-ThumbprintHash' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.68 NAME 'machineArchitecture' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******311 NAME 'printDuplexSupported' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******795 NAME 'msDS-AzDomainTimeout' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******992 NAME 'msTSProperty02' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.583 NAME 'meetingURL' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******991 NAME 'msTSProperty01' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.635 NAME 'privilegeValue' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2100 NAME 'msDS-ClaimTypeAppliesToClass' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.115 NAME 'invocationId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2288 NAME 'msDS-AuthNPolicySiloMembersBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******681 NAME 'msWMI-intFlags4' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******680 NAME 'msWMI-intFlags3' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******679 NAME 'msWMI-intFlags2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******678 NAME 'msWMI-intFlags1' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.100 NAME 'msDFSR-MemberReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******00 NAME 'priorValue' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******379 NAME 'mS-SQL-Vines' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******377 NAME 'mS-SQL-TCPIP' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2172 NAME 'msKds-SecretAgreementParam' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2222 NAME 'msDS-cloudExtensionAttribute9' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2221 NAME 'msDS-cloudExtensionAttribute8' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2220 NAME 'msDS-cloudExtensionAttribute7' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2219 NAME 'msDS-cloudExtensionAttribute6' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2218 NAME 'msDS-cloudExtensionAttribute5' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.661 NAME 'isDefunct' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2217 NAME 'msDS-cloudExtensionAttribute4' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.653 NAME 'managedBy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2216 NAME 'msDS-cloudExtensionAttribute3' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2215 NAME 'msDS-cloudExtensionAttribute2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2214 NAME 'msDS-cloudExtensionAttribute1' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.588 NAME 'meetingEndTime' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.498 NAME 'creationWizard' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******915 NAME 'msRADIUS-FramedIpv6Prefix' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.12 NAME 'msDFSR-FileFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'x121Address' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.637 NAME 'privilegeHolder' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.214 NAME 'originalDisplayTableMSDOS' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.211 NAME 'schedule' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******228 NAME 'mSMQDsServices' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.64 NAME 'logonHours' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.883 NAME 'msRRASVendorAttributeEntry' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.58 NAME 'localeID' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.97 NAME 'preferredOU' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2033 NAME 'msDFS-NamespaceIdentityGUIDv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******334 NAME 'pKIDefaultCSPs' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******411 NAME 'ms-DS-MachineAccountQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.891 NAME 'gPLink' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.617 NAME 'homePostalAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.320 NAME 'implementedCategories' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.19 NAME 'uSNCreated' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.651 NAME 'otherMailbox' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.345 NAME 'msSFU30NSMAPFieldPosition' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.618 NAME 'wellKnownObjects' SYNTAX '1.2.840.113556.1.4.903' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2160 NAME 'msDS-ClaimIsSingleValued' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.1 NAME 'msDFSR-Version' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.874 NAME 'fRSFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******713 NAME 'MSMQ-SecuredSource' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.825 NAME 'enrollmentProviders' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.666 NAME 'syncAttributes' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.665 NAME 'syncMembership' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.48 NAME 'keywords' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2169 NAME 'msKds-KDFAlgorithmID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.370 NAME 'rIDAvailablePool' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.214 NAME 'nextLevelStore' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******145 NAME 'msRADIUSCallbackNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.303 NAME 'msSFU30IntraFieldSeparator' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.346 NAME 'desktopProfile' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.20 NAME 'cOMInterfaceID' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.279 NAME 'printMinXExtent' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******213 NAME 'assocNTAccount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.671 NAME 'msiFileList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2032 NAME 'msDFS-GenerationGUIDv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2279 NAME 'msDS-UserTGTLifetime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.********.10 NAME 'msDFSR-ReplicationGroupType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******790 NAME 'msDS-PerUserTrustTombstonesQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******124 NAME 'msNPCallingStationID' SYNTAX '*******.4.1.1466.************' )",
            "( 0.9.2342.********.100.1.2 NAME 'textEncodedORAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.487 NAME 'fRSRootPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******807 NAME 'msDS-MembersForAzRoleBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******823 NAME 'msieee80211-ID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.791 NAME 'transportType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.674 NAME 'rootTrust' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******641 NAME 'msWMI-PropertyName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.25 NAME 'mayContain' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******438 NAME 'msPKI-RA-Policies' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.769 NAME 'aCSEventLogLevel' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.1.1.1.0 NAME 'uidNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.1.1.1.9 NAME 'shadowInactive' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.945 NAME 'mSMQSiteGates' SYNTAX '*******.4.1.1466.************' )",
            "( *******5 NAME 'internationalISDNNumber' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******979 NAME 'msTSAllowLogon' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.274 NAME 'printSpooling' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.242 NAME 'printCollate' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******345 NAME 'dSUIShellMaximum' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.693 NAME 'pendingCACertificates' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2257 NAME 'msDS-DeviceObjectVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.852 NAME 'netbootCurrentClientCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.534 NAME 'fRSLevelLimit' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******685 NAME 'msWMI-Parm4' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******684 NAME 'msWMI-Parm3' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******988 NAME 'msTSDefaultToMainPrinter' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******913 NAME 'msRADIUS-FramedInterfaceId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.353 NAME 'displayNamePrintable' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******696 NAME 'lastLogonTimestamp' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******683 NAME 'msWMI-Parm2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.330 NAME 'lastUpdateSequence' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.696 NAME 'currentParentCA' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.689 NAME 'cRLObject' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******682 NAME 'msWMI-Parm1' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.22 NAME 'governsID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******632 NAME 'msWMI-Int8Default' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******69 NAME 'logonCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.772 NAME 'aCSPolicyName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******8 NAME 'authorityRevocationList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******212 NAME 'isEphemeral' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.324 NAME 'packageType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******435 NAME 'msPKI-Template-Minor-Revision' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2010 NAME 'msTSLSProperty02' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******961 NAME 'msDS-SiteName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2009 NAME 'msTSLSProperty01' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******336 NAME 'replInterval' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2066 NAME 'msDS-RequiredDomainBehaviorVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2185 NAME 'msDS-GeoCoordinatesLongitude' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2182 NAME 'msDS-AllowedToActOnBehalfOfOtherIdentity' SYNTAX '1.2.840.113556.1.4.907' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( *******.*******1 NAME 'shadowFlag' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.8 NAME 'msDFSR-ConflictSizeInMb' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.357 NAME 'nTMixedDomain' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2191 NAME 'msDS-IngressClaimsTransformationPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******892 NAME 'msPKIRoamingTimeStamp' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2174 NAME 'msKds-PrivateKeyLength' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.324 NAME 'addressEntryDisplayTable' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.218 NAME 'applicationName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******318 NAME 'aCSNonReservedPeakRate' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2023 NAME 'msDS-PasswordSettingsPrecedence' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.99 NAME 'priorSetTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.914 NAME 'allowedAttributesEffective' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.271 NAME 'printOwner' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******996 NAME 'msDS-UserPasswordExpiryTimeComputed' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.930 NAME 'mSMQServiceType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******780 NAME 'hideFromAB' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.578 NAME 'meetingContactInfo' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2020 NAME 'msDS-PSOAppliesTo' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******944 NAME 'msDS-PhoneticDepartment' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******792 NAME 'msDS-AzLDAPQuery' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.251 NAME 'cOMTreatAsClassId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******4 NAME 'builtinModifiedCount' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( *******.******* NAME 'shadowMax' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.325 NAME 'setupCommand' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******647 NAME 'msWMI-TargetObject' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.420 NAME 'publicKeyPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******714 NAME 'MSMQ-MulticastAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******677 NAME 'msWMI-Genus' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2281 NAME 'msDS-ComputerTGTLifetime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******671 NAME 'msPKI-OID-Attribute' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.36 NAME 'dMDLocation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.810 NAME 'createDialog' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2140 NAME 'msDNS-DSRecordSetTTL' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******358 NAME 'schemaInfo' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******624 NAME 'msWMI-ChangeDate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******975 NAME 'msDS-RevealedListBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******962 NAME 'msDS-PromotionSettings' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.229 NAME 'driverName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.378 NAME 'dnsAllowDynamic' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******246 NAME 'interSiteTopologyGenerator' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.817 NAME 'localizedDescription' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2235 NAME 'msDS-ReplValueMetaDataExt' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******933 NAME 'msDS-IsDomainFor' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2213 NAME 'msDS-RIDPoolAllocationEnabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.73 NAME 'lockoutThreshold' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.50 NAME 'lastContentIndexed' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.824 NAME 'signatureAlgorithms' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.860 NAME 'netbootServer' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.40 NAME 'msDFSR-StagingCleanupTriggerInPercent' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******695 NAME 'msMQ-Recipient-FormatName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******966 NAME 'msTPM-OwnerInformation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******56 NAME 'comment' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.650 NAME 'mhsORAddress' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.929 NAME 'mSMQInRoutingServers' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******787 NAME 'msDS-AllowedToDelegateTo' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******389 NAME 'mS-SQL-Language' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.18 NAME 'msDFSR-ContentSetGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.8 NAME 'possSuperiors' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.912 NAME 'allowedChildClassesEffective' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2132 NAME 'msDNS-NSEC3OptOut' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******36 NAME 'trustType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******879 NAME 'msDS-SourceObjectDN' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.533 NAME 'fRSReplicaSetGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******434 NAME 'msPKI-Template-Schema-Version' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.241 NAME 'printMaxCopies' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.961 NAME 'mSMQSiteForeign' SYNTAX '*******.4.1.1466.***********' )",
            "( 1.2.840.11355*******808 NAME 'msDS-OperationsForAzTask' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******242 NAME 'dNReferenceUpdate' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 0.9.2342.********.100.1.5 NAME 'drink' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******923 NAME 'msDS-KrbTgtLink' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******402 NAME 'mS-SQL-Publisher' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2018 NAME 'msDS-LockoutDuration' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.688 NAME 'cAWEBURL' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.*******3 NAME 'bootParameter' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.536 NAME 'fRSExtensions' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.233 NAME 'printStartTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******788 NAME 'msDS-PerUserTrustQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******59 NAME 'accountExpires' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( *******.*******4 NAME 'nisNetgroupTriple' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******390 NAME 'mS-SQL-Description' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.224 NAME 'defaultSecurityDescriptor' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113549.1.9.2 NAME 'unstructuredName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.695 NAME 'pendingParentCA' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******375 NAME 'mS-SQL-MultiProtocol' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2152 NAME 'msAuthz-LastEffectiveSecurityPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.56 NAME 'localPolicyFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******392 NAME 'mS-SQL-InformationDirectory' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2159 NAME 'msDS-ClaimIsValueSpaceRestricted' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.708 NAME 'dhcpSites' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.717 NAME 'dhcpState' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.762 NAME 'aCSServiceType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******32 NAME 'trustDirection' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.312 NAME 'rpcNsObjectID' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******395 NAME 'mS-SQL-Alias' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 2.5.18.2 NAME 'modifyTimeStamp' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2145 NAME 'msDNS-DNSKEYRecords' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.301 NAME 'wbemPath' SYNTAX '*******.4.1.1466.************' )",
            "( 2.5.4.0 NAME 'objectClass' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.21 NAME 'msDFSR-DfsPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******429 NAME 'msPKI-RA-Signature' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******699 NAME 'msTAPI-ProtocolId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2086 NAME 'msSPP-PhoneLicense' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******20 NAME 'schemaFlagsEx' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******945 NAME 'msDS-PhoneticCompanyName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.916 NAME 'canonicalName' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.702 NAME 'dhcpObjName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2238 NAME 'msds-memberTransitive' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******33 NAME 'trustPartner' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.927 NAME 'mSMQSites' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.867 NAME 'altSecurityIdentities' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.615 NAME 'shellContextMenu' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.866 NAME 'pekKeyChangeInterval' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2046 NAME 'addressBookRoots2' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.27 NAME 'currentValue' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******66 NAME 'groupMembershipSAM' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******926 NAME 'msDS-NeverRevealGroup' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.28 NAME 'msDFSR-ReadOnly' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******314 NAME 'aCSMaximumSDUSize' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.457 NAME 'localPolicyReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******189 NAME 'msRASSavedCallbackNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******918 NAME 'msRADIUS-SavedFramedIpv6Route' SYNTAX '*******.4.1.1466.************' )",
            "( ******** NAME 'dITContentRules' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.895 NAME 'transportAddressAttribute' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******418 NAME 'tokenGroupsGlobalAndUniversal' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.850 NAME 'netbootLimitClients' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 2.16.840.1.113730.3.1.2 NAME 'departmentNumber' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.944 NAME 'mSMQSite2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.943 NAME 'mSMQSite1' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******664 NAME 'msDS-Replication-Notify-Subsequent-DSA-Delay' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.537 NAME 'dynamicLDAPServer' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2249 NAME 'msDS-DeviceOSType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.35 NAME 'employeeID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2137 NAME 'msDNS-NSEC3RandomSaltLength' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2151 NAME 'msAuthz-ProposedSecurityPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.267 NAME 'uSNDSALastObjRemoved' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.963 NAME 'mSMQQueueJournalQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.607 NAME 'queryPolicyObject' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******978 NAME 'msTSHomeDrive' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.593 NAME 'msExchLabeledURI' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******329 NAME 'pKIMaxIssuingDepth' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2061 NAME 'msDS-EnabledFeature' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.278 NAME 'printMaxYExtent' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******6 NAME 'codePage' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******802 NAME 'msDS-AzBizRuleLanguage' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.363 NAME 'operatingSystem' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.761 NAME 'aCSMaxDurationPerFlow' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.921 NAME 'mSMQJournalQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2106 NAME 'msSPP-CSVLKPartialProductKey' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******190 NAME 'msRASSavedFramedIPAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2171 NAME 'msKds-SecretAgreementAlgorithmID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.703 NAME 'dhcpObjDescription' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.890 NAME 'uPNSuffixes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******720 NAME 'msDS-ReplicationEpoch' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.*******4 NAME 'bootFile' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.614 NAME 'adminContextMenu' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.231 NAME 'oMSyntax' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.8 NAME 'userAccountControl' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.621 NAME 'ipsecID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.511 NAME 'flatName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.784 NAME 'aCSIdentityName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******5 NAME 'msiScriptPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******25 NAME 'supplementalCredentials' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2287 NAME 'msDS-AuthNPolicySiloMembers' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******99 NAME 'serviceInstanceVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******347 NAME 'sPNMappings' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.933 NAME 'mSMQComputerType' SYNTAX '1.2.840.113556.1.4.905' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.780 NAME 'aCSNonReservedTxLimit' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******227 NAME 'mSMQRoutingServices' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2074 NAME 'msTSPrimaryDesktopBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( ******** NAME 'createTimeStamp' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( *******.******** NAME 'ipHostNumber' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******130 NAME 'msNPSavedCallingStationID' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.700 NAME 'dhcpFlags' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.629 NAME 'ipsecFilterReference' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.40 NAME 'fromServer' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.568 NAME 'meetingKeyword' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2178 NAME 'msKds-UseStartTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******321 NAME 'aCSNonReservedMinPolicedSize' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.246 NAME 'printLanguage' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.54 NAME 'tombstoneLifetime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.765 NAME 'aCSPermissionBits' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( *******.******* NAME 'shadowWarning' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******398 NAME 'mS-SQL-LastBackupDate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2 NAME 'objectGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.146 NAME 'company' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******710 NAME 'msDS-AllowedDNSSuffixes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******841 NAME 'msDS-ObjectReferenceBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 2.5.4.8 NAME 'st' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.341 NAME 'msSFU30YpServers' SYNTAX '*******.4.1.1466.************' )",
            "( 2.5.4.4 NAME 'sn' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.707 NAME 'dhcpRanges' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.282 NAME 'printMemory' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.924 NAME 'mSMQPrivacyLevel' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.333 NAME 'oMTIndxGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.205 NAME 'pKTGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2251 NAME 'msDS-DevicePhysicalIDs' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******410 NAME 'mS-DS-CreatorSID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.345 NAME 'groupPriority' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2030 NAME 'msDFS-SchemaMajorVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.513 NAME 'siteObjectBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.87 NAME 'nETBIOSName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2175 NAME 'msKds-RootKeyData' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.24 NAME 'mustContain' SYNTAX '*******.4.1.1466.************' )",
            "( ******** NAME 'houseIdentifier' SYNTAX '*******.4.1.1466.************' )",
            "( *******.*******6 NAME 'nisMapName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******917 NAME 'msRADIUS-FramedIpv6Route' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.307 NAME 'msSFU30MasterServerName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.98 NAME 'primaryGroupID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******837 NAME 'msDs-masteredBy' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.200 NAME 'controlAccessRights' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******158 NAME 'msRADIUSFramedRoute' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******07 NAME 'remoteSource' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******971 NAME 'msDS-LastFailedInteractiveLogonTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******958 NAME 'msDS-AuthenticatedAtDC' SYNTAX '*******.4.1.1466.************' )",
            "( 2.5.4.5 NAME 'serialNumber' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.509 NAME 'serviceClassName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2050 NAME 'msPKI-CredentialRoamingTokens' SYNTAX '1.2.840.113556.1.4.903' )",
            "( 1.2.840.113556.1.4.2008 NAME 'msTSManagingLS4' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2005 NAME 'msTSManagingLS3' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2002 NAME 'msTSManagingLS2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******363 NAME 'mS-SQL-Name' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.41 NAME 'mobile' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2108 NAME 'msTPM-OwnerInformationTemp' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.886 NAME 'purportedSearch' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******384 NAME 'mS-SQL-PublicationURL' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2271 NAME 'msDS-CloudIsManaged' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.41 NAME 'generatedConnection' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.864 NAME 'netbootSCPBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******396 NAME 'mS-SQL-Size' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******15 NAME 'rpcNsInterfaceID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.56 NAME 'documentPublisher' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.619 NAME 'dNSHostName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2273 NAME 'msDS-CloudAnchor' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.939 NAME 'mSMQNameStyle' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.882 NAME 'fRSVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.777 NAME 'aCSDSBMRefresh' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.80 NAME 'minTicketAge' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******310 NAME 'mSMQSiteGatesMig' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.83 NAME 'monikerDisplayName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2150 NAME 'msAuthz-EffectiveSecurityPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.15 NAME 'hasPartialReplicaNCs' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2194 NAME 'msDS-TDOEgressBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******811 NAME 'msDS-TasksForAzTaskBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******987 NAME 'msTSConnectPrinterDrives' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******953 NAME 'ms-net-ieee-80211-GP-PolicyReserved' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******313 NAME 'aCSMaxTokenBucketPerFlow' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.579 NAME 'meetingOwner' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******2 NAME 'badPwdCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.39 NAME 'forceLogoff' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.326 NAME 'perRecipDialogDisplayTable' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.51 NAME 'lastLogoff' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******796 NAME 'msDS-AzScriptEngineCacheMax' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2269 NAME 'msDS-IssuerPublicCertificates' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******639 NAME 'msWMI-Name' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.4 NAME 'replUpToDateVector' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.470 NAME 'trustAttributes' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.515 NAME 'serverReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.308 NAME 'msSFU30OrderNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******669 NAME 'msDS-Approx-Immed-Subordinates' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2141 NAME 'msDNS-SignatureInceptionOffset' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2186 NAME 'msDS-IsPossibleValuesPresent' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.78 NAME 'minPwdAge' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.********.339 NAME 'msSFU30NisDomain' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******701 NAME 'msTAPI-IpAddress' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.249 NAME 'cOMCLSID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.774 NAME 'aCSMaxNoOfLogFiles' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.494 NAME 'siteServer' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.849 NAME 'netbootAllowNewClients' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******931 NAME 'msDS-KrbTgtLinkBl' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******789 NAME 'msDS-AllUsersTrustQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2156 NAME 'msAuthz-MemberRulesInCentralAccessPolicyBL' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.721 NAME 'ipPhone' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.613 NAME 'employeeType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******634 NAME 'msWMI-Int8Min' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2248 NAME 'msDS-IsEnabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******950 NAME 'msDS-AzGenericData' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******646 NAME 'msWMI-TargetNameSpace' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.816 NAME 'fileExtPriority' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.712 NAME 'optionDescription' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.321 NAME 'requiredCategories' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.255 NAME 'addressSyntax' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2110 NAME 'msTPM-TpmInformationForComputerBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******785 NAME 'msIIS-FTPRoot' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.518 NAME 'defaultHidingValue' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.946 NAME 'mSMQCost' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'generationQualifier' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.681 NAME 'indexedScopes' SYNTAX '*******.4.1.1466.************' )",
            "( *******.******** NAME 'nisMapEntry' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******865 NAME 'msDS-PrincipalName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2073 NAME 'msTSPrimaryDesktop' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.697 NAME 'cACertificateDN' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******354 NAME 'scopeFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******942 NAME 'msDS-PhoneticFirstName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.******** NAME 'ipNetmaskNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******997 NAME 'msDS-HABSeniorityIndex' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******307 NAME 'accountNameHistory' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.893 NAME 'gPCFunctionalityVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2203 NAME 'msDS-parentdistname' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******687 NAME 'extraColumns' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******834 NAME 'msDS-ExternalStore' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******983 NAME 'msTSMaxIdleTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.687 NAME 'cAConnect' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2048 NAME 'templateRoots2' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******54 NAME 'serverState' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******224 NAME 'parentGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.926 NAME 'mSMQTransactional' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.925 NAME 'mSMQOwnerID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2035 NAME 'msDFS-Ttlv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******50 NAME 'adminCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2041 NAME 'msDFS-LinkIdentityGUIDv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.490 NAME 'fRSDSPoll' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2015 NAME 'msDS-PasswordComplexityEnabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******05 NAME 'remoteServerName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.531 NAME 'nonSecurityMemberBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 2.16.840.1.113730.3.1.36 NAME 'thumbnailLogo' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.586 NAME 'meetingRecurrence' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******643 NAME 'msWMI-QueryLanguage' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.962 NAME 'mSMQQueueQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******415 NAME 'mSMQLabelEx' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.16 NAME 'nCName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2170 NAME 'msKds-KDFParam' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.567 NAME 'meetingDescription' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******704 NAME 'msDS-NCReplCursors' SYNTAX '*******.4.1.1466.************' )",
            "( ******** NAME 'facsimileTelephoneNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.851 NAME 'netbootMaxClients' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2016 NAME 'msDS-PasswordReversibleEncryptionEnabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******635 NAME 'msWMI-Int8ValidValues' SYNTAX '1.2.840.113556.1.4.906' )",
            "( 1.2.840.113556.1.4.719 NAME 'dhcpMaxKey' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******835 NAME 'msDS-Integer' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******208 NAME 'aNR' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******393 NAME 'mS-SQL-Database' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.42 NAME 'pager' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******914 NAME 'msRADIUS-SavedFramedInterfaceId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******391 NAME 'mS-SQL-Type' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.566 NAME 'meetingName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******23 NAME 'serviceClassInfo' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.26 NAME 'creationTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******03 NAME 'proxyLifetime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.660 NAME 'treeName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.892 NAME 'gPOptions' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.923 NAME 'mSMQAuthenticate' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******432 NAME 'msPKI-Certificate-Name-Flag' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.206 NAME 'pKT' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.287 NAME 'printNetworkAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******431 NAME 'msPKI-Private-Key-Flag' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******346 NAME 'templateRoots' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.657 NAME 'serviceDNSName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.868 NAME 'isCriticalSystemObject' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.301 NAME 'garbageCollPeriod' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.288 NAME 'printMACAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******304 NAME 'sDRightsEffective' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.380 NAME 'extendedCharsAllowed' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.86 NAME 'userWorkstations' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******360 NAME 'mS-DS-ConsistencyGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******712 NAME 'msPKI-OIDLocalizedName' SYNTAX '*******.4.1.1466.************' )",
            "( ******** NAME 'attributeTypes' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.31 NAME 'fRSReplicaSetType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.*********.57 NAME 'labeledURI' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.341 NAME 'appliesTo' SYNTAX '*******.4.1.1466.************' )",
            "( 2.5.4.11 NAME 'ou' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2293 NAME 'msDS-ServiceAuthNPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.346 NAME 'msSFU30PosixMember' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******973 NAME 'msDS-FailedInteractiveLogonCountAtLastSuccessfulLogon' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( ********0 NAME 'subSchemaSubEntry' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2262 NAME 'msDS-ApproximateLastLogonTimeStamp' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.222 NAME 'location' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.854 NAME 'netbootAnswerOnlyValidClients' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******949 NAME 'msDS-AzObjectGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 2.16.840.1.113730.3.1.34 NAME 'middleName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2158 NAME 'msDS-ClaimSourceType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.6 NAME 'roomNumber' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.706 NAME 'dhcpMask' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2109 NAME 'msTPM-TpmInformationForComputer' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.623 NAME 'ipsecData' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******171 NAME 'msRADIUSServiceType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.705 NAME 'dhcpSubnets' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******999 NAME 'msFVE-KeyPackage' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******968 NAME 'msDS-NC-RO-Replica-Locations-BL' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.36 NAME 'enabledConnection' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.472 NAME 'domainCrossRef' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.52 NAME 'lastLogon' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.28 NAME 'dnsRoot' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.966 NAME 'mSMQDigestsMig' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.878 NAME 'fRSPrimaryMember' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******848 NAME 'msDS-QuotaEffective' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******372 NAME 'mS-SQL-UnicodeSortOrder' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.639 NAME 'isMemberOfPartialAttributeSet' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.464 NAME 'wWWHomePage' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.711 NAME 'superScopeDescription' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******631 NAME 'msWMI-IntValidValues' SYNTAX '*******.4.1.1466.************' )",
            "( *******.******* NAME 'gecos' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2063 NAME 'msDS-OptionalFeatureFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.38 NAME 'flags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******240 NAME 'netbootSIFFile' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.690 NAME 'cAUsages' SYNTAX '*******.4.1.1466.************' )",
            "( 0.9.2342.********.100.1.60 NAME 'jpegPhoto' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2104 NAME 'msDS-MembersOfResourcePropertyListBL' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.66 NAME 'lSACreationTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.709 NAME 'dhcpReservations' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.934 NAME 'mSMQForeign' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******963 NAME 'msDS-SupportedEncryptionTypes' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******401 NAME 'mS-SQL-Keywords' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******705 NAME 'msDS-NCReplInboundNeighbors' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2128 NAME 'msDNS-KeymasterZones' SYNTAX '*******.4.1.1466.************' )",
            "( 2.5.4.19 NAME 'physicalDeliveryOfficeName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******694 NAME 'gPCWQLFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******95 NAME 'systemPossSuperiors' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.218 NAME 'oMObjectClass' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******226 NAME 'mSMQDependentClientServices' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******676 NAME 'msWMI-Class' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2144 NAME 'msDNS-SigningKeys' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******630 NAME 'msWMI-IntMin' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.372 NAME 'rIDPreviousAllocationPool' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.848 NAME 'appSchemaVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******981 NAME 'msTSMaxDisconnectionTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******645 NAME 'msWMI-TargetClass' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.535 NAME 'fRSRootSecurity' SYNTAX '1.2.840.113556.1.4.907' SINGLE-VALUE )",
            "( 1.2.840.11355*******423 NAME 'msCOM-PartitionLink' SYNTAX '*******.4.1.1466.************' )",
            "( *******2 NAME 'owner' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******436 NAME 'msPKI-Cert-Template-OID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******716 NAME 'msDS-IntId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.309 NAME 'msSFU30Name' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.254 NAME 'cOMTypelibId' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******642 NAME 'msWMI-Query' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******55 NAME 'uASCompat' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******623 NAME 'msWMI-Author' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******964 NAME 'msFVE-RecoveryPassword' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.37 NAME 'associatedDomain' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.764 NAME 'aCSPriority' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.571 NAME 'meetingType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.783 NAME 'defaultObjectCategory' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******365 NAME 'mS-SQL-Contact' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.679 NAME 'creator' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'certificateRevocationList' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.699 NAME 'dhcpType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******405 NAME 'mS-SQL-AllowQueuedUpdatingSubscription' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.915 NAME 'possibleInferiors' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2234 NAME 'netbootDUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.899 NAME 'aCSEnableRSVPAccounting' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.881 NAME 'fRSTimeLastConfigChange' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.55 NAME 'audio' SYNTAX '*******.4.1.1466.************' )",
            "( *******.*******3 NAME 'memberNisNetgroup' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.898 NAME 'aCSNonReservedTxSize' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.771 NAME 'servicePrincipalName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******820 NAME 'msDS-HasDomainNCs' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2070 NAME 'msTSEndpointData' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.620 NAME 'ipsecName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.458 NAME 'qualityOfService' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2042 NAME 'msDFS-ShortNameLinkPathv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******688 NAME 'msDS-Security-Group-Extra-Classes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2289 NAME 'msDS-UserAuthNPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.83 NAME 'repsTo' SYNTAX 'OctetString' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******357 NAME 'dSCorePropagationData' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2196 NAME 'msDS-ManagedPassword' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.647 NAME 'otherMobile' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2072 NAME 'msTSEndpointPlugin' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.749 NAME 'url' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.701 NAME 'dhcpIdentification' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******22 NAME 'serviceClassID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2058 NAME 'isRecycled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.213 NAME 'defaultClassStore' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2252 NAME 'msDS-DeviceID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.633 NAME 'policyReplicationFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******693 NAME 'msFRS-Hub-Member' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******328 NAME 'pKIKeyUsage' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.459 NAME 'networkAddress' SYNTAX '1.2.840.113556.1.4.905' )",
            "( 1.2.840.11355*******786 NAME 'msIIS-FTPDir' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.806 NAME 'treatAsLeaf' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.820 NAME 'bridgeheadServerListBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 0.9.2342.********.100.1.15 NAME 'documentLocation' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.36 NAME 'msDFSR-OnDemandExclusionDirectoryFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.640 NAME 'partialAttributeSet' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.853 NAME 'netbootAnswerRequests' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( ******** NAME 'member' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.323 NAME 'msSFU30Aliases' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******243 NAME 'mSMQQueueNameExt' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******370 NAME 'mS-SQL-CharacterSet' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******622 NAME 'msDS-Entry-Time-To-Die' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.460 NAME 'lDAPDisplayName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2031 NAME 'msDFS-SchemaMinorVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.*******2 NAME 'memberUid' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******800 NAME 'msDS-AzOperationID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.322 NAME 'categoryId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.60 NAME 'lockoutDuration' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.870 NAME 'frsComputerReferenceBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 2.5.4.45 NAME 'x500uniqueIdentifier' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.25 NAME 'msDFSR-Priority' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.847 NAME 'installUiLevel' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******842 NAME 'msDs-MaxValues' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 2.5.4.9 NAME 'street' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2154 NAME 'msAuthz-CentralAccessPolicyID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.3 NAME 'whenChanged' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******433 NAME 'msPKI-Minimal-Key-Size' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******814 NAME 'msDS-TasksForAzRole' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.101 NAME 'msDFSR-ComputerReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.580 NAME 'meetingIP' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.539 NAME 'initialAuthIncoming' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.356 NAME 'foreignIdentifier' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.565 NAME 'meetingID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.******* NAME 'unixHomeDirectory' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******721 NAME 'msDS-UpdateScript' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.557 NAME 'parentCA' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.255 NAME 'vendor' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.900 NAME 'aCSRSVPAccountFilesLocation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******320 NAME 'aCSNonReservedMaxSDUSize' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******956 NAME 'ms-net-ieee-8023-GP-PolicyReserved' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.773 NAME 'aCSRSVPLogFilesLocation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.951 NAME 'mSMQQMID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.102 NAME 'memberOf' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******397 NAME 'mS-SQL-CreationDate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2146 NAME 'msDNS-ParentHasSecureDelegation' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******13 NAME 'rpcNsBindings' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.656 NAME 'userPrincipalName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******934 NAME 'msDS-IsPartialReplicaFor' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2167 NAME 'msDS-PrimaryComputer' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.469 NAME 'USNIntersite' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******803 NAME 'msDS-AzLastImportedBizRulePath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2295 NAME 'msDS-AssignedAuthNPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'description' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.922 NAME 'mSMQLabel' SYNTAX '1.2.840.113556.1.4.905' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2024 NAME 'msDS-NcType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2011 NAME 'msDS-MaximumPasswordAge' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2291 NAME 'msDS-ComputerAuthNPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******832 NAME 'msDS-DateTime' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.281 NAME 'nTSecurityDescriptor' SYNTAX '1.2.840.113556.1.4.907' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.722 NAME 'otherIpPhone' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******368 NAME 'mS-SQL-Build' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.219 NAME 'iconPath' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******417 NAME 'mSMQComputerTypeEx' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.38 NAME 'associatedName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******986 NAME 'msTSConnectClientDrives' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2285 NAME 'msDS-AssignedAuthNPolicySilo' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******638 NAME 'msWMI-Mof' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.314 NAME 'rpcNsTransferSyntax' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******702 NAME 'msDS-TrustForestTrustInfo' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.557 NAME 'Enabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.21 NAME 'subClassOf' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 0.9.2342.********.100.1.44 NAME 'uniqueIdentifier' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******845 NAME 'msDS-QuotaAmount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******119 NAME 'msNPAllowDialin' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.33 NAME 'isSingleValued' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.782 NAME 'objectCategory' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2177 NAME 'msKds-DomainID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2195 NAME 'msDS-AppliesToResourceTypes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******52 NAME 'groupAttributes' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.334 NAME 'volTableIdxGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.272 NAME 'printNotify' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.334 NAME 'searchFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2298 NAME 'msDS-AuthNPolicySiloEnforced' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******428 NAME 'msCOM-ObjectId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.400 NAME 'addressEntryDisplayTableMSDOS' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.81 NAME 'modifiedCountAtLastProm' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.71 NAME 'machineRole' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******123 NAME 'msNPCalledStationID' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.654 NAME 'managedObjects' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.638 NAME 'isPrivilegeHolder' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******97 NAME 'systemMustContain' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.91 NAME 'otherLoginWorkstations' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.32 NAME 'msDFSR-DisablePacketPrivacy' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2297 NAME 'msDS-AuthNPolicyEnforced' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.576 NAME 'meetingMaxParticipants' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.******* NAME 'loginShell' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.779 NAME 'aCSCacheTimeout' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.751 NAME 'userSharedFolder' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.342 NAME 'msSFU30MaxGidNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******380 NAME 'mS-SQL-Status' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******3 NAME 'builtinCreationTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.277 NAME 'printMaxXExtent' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.230 NAME 'printSeparatorFile' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******387 NAME 'mS-SQL-GPSHeight' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2054 NAME 'msImaging-PSPString' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.235 NAME 'printFormName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******0 NAME 'telephoneNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******621 NAME 'msDS-Other-Settings' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.304 NAME 'msSFU30SearchAttributes' SYNTAX '*******.4.1.1466.************' )",
            "( 2.5.21.9 NAME 'structuralObjectClass' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.659 NAME 'serviceDNSNameType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.902 NAME 'aCSMaxSizeOfRSVPAccountFile' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.569 NAME 'meetingLocation' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.261 NAME 'division' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******640 NAME 'msWMI-NormalizedClass' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.300 NAME 'printerName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******960 NAME 'msDS-isRODC' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.268 NAME 'eFSPolicy' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******824 NAME 'msDS-AzMajorVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2134 NAME 'msDNS-DSRecordAlgorithms' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.270 NAME 'printShareName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******400 NAME 'mS-SQL-Applications' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******312 NAME 'aCSServerList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******376 NAME 'mS-SQL-SPX' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.368 NAME 'rIDManagerReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******371 NAME 'mS-SQL-SortOrder' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.118 NAME 'otherPager' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******894 NAME 'msPKIAccountCredentials' SYNTAX '1.2.840.113556.1.4.903' )",
            "( 1.2.840.113556.********.16 NAME 'msDFSR-Flags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******301 NAME 'tokenGroups' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******626 NAME 'msWMI-CreationDate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.14 NAME 'hasMasterNCs' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******53 NAME 'rid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2084 NAME 'msSPP-ConfirmationId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.2 NAME 'msDFSR-Extension' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******846 NAME 'msDS-DefaultQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.35 NAME 'rangeUpper' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******633 NAME 'msWMI-Int8Max' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.48 NAME 'isDeleted' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******327 NAME 'pKIDefaultKeySpec' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******408 NAME 'mS-DS-ReplicatesNCReason' SYNTAX '1.2.840.113556.1.4.903' )",
            "( 1.2.840.11355*******816 NAME 'msDS-AzClassId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2088 NAME 'msSPP-IssuanceLicense' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******672 NAME 'msPKI-OID-CPS' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.590 NAME 'meetingBlob' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.72 NAME 'marshalledInterface' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******385 NAME 'mS-SQL-GPSLatitude' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2097 NAME 'msDS-ClaimPossibleValues' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.26 NAME 'msDFSR-DeletedPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******826 NAME 'msDS-RetiredReplNCSignatures' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2143 NAME 'msDNS-SigningKeyDescriptors' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.491 NAME 'fRSFaultCondition' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2278 NAME 'msDS-UserAllowedToAuthenticateFrom' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2017 NAME 'msDS-LockoutObservationWindow' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2130 NAME 'msDNS-IsSigned' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2057 NAME 'msDS-HostServiceAccountBL' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.683 NAME 'cRLPartitionedRevocationList' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.481 NAME 'schemaUpdate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******332 NAME 'pKIOverlapPeriod' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.692 NAME 'previousCACertificates' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.573 NAME 'meetingApplication' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******849 NAME 'msDS-QuotaUsed' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.*******0 NAME 'ipNetworkNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.517 NAME 'ipsecPolicyReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******822 NAME 'msieee80211-DataType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.664 NAME 'syncWithObject' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2183 NAME 'msDS-GeoCoordinatesAltitude' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.284 NAME 'bytesPerMinute' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******39 NAME 'profilePath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'crossCertificatePair' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******929 NAME 'msDS-SecondaryKrbTgtNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2294 NAME 'msDS-ServiceAuthNPolicyBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******414 NAME 'dNSTombstoned' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.104 NAME 'ownerBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******930 NAME 'msDS-RevealedDSAs' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2240 NAME 'msDS-IssuerCertificates' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******692 NAME 'msFRS-Topology-Pref' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******58 NAME 'domainReplica' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.2 NAME 'whenCreated' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.76 NAME 'maxStorage' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.484 NAME 'fRSDirectoryFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******916 NAME 'msRADIUS-SavedFramedIpv6Prefix' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2087 NAME 'msSPP-ConfigLicense' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.627 NAME 'ipsecNFAReference' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.351 NAME 'auxiliaryClass' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.50 NAME 'linkID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******718 NAME 'msDS-AdditionalSamAccountName' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.35 NAME 'msDFSR-OnDemandExclusionFileFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.577 NAME 'meetingOriginator' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.169 NAME 'showInAdvancedViewOnly' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.582 NAME 'meetingAdvertiseScope' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.48 NAME 'buildingName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2181 NAME 'msImaging-HashAlgorithm' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2101 NAME 'msDS-ClaimSharesPossibleValuesWith' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.24 NAME 'contentIndexingAllowed' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.********.39 NAME 'msDFSR-CommonStagingSizeInMb' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2135 NAME 'msDNS-RFC5011KeyRollovers' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.682 NAME 'friendlyNames' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2071 NAME 'msTSEndpointType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2263 NAME 'msDS-RegisteredUsers' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2062 NAME 'msDS-OptionalFeatureGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.767 NAME 'aCSMaxPeakBandwidth' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( *******8 NAME 'preferredDeliveryMethod' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.919 NAME 'mSMQQuota' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.327 NAME 'packageFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.382 NAME 'dnsRecord' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.755 NAME 'domainIdentifier' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.872 NAME 'fRSControlInboundBacklog' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.928 NAME 'mSMQOutRoutingServers' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.768 NAME 'aCSEnableRSVPMessageLogging' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.585 NAME 'meetingIsEncrypted' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.34 NAME 'rangeLower' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******361 NAME 'mS-DS-ConsistencyChildCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2192 NAME 'msDS-EgressClaimsTransformationPolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2292 NAME 'msDS-ComputerAuthNPolicyBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.843 NAME 'lDAPAdminLimits' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******847 NAME 'msDS-TombstoneQuotaFactor' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******355 NAME 'queryFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'postalAddress' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.307 NAME 'options' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.74 NAME 'dSASignature' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.380 NAME 'dnsSecureSecondaries' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.634 NAME 'privilegeDisplayName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.598 NAME 'dmdName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******399 NAME 'mS-SQL-LastDiagnosticDate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2280 NAME 'msDS-ComputerAllowedToAuthenticateTo' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.350 NAME 'addressType' SYNTAX '1.2.840.113556.1.4.905' SINGLE-VALUE )",
            "( 1.2.840.113556.********.38 NAME 'msDFSR-CommonStagingPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.672 NAME 'categories' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******675 NAME 'msPKI-RA-Application-Policies' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******244 NAME 'addressBookRoots' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.336 NAME 'volTableGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.65 NAME 'logonWorkstation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2153 NAME 'msAuthz-ResourceCondition' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.34 NAME 'msDFSR-DefaultCompressionExclusionFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.756 NAME 'aCSTimeOfDay' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2276 NAME 'msDS-SyncServerUrl' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.710 NAME 'superScopes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.210 NAME 'proxyAddresses' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.348 NAME 'msSFU30NetgroupHostAtDomain' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******306 NAME 'dNSProperty' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.141 NAME 'department' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.871 NAME 'fRSControlDataCreation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.253 NAME 'cOMOtherProgId' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******337 NAME 'mSMQUserSid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 0.9.2342.********.100.1.14 NAME 'documentAuthor' SYNTAX '*******.4.1.1466.************' )",
            "( ******** NAME 'cACertificate' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.698 NAME 'dhcpUniqueKey' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******980 NAME 'msTSRemoteControl' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.9 NAME 'host' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2081 NAME 'msSPP-CSVLKSkuId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.483 NAME 'fRSFileFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2168 NAME 'msDS-IsPrimaryComputerFor' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.74 NAME 'maxPwdAge' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******374 NAME 'mS-SQL-NamedPipe' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******972 NAME 'msDS-FailedInteractiveLogonCount' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******649 NAME 'msWMI-TargetType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.668 NAME 'domainCAs' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2021 NAME 'msDS-PSOApplied' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.25 NAME 'countryCode' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******60 NAME 'lmPwdHistory' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.275 NAME 'printKeepPrintedJobs' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2014 NAME 'msDS-PasswordHistoryLength' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******836 NAME 'msDS-hasMasterNCs' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******813 NAME 'msDS-OperationsForAzRoleBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.212 NAME 'dSHeuristics' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.877 NAME 'fRSPartnerAuthLevel' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.13 NAME 'displayName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.269 NAME 'linkTrackSecret' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******239 NAME 'mSMQDependentClientService' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.238 NAME 'printMaxResolutionSupported' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.325 NAME 'perMsgDialogDisplayTable' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.819 NAME 'bridgeheadTransportList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.540 NAME 'initialAuthOutgoing' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.523 NAME 'proxyGenerationEnabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.760 NAME 'aCSAggregateTokenRatePerUser' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.381 NAME 'dnsNotifySecondaries' SYNTAX '*******.4.1.1466.************' )",
            "( *******1 NAME 'telexNumber' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******17 NAME 'rpcNsPriority' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.300 NAME 'msSFU30SearchContainer' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.960 NAME 'mSMQNt4Stub' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.844 NAME 'lDAPIPDenyList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.918 NAME 'mSMQJournal' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.********.343 NAME 'msSFU30MaxUidNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******442 NAME 'msDS-Cached-Membership-Time-Stamp' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******458 NAME 'msDS-Auxiliary-Classes' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.821 NAME 'siteList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******782 NAME 'msDS-KeyVersionNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( ******** NAME 'uniqueMember' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******797 NAME 'msDS-AzScriptTimeout' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******812 NAME 'msDS-OperationsForAzRole' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.809 NAME 'remoteStorageGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.231 NAME 'priority' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.37 NAME 'msDFSR-Options2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2083 NAME 'msSPP-InstallationId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'attributeCertificateAttribute' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.302 NAME 'msSFU30FieldSeparator' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.532 NAME 'superiorDNSRoot' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.822 NAME 'siteLinkList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******366 NAME 'mS-SQL-Location' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.94 NAME 'ntPwdHistory' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355******* NAME 'name' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******629 NAME 'msWMI-IntMax' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******18 NAME 'rpcNsProfileEntry' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2049 NAME 'msDS-BridgeHeadServersUsed' SYNTAX '1.2.840.113556.1.4.903' )",
            "( 1.2.840.11355*******969 NAME 'samDomainUpdates' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.889 NAME 'additionalTrustedServiceNames' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.77 NAME 'maxTicketAge' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******661 NAME 'msDS-NC-Replica-Locations' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******783 NAME 'msDS-ExecuteScriptPassword' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.49 NAME 'mAPIID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.9 NAME 'msDFSR-Enabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.250 NAME 'cOMUniqueLIBID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'postOfficeBox' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2067 NAME 'msDS-LastKnownRDN' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******344 NAME 'dSUIAdminMaximum' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******153 NAME 'msRADIUSFramedIPAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******799 NAME 'msDS-AzScopeName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2013 NAME 'msDS-MinimumPasswordLength' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******14 NAME 'rpcNsGroup' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******46 NAME 'objectSid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.6 NAME 'msDFSR-StagingSizeInMb' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.365 NAME 'operatingSystemServicePack' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'objectClasses' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******698 NAME 'msTAPI-uid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.256 NAME 'streetAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******191 NAME 'msRASSavedFramedRoute' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******965 NAME 'msFVE-RecoveryGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2166 NAME 'msDS-GenerationId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******359 NAME 'otherWellKnownObjects' SYNTAX '1.2.840.113556.1.4.903' )",
            "( 1.2.840.11355*******940 NAME 'msDS-RevealedList' SYNTAX '1.2.840.113556.1.4.904' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2282 NAME 'msDS-ServiceAllowedToAuthenticateTo' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.379 NAME 'dnsAllowXFR' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.628 NAME 'ipsecNegotiationPolicyReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******976 NAME 'msTSProfilePath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2076 NAME 'msPKI-Enrollment-Servers' SYNTAX '*******.4.1.1466.************' )",
            "( ******** NAME 'deltaRevocationList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.18 NAME 'otherTelephone' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2077 NAME 'msPKI-Site-Name' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******316 NAME 'aCSMinimumLatency' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2157 NAME 'msDS-ClaimSource' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******970 NAME 'msDS-LastSuccessfulInteractiveLogonTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.280 NAME 'printMinYExtent' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.415 NAME 'operatingSystemHotfix' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.306 NAME 'msSFU30MapFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.610 NAME 'classDisplayName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******381 NAME 'mS-SQL-LastUpdatedDate' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******957 NAME 'msDS-AuthenticatedToAccountlist' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******825 NAME 'msDS-AzMinorVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2198 NAME 'msDS-ManagedPasswordPreviousId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2068 NAME 'msDS-DeletedObjectLifetime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2095 NAME 'msDS-IsUsedAsResourceSecurityAttribute' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.786 NAME 'mailAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.373 NAME 'rIDUsedPool' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.19 NAME 'msDFSR-RdcEnabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.44 NAME 'homeDirectory' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.538 NAME 'prefixMap' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2034 NAME 'msDFS-LastModifiedv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2155 NAME 'msAuthz-MemberRulesInCentralAccessPolicy' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.947 NAME 'mSMQSignCertificates' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.714 NAME 'dhcpOptions' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2060 NAME 'msDS-LocalEffectiveRecycleTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.675 NAME 'catalogs' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******34 NAME 'trustPosixOffset' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******404 NAME 'mS-SQL-AllowImmediateUpdatingSubscription' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2047 NAME 'globalAddressList2' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.135 NAME 'cost' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******331 NAME 'pKIExpirationPeriod' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.45 NAME 'organizationalStatus' SYNTAX '*******.4.1.1466.************' )",
            "( ******** NAME 'businessCategory' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.4 NAME 'msDFSR-RootSizeInMb' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.869 NAME 'frsComputerReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******893 NAME 'msPKIDPAPIMasterKeys' SYNTAX '1.2.840.113556.1.4.903' )",
            "( 1.2.840.11355*******430 NAME 'msPKI-Enrollment-Flag' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.45 NAME 'homeDrive' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2085 NAME 'msSPP-OnlineLicense' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******96 NAME 'systemMayContain' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.90 NAME 'unicodePwd' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.763 NAME 'aCSTotalNoOfFlows' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******806 NAME 'msDS-MembersForAzRole' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.873 NAME 'fRSControlOutboundBacklog' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.89 NAME 'nTGroupMembers' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.815 NAME 'canUpgradeScript' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.96 NAME 'pwdLastSet' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.228 NAME 'portName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******821 NAME 'msieee80211-Data' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.720 NAME 'dhcpUpdateTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( *******3 NAME 'roleOccupant' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******818 NAME 'msDS-AzTaskIsRoleDefinition' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( *******.*******6 NAME 'ipServiceProtocol' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.488 NAME 'fRSStagingPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.25 NAME 'dc' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.502 NAME 'timeVolChange' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.303 NAME 'notificationList' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.952 NAME 'mSMQMigrated' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2290 NAME 'msDS-UserAuthNPolicyBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.53 NAME 'lastSetTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.894 NAME 'gPCFileSysPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******2 NAME 'teletexTerminalIdentifier' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.471 NAME 'schemaVersion' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.91 NAME 'repsFrom' SYNTAX 'OctetString' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.5 NAME 'msDFSR-StagingPath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******.*******5 NAME 'ipServicePort' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.781 NAME 'lastKnownParent' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'initials' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.901 NAME 'aCSMaxNoOfAccountFiles' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******928 NAME 'msDS-RevealOnDemandGroup' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******689 NAME 'msDS-Non-Security-Group-Extra-Classes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.344 NAME 'groupsToIgnore' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.896 NAME 'uSNSource' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.964 NAME 'mSMQNt4Flags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2102 NAME 'msDS-ClaimSharesPossibleValuesWithBL' SYNTAX '*******.4.1.1466.************' )",
            "( *******9 NAME 'presentationAddress' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2051 NAME 'msDS-OIDToGroupLink' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.369 NAME 'fSMORoleOwner' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******954 NAME 'ms-net-ieee-8023-GP-PolicyGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.648 NAME 'primaryTelexNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2283 NAME 'msDS-ServiceAllowedToAuthenticateFrom' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'title' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.1 NAME 'uid' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******247 NAME 'interSiteTopologyRenew' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******697 NAME 'msDS-Settings' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.247 NAME 'printAttributes' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2188 NAME 'msDS-ValueTypeReferenceBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2133 NAME 'msDNS-MaintainTrustAnchor' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.324 NAME 'msSFU30KeyValues' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******378 NAME 'mS-SQL-AppleTalk' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******663 NAME 'msDS-Replication-Notify-First-DSA-Delay' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******21 NAME 'securityIdentifier' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.748 NAME 'attributeDisplayNames' SYNTAX '*******.4.1.1466.************' )",
            "( 2.16.840.1.113730.3.1.35 NAME 'thumbnailPhoto' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2082 NAME 'msSPP-KMSIds' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.758 NAME 'aCSMaxTokenRatePerFlow' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.121 NAME 'uSNLastObjRem' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.875 NAME 'fRSMemberReference' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******349 NAME 'gPCUserExtensionNames' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******38 NAME 'userParameters' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******6 NAME 'userCertificate' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.102 NAME 'msDFSR-MemberReferenceBL' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.131 NAME 'co' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******* NAME 'cn' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.936 NAME 'mSMQEncryptKey' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.226 NAME 'adminDescription' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( *******4 NAME 'seeAlso' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.444 NAME 'msExchAssistantName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.667 NAME 'syncWithSID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******998 NAME 'msFVE-VolumeGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2107 NAME 'msTPM-SrkPubThumbprint' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.81 NAME 'info' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******686 NAME 'msWMI-ScopeGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******51 NAME 'oEMInformation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.935 NAME 'mSMQOSType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.374 NAME 'rIDNextRID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2039 NAME 'msDFS-LinkPathv2' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******41 NAME 'versionNumber' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.505 NAME 'oMTGuid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.88 NAME 'nextRid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2037 NAME 'msDFS-Propertiesv2' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******994 NAME 'msTSLicenseVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 2.16.840.1.113730.3.140 NAME 'userSMIMECertificate' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******985 NAME 'msTSBrokenConnectionAction' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.281 NAME 'printStaplingSupported' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.********.17 NAME 'msDFSR-Options' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.485 NAME 'fRSUpdateTimeout' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******967 NAME 'msDS-NC-RO-Replica-Locations' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******819 NAME 'msDS-AzApplicationData' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.347 NAME 'msSFU30PosixMemberOf' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******425 NAME 'msCOM-UserLink' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.********.24 NAME 'msDFSR-DfsLinkTarget' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.512 NAME 'siteObject' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.584 NAME 'meetingRating' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******794 NAME 'msDS-NonMembersBL' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.776 NAME 'aCSDSBMPriority' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.520 NAME 'machinePasswordChangeInterval' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.669 NAME 'rIDSetReferences' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.941 NAME 'mSMQLongLived' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******241 NAME 'netbootMirrorDataFile' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.305 NAME 'msSFU30ResultAttributes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2236 NAME 'msds-memberOfTransitive' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******427 NAME 'msCOM-DefaultPartitionLink' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.519 NAME 'lastBackupRestorationTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.337 NAME 'currMachineId' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.32 NAME 'attributeSyntax' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.362 NAME 'siteGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.823 NAME 'certificateTemplates' SYNTAX '*******.4.1.1466.************' )",
            "( 2.16.840.1.113730.3.1.39 NAME 'preferredLanguage' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.352 NAME 'msSFU30CryptMethod' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******946 NAME 'msDS-PhoneticDisplayName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 2.5.4.14 NAME 'searchGuide' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2270 NAME 'msDS-IsManaged' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.581 NAME 'meetingScope' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.673 NAME 'retiredReplDSASignatures' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.855 NAME 'netbootNewMachineNamingPolicy' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******245 NAME 'globalAddressList' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.227 NAME 'extensionName' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.884 NAME 'msRRASAttribute' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.********.349 NAME 'msSFU30NetgroupUserAtDomain' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.680 NAME 'queryPoint' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.888 NAME 'iPSECNegotiationPolicyAction' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.29 NAME 'msDFSR-CachePolicy' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.299 NAME 'printMediaSupported' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.503 NAME 'timeRefresh' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******1 NAME 'authenticationOptions' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******98 NAME 'systemAuxiliaryClass' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.857 NAME 'netbootIntelliMirrorOSes' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******406 NAME 'mS-SQL-AllowSnapshotFilesFTPDownloading' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.11355*******333 NAME 'pKIExtendedKeyUsage' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2019 NAME 'msDS-LockoutThreshold' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******844 NAME 'msDS-QuotaTrustee' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.350 NAME 'msSFU30IsValidContainer' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.359 NAME 'netbootGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******719 NAME 'msDS-DnsRootAlias' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.909 NAME 'extendedAttributeInfo' SYNTAX '*******.4.1.1466.************' NO-USER-MODIFICATION )",
            "( *******.*******0 NAME 'shadowExpire' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******248 NAME 'interSiteTopologyFailover' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2275 NAME 'msDS-CloudIsEnabled' SYNTAX '*******.4.1.1466.***********' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.887 NAME 'iPSECNegotiationPolicyType' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2059 NAME 'msDS-LocalEffectiveDeletionTime' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.587 NAME 'meetingStartTime' SYNTAX '*******.4.1.1466.************' )",
            "( ******** NAME 'postalCode' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.2.445 NAME 'originalDisplayTable' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******990 NAME 'msTSInitialProgram' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.662 NAME 'lockoutTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.21 NAME 'secretary' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.95 NAME 'pwdHistoryLength' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.759 NAME 'aCSMaxPeakBandwidthPerFlow' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.753 NAME 'nameServiceFlags' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.694 NAME 'previousParentCA' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******42 NAME 'winsockAddresses' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2075 NAME 'msTSSecondaryDesktops' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.2105 NAME 'msSPP-CSVLKPid' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.49 NAME 'badPasswordTime' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2184 NAME 'msDS-GeoCoordinatesLatitude' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2079 NAME 'msDS-RequiredForestBehaviorVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******955 NAME 'ms-net-ieee-8023-GP-PolicyData' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.237 NAME 'printBinNames' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******382 NAME 'mS-SQL-InformationURL' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.********.13 NAME 'msDFSR-DirectoryFilter' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******48 NAME 'schemaIDGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.4.2189 NAME 'msDS-TransformationRules' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 2.5.4.10 NAME 'o' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.504 NAME 'seqNotification' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 2.5.4.7 NAME 'l' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.57 NAME 'defaultLocalPolicyObject' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******801 NAME 'msDS-AzBizRule' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.948 NAME 'mSMQDigests' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.2.327 NAME 'helpFileName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.421 NAME 'domainWidePolicy' SYNTAX '*******.4.1.1466.************' )",
            "( 2.5.4.6 NAME 'c' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2139 NAME 'msDNS-DNSKEYRecordSetTTL' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.11 NAME 'documentIdentifier' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.609 NAME 'sIDHistory' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******711 NAME 'msDS-SDReferenceDomain' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******367 NAME 'mS-SQL-Memory' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.715 NAME 'dhcpClasses' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******305 NAME 'moveTreeState' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.113556.1.4.757 NAME 'aCSDirection' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.631 NAME 'printPagesPerMinute' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******45 NAME 'revision' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.646 NAME 'otherFacsimileTelephoneNumber' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******798 NAME 'msDS-AzApplicationName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.366 NAME 'rpcNsAnnotation' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2190 NAME 'msDS-TransformationRulesCompiled' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.11355*******636 NAME 'msWMI-StringDefault' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.813 NAME 'upgradeProductCode' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******951 NAME 'ms-net-ieee-80211-GP-PolicyGUID' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2098 NAME 'msDS-ClaimValueType' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.194 NAME 'adminDisplayName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.879 NAME 'fRSServiceCommandStatus' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.361 NAME 'netbootMachineFilePath' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.67 NAME 'lSAModifiedCount' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.920 NAME 'mSMQBasePriority' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2277 NAME 'msDS-UserAllowedToAuthenticateTo' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2056 NAME 'msDS-HostServiceAccount' SYNTAX '*******.4.1.1466.************' )",
            "( 1.2.840.11355*******943 NAME 'msDS-PhoneticLastName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.2055 NAME 'msDS-USNLastSyncSuccess' SYNTAX '1.2.840.113556.1.4.906' SINGLE-VALUE )",
            "( 1.2.840.11355*******01 NAME 'privateKey' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( ******** NAME 'givenName' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.685 NAME 'parentCACertificateChain' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.11355*******924 NAME 'msDS-RevealedUsers' SYNTAX '1.2.840.113556.1.4.903' NO-USER-MODIFICATION )",
            "( 1.2.840.113556.1.2.76 NAME 'objectVersion' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )",
            "( 1.2.840.113556.1.4.856 NAME 'netbootNewMachineOU' SYNTAX '*******.4.1.1466.************' SINGLE-VALUE )"
        ],
        "cn": [
            "Aggregate"
        ],
        "dITContentRules": [
            "( 1.2.840.113556.********.6 NAME 'msDFSR-Content' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******** NAME 'device' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MAY (uid $ manager $ ipHostNumber $ macAddress $ bootParameter $ bootFile ))",
            "( 1.2.840.113556.1.5.205 NAME 'msWMI-IntRangeParam' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.5 NAME 'samServer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.196 NAME 'msPKI-Enterprise-Oid' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.7000.53 NAME 'crossRefContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******* NAME 'ipNetwork' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'organizationalUnit' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.152 NAME 'intellimirrorGroup' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.253 NAME 'msFVE-RecoveryInformation' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.262 NAME 'msImaging-PSPs' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.251 NAME 'ms-net-ieee-80211-GroupPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.138 NAME 'aCSSubnet' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.43 NAME 'fTDfs' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.27 NAME 'rpcEntry')",
            "( 1.2.840.113556.1.5.85 NAME 'dnsZone' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.4.2163 NAME 'msAuthz-CentralAccessRule' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.194 NAME 'msCOM-PartitionSet' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.242 NAME 'msDS-QuotaContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.281 NAME 'msDS-ClaimsTransformationPolicies' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.146 NAME 'remoteStorageServicePoint' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.2 NAME 'samDomainBase')",
            "( 1.2.840.113556.1.5.132 NAME 'dHCPClass' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.283 NAME 'msDS-CloudExtensions')",
            "( 1.2.840.113556.1.5.89 NAME 'nTFRSSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.24 NAME 'remoteMailRecipient' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MUST (cn ) MAY (telephoneNumber $ userCertificate $ info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ showInAddressBook $ userCert $ legacyExchangeDN $ msDS-PhoneticDisplayName $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ userSMIMECertificate $ textEncodedORAddress $ secretary $ labeledURI ))",
            "( 1.2.840.113556.1.5.221 NAME 'msTAPI-RtConference' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.201 NAME 'msWMI-SimplePolicyTemplate' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.212 NAME 'msSFU30NetId' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.49 NAME 'packageRegistration' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.139 NAME 'lostAndFound' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.14 NAME 'connectionPoint')",
            "( 1.2.840.113556.1.5.6 NAME 'securityPrincipal')",
            "( 1.2.840.113556.1.5.147 NAME 'siteLink' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.255 NAME 'msDS-PasswordSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.4.2162 NAME 'msAuthz-CentralAccessRules' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.30 NAME 'serviceInstance' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.156 NAME 'rRASAdministrationDictionary' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.4.2164 NAME 'msAuthz-CentralAccessPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 2.16.840.1.113730.3.2.2 NAME 'inetOrgPerson' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MUST (objectSid $ sAMAccountName ) MAY (info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ securityIdentifier $ supplementalCredentials $ rid $ sAMAccountType $ sIDHistory $ showInAddressBook $ userCert $ legacyExchangeDN $ altSecurityIdentities $ tokenGroups $ tokenGroupsNoGCAcceptable $ accountNameHistory $ tokenGroupsGlobalAndUniversal $ msDS-KeyVersionNumber $ unixUserPassword $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ msDS-cloudExtensionAttribute1 $ msDS-cloudExtensionAttribute2 $ msDS-cloudExtensionAttribute3 $ msDS-cloudExtensionAttribute4 $ msDS-cloudExtensionAttribute5 $ msDS-cloudExtensionAttribute6 $ msDS-cloudExtensionAttribute7 $ msDS-cloudExtensionAttribute8 $ msDS-cloudExtensionAttribute9 $ msDS-cloudExtensionAttribute10 $ msDS-cloudExtensionAttribute11 $ msDS-cloudExtensionAttribute12 $ msDS-cloudExtensionAttribute13 $ msDS-cloudExtensionAttribute14 $ msDS-cloudExtensionAttribute15 $ msDS-cloudExtensionAttribute16 $ msDS-cloudExtensionAttribute17 $ msDS-cloudExtensionAttribute18 $ msDS-cloudExtensionAttribute19 $ msDS-cloudExtensionAttribute20 $ textEncodedORAddress $ uidNumber $ gidNumber $ gecos $ unixHomeDirectory $ loginShell $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag ))",
            "( 1.2.840.113556.1.5.52 NAME 'fileLinkTracking' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.18 NAME 'domainPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.216 NAME 'msSFU30NetworkUser' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 0.9.2342.********.100.4.19 NAME 'simpleSecurityObject')",
            "( 1.2.840.113556.1.5.177 NAME 'pKICertificateTemplate' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.293 NAME 'msDS-AuthNPolicies' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.2 NAME 'msDFSR-Subscriber' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.31 NAME 'site' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.222 NAME 'msTAPI-RtPerson' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.68 NAME 'applicationSiteSettings')",
            "( 1.2.840.113556.1.3.14 NAME 'attributeSchema' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.267 NAME 'msSPP-ActivationObject' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.220 NAME 'msDS-App-Configuration' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.3.23 NAME 'container' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.10 NAME 'msDFSR-Connection' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.207 NAME 'msWMI-UintRangeParam' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.23 NAME 'printQueue' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.260 NAME 'msDFS-DeletedLinkv2' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.140 NAME 'interSiteTransportContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.130 NAME 'indexServerCatalog' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.98 NAME 'ipsecPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'top')",
            "( 1.2.840.113556.1.5.36 NAME 'volume' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.236 NAME 'msDS-AzOperation' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'groupOfNames' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.12 NAME 'configuration' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.78 NAME 'licensingSiteSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.69 NAME 'nTDSSiteSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.269 NAME 'msDS-ClaimTypePropertyBase')",
            "( 1.2.840.113556.1.5.273 NAME 'msDS-ResourceProperty' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.239 NAME 'msDS-AzRole' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******** NAME 'bootableDevice')",
            "( 1.2.840.113556.1.5.294 NAME 'msDS-AuthNPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.86 NAME 'dnsNode' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.210 NAME 'msWMI-StringSetParam' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.264 NAME 'msDS-ManagedServiceAccount' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MUST (objectSid $ sAMAccountName ) MAY (info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ securityIdentifier $ supplementalCredentials $ rid $ sAMAccountType $ sIDHistory $ showInAddressBook $ userCert $ legacyExchangeDN $ altSecurityIdentities $ tokenGroups $ tokenGroupsNoGCAcceptable $ accountNameHistory $ tokenGroupsGlobalAndUniversal $ msDS-KeyVersionNumber $ unixUserPassword $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ msDS-cloudExtensionAttribute1 $ msDS-cloudExtensionAttribute2 $ msDS-cloudExtensionAttribute3 $ msDS-cloudExtensionAttribute4 $ msDS-cloudExtensionAttribute5 $ msDS-cloudExtensionAttribute6 $ msDS-cloudExtensionAttribute7 $ msDS-cloudExtensionAttribute8 $ msDS-cloudExtensionAttribute9 $ msDS-cloudExtensionAttribute10 $ msDS-cloudExtensionAttribute11 $ msDS-cloudExtensionAttribute12 $ msDS-cloudExtensionAttribute13 $ msDS-cloudExtensionAttribute14 $ msDS-cloudExtensionAttribute15 $ msDS-cloudExtensionAttribute16 $ msDS-cloudExtensionAttribute17 $ msDS-cloudExtensionAttribute18 $ msDS-cloudExtensionAttribute19 $ msDS-cloudExtensionAttribute20 $ textEncodedORAddress $ uidNumber $ gidNumber $ gecos $ unixHomeDirectory $ loginShell $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ ipHostNumber ))",
            "( 1.2.840.113556.1.5.15 NAME 'contact' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MAY (userCertificate $ info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ showInAddressBook $ userCert $ legacyExchangeDN $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ userSMIMECertificate $ textEncodedORAddress $ secretary $ labeledURI ))",
            "( *******.******* NAME 'posixAccount')",
            "( 1.2.840.113556.1.5.266 NAME 'msSPP-ActivationObjectsContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.217 NAME 'msWMI-ObjectEncoding' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.33 NAME 'storage' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.67 NAME 'domainDNS' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MAY (cACertificate $ builtinCreationTime $ builtinModifiedCount $ creationTime $ domainPolicyObject $ forceLogoff $ defaultLocalPolicyObject $ lockoutDuration $ lockOutObservationWindow $ lSACreationTime $ lSAModifiedCount $ lockoutThreshold $ maxPwdAge $ minPwdAge $ minPwdLength $ modifiedCountAtLastProm $ nETBIOSName $ nextRid $ pwdProperties $ pwdHistoryLength $ privateKey $ replicaSource $ objectSid $ oEMInformation $ serverState $ uASCompat $ serverRole $ domainReplica $ modifiedCount $ controlAccessRights $ auditingPolicy $ eFSPolicy $ desktopProfile $ nTMixedDomain $ rIDManagerReference $ treeName $ pekList $ pekKeyChangeInterval $ gPLink $ gPOptions $ ms-DS-MachineAccountQuota $ msDS-LogonTimeSyncInterval $ msDS-PerUserTrustQuota $ msDS-AllUsersTrustQuota $ msDS-PerUserTrustTombstonesQuota ))",
            "( 1.2.840.113556.1.5.92 NAME 'linkTrackVolEntry' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******** NAME 'ieee802Device')",
            "( 0.9.2342.********.100.4.17 NAME 'domainRelatedObject')",
            "( 1.2.840.113556.1.5.235 NAME 'msDS-AzApplication' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.107 NAME 'sitesContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.263 NAME 'msImaging-PostScanProcess' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.240 NAME 'msieee80211-Policy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.95 NAME 'subnetContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 0.9.2342.********.100.4.6 NAME 'document' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'person' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.274 NAME 'msDS-ResourcePropertyList' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.270 NAME 'msDS-ClaimTypes' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******* NAME 'shadowAccount')",
            "( 1.2.840.113556.1.5.179 NAME 'mSMQMigratedUser' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.185 NAME 'mS-SQL-OLAPServer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.4.1.1466.101.119.2 NAME 'dynamicObject')",
            "( 1.2.840.113556.1.5.155 NAME 'nTFRSSubscriber' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.129 NAME 'rIDSet' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.3.58 NAME 'addressTemplate' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.154 NAME 'nTFRSSubscriptions' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.7000.47 NAME 'nTDSDSA' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.175 NAME 'infrastructureUpdate' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.215 NAME 'msSFU30DomainInfo' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.213 NAME 'msWMI-Som' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.82 NAME 'rpcProfile' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.164 NAME 'mSMQSiteLink' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.184 NAME 'mS-SQL-SQLServer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.106 NAME 'queryPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.162 NAME 'mSMQConfiguration' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.257 NAME 'msDFS-NamespaceAnchor' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.7 NAME 'msDFSR-ContentSet' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.276 NAME 'msTPM-InformationObjectsContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.209 NAME 'msWMI-RealRangeParam' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'organizationalPerson' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.176 NAME 'msExchConfigurationContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.278 NAME 'msKds-ProvRootKey' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.238 NAME 'msDS-AzTask' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.282 NAME 'msDS-GroupManagedServiceAccount' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MUST (objectSid $ sAMAccountName ) MAY (info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ securityIdentifier $ supplementalCredentials $ rid $ sAMAccountType $ sIDHistory $ showInAddressBook $ userCert $ legacyExchangeDN $ altSecurityIdentities $ tokenGroups $ tokenGroupsNoGCAcceptable $ accountNameHistory $ tokenGroupsGlobalAndUniversal $ msDS-KeyVersionNumber $ unixUserPassword $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ msDS-cloudExtensionAttribute1 $ msDS-cloudExtensionAttribute2 $ msDS-cloudExtensionAttribute3 $ msDS-cloudExtensionAttribute4 $ msDS-cloudExtensionAttribute5 $ msDS-cloudExtensionAttribute6 $ msDS-cloudExtensionAttribute7 $ msDS-cloudExtensionAttribute8 $ msDS-cloudExtensionAttribute9 $ msDS-cloudExtensionAttribute10 $ msDS-cloudExtensionAttribute11 $ msDS-cloudExtensionAttribute12 $ msDS-cloudExtensionAttribute13 $ msDS-cloudExtensionAttribute14 $ msDS-cloudExtensionAttribute15 $ msDS-cloudExtensionAttribute16 $ msDS-cloudExtensionAttribute17 $ msDS-cloudExtensionAttribute18 $ msDS-cloudExtensionAttribute19 $ msDS-cloudExtensionAttribute20 $ textEncodedORAddress $ uidNumber $ gidNumber $ gecos $ unixHomeDirectory $ loginShell $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ ipHostNumber ))",
            "( *******.******* NAME 'nisMap' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.*******0 NAME 'nisObject' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.277 NAME 'msKds-ProvServerConfiguration' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.217 NAME 'msSFU30NISMapConfig' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.7000.48 NAME 'serversContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.90 NAME 'linkTrackVolumeTable' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.188 NAME 'mS-SQL-SQLDatabase' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.211 NAME 'msWMI-PolicyType' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.183 NAME 'dSUISettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.157 NAME 'groupPolicyContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.3 NAME 'samDomain' MAY (forceLogoff $ objectSid $ oEMInformation $ serverState $ uASCompat $ serverRole $ domainReplica $ modifiedCount ))",
            "( 1.2.840.113556.1.5.234 NAME 'msDS-AzAdminManager' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.214 NAME 'msWMI-Rule' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.254 NAME 'nTDSDSARO' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.286 NAME 'msDS-Device' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.34 NAME 'trustedDomain' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 0.9.2342.********.100.4.7 NAME 'room' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'organization' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.272 NAME 'msDS-ClaimType' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******* NAME 'ipService' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******* NAME 'ipProtocol' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.80 NAME 'rpcGroup' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.17 NAME 'server' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.28 NAME 'secret' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.163 NAME 'mSMQEnterpriseSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.202 NAME 'msWMI-MergeablePolicyTemplate' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.195 NAME 'msPKI-Key-Recovery-Agent' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MUST (objectSid $ sAMAccountName ) MAY (info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ securityIdentifier $ supplementalCredentials $ rid $ sAMAccountType $ sIDHistory $ showInAddressBook $ userCert $ legacyExchangeDN $ altSecurityIdentities $ tokenGroups $ tokenGroupsNoGCAcceptable $ accountNameHistory $ tokenGroupsGlobalAndUniversal $ msDS-KeyVersionNumber $ unixUserPassword $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ msDS-cloudExtensionAttribute1 $ msDS-cloudExtensionAttribute2 $ msDS-cloudExtensionAttribute3 $ msDS-cloudExtensionAttribute4 $ msDS-cloudExtensionAttribute5 $ msDS-cloudExtensionAttribute6 $ msDS-cloudExtensionAttribute7 $ msDS-cloudExtensionAttribute8 $ msDS-cloudExtensionAttribute9 $ msDS-cloudExtensionAttribute10 $ msDS-cloudExtensionAttribute11 $ msDS-cloudExtensionAttribute12 $ msDS-cloudExtensionAttribute13 $ msDS-cloudExtensionAttribute14 $ msDS-cloudExtensionAttribute15 $ msDS-cloudExtensionAttribute16 $ msDS-cloudExtensionAttribute17 $ msDS-cloudExtensionAttribute18 $ msDS-cloudExtensionAttribute19 $ msDS-cloudExtensionAttribute20 $ textEncodedORAddress $ uidNumber $ gidNumber $ gecos $ unixHomeDirectory $ loginShell $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag ))",
            "( 0.9.2342.********.100.4.18 NAME 'friendlyCountry' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.258 NAME 'msDFS-Namespacev2' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.96 NAME 'subnet' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.216 NAME 'applicationVersion' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******** NAME 'residentialPerson' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******** NAME 'cRLDistributionPoint' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.137 NAME 'aCSPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.77 NAME 'controlAccessRight' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.219 NAME 'msMQ-Group' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.8 NAME 'group' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MUST (cn $ objectSid $ sAMAccountName ) MAY (telephoneNumber $ userPassword $ userCertificate $ info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ securityIdentifier $ supplementalCredentials $ rid $ sAMAccountType $ sIDHistory $ showInAddressBook $ userCert $ legacyExchangeDN $ altSecurityIdentities $ tokenGroups $ tokenGroupsNoGCAcceptable $ accountNameHistory $ tokenGroupsGlobalAndUniversal $ msDS-KeyVersionNumber $ unixUserPassword $ msDS-PhoneticDisplayName $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ userSMIMECertificate $ textEncodedORAddress $ secretary $ labeledURI $ gidNumber $ memberUid ))",
            "( 1.2.840.113556.******** NAME 'msPrint-ConnectionPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.3.11 NAME 'crossRef' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.9 NAME 'msDFSR-Member' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.3.59 NAME 'displayTemplate' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.3.13 NAME 'classSchema' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.200 NAME 'msWMI-PolicyTemplate' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.165 NAME 'mSMQSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******* NAME 'oncRpc' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.126 NAME 'serviceConnectionPoint' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.4 NAME 'builtinDomain' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MAY (creationTime $ forceLogoff $ lockoutDuration $ lockOutObservationWindow $ lockoutThreshold $ maxPwdAge $ minPwdAge $ minPwdLength $ modifiedCountAtLastProm $ nextRid $ pwdProperties $ pwdHistoryLength $ objectSid $ oEMInformation $ serverState $ uASCompat $ serverRole $ domainReplica $ modifiedCount ))",
            "( 1.2.840.113556.1.5.241 NAME 'msDS-AppData' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.73 NAME 'rpcServerElement' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.150 NAME 'rRASAdministrationConnectionPoint' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.191 NAME 'aCSResourceLimits' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'locality' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******* NAME 'ipHost')",
            "( 1.2.840.113556.1.5.275 NAME 'msTPM-InformationObject' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.289 NAME 'msDS-DeviceContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.4.2129 NAME 'msDNS-ServerSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.76 NAME 'foreignSecurityPrincipal' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.44 NAME 'classStore' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 0.9.2342.********.100.4.5 NAME 'account' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.26 NAME 'rpcProfileElement' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.215 NAME 'msWMI-WMIGPO' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.243 NAME 'msDS-QuotaControl' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.256 NAME 'msDS-PasswordSettingsContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.187 NAME 'mS-SQL-SQLPublication' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.9 NAME 'user' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MUST (objectSid $ sAMAccountName ) MAY (info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ securityIdentifier $ supplementalCredentials $ rid $ sAMAccountType $ sIDHistory $ showInAddressBook $ userCert $ legacyExchangeDN $ altSecurityIdentities $ tokenGroups $ tokenGroupsNoGCAcceptable $ accountNameHistory $ tokenGroupsGlobalAndUniversal $ msDS-KeyVersionNumber $ unixUserPassword $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ msDS-cloudExtensionAttribute1 $ msDS-cloudExtensionAttribute2 $ msDS-cloudExtensionAttribute3 $ msDS-cloudExtensionAttribute4 $ msDS-cloudExtensionAttribute5 $ msDS-cloudExtensionAttribute6 $ msDS-cloudExtensionAttribute7 $ msDS-cloudExtensionAttribute8 $ msDS-cloudExtensionAttribute9 $ msDS-cloudExtensionAttribute10 $ msDS-cloudExtensionAttribute11 $ msDS-cloudExtensionAttribute12 $ msDS-cloudExtensionAttribute13 $ msDS-cloudExtensionAttribute14 $ msDS-cloudExtensionAttribute15 $ msDS-cloudExtensionAttribute16 $ msDS-cloudExtensionAttribute17 $ msDS-cloudExtensionAttribute18 $ msDS-cloudExtensionAttribute19 $ msDS-cloudExtensionAttribute20 $ textEncodedORAddress $ uidNumber $ gidNumber $ gecos $ unixHomeDirectory $ loginShell $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag ))",
            "( 1.2.840.113556.1.5.259 NAME 'msDFS-Linkv2' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.141 NAME 'interSiteTransport' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.4 NAME 'msDFSR-GlobalSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.29 NAME 'serviceClass' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.189 NAME 'mS-SQL-OLAPDatabase' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******** NAME 'certificationAuthority' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.104 NAME 'meeting' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.287 NAME 'msDS-DeviceRegistrationServiceContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.71 NAME 'nTDSConnection' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.291 NAME 'msDS-AuthNPolicySilos' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.218 NAME 'msMQ-Custom-Recipient' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.72 NAME 'nTDSService' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.3.9 NAME 'dMD' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.280 NAME 'msDS-ClaimsTransformationPolicyType' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 0.9.2342.********.100.4.14 NAME 'rFC822LocalPart' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.190 NAME 'mS-SQL-OLAPCube' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.208 NAME 'msWMI-UintSetParam' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( *******.******* NAME 'posixGroup')",
            "( ******** NAME 'groupOfUniqueNames' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.252 NAME 'ms-net-ieee-8023-GroupPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.119 NAME 'ipsecNegotiationPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.292 NAME 'msDS-AuthNPolicySilo' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.121 NAME 'ipsecNFA' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.42 NAME 'dfsConfiguration' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 0.9.2342.********.100.4.9 NAME 'documentSeries' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.271 NAME 'msDS-ResourceProperties' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.91 NAME 'linkTrackObjectMoveTable' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.136 NAME 'rpcContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.83 NAME 'rIDManager' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.206 NAME 'msWMI-IntSetParam' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.5 NAME 'msDFSR-ReplicationGroup' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.125 NAME 'addressBookContainer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.7000.49 NAME 'applicationSettings')",
            "( 1.2.840.113556.1.5.265 NAME 'msDS-OptionalFeature' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.94 NAME 'serviceAdministrationPoint' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.102 NAME 'nTFRSReplicaSet' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.203 NAME 'msWMI-RangeParam' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.7000.56 NAME 'ipsecBase')",
            "( 1.2.840.113556.********.3 NAME 'msDFSR-Subscription' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.223 NAME 'msPKI-PrivateKeyRecoveryAgent' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.178 NAME 'pKIEnrollmentService' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.211 NAME 'msSFU30MailAliases' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.53 NAME 'typeLibrary' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.8 NAME 'msDFSR-Topology' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.237 NAME 'msDS-AzScope' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.74 NAME 'categoryRegistration' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.11 NAME 'comConnectionPoint' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.93 NAME 'linkTrackOMTEntry' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.10 NAME 'classRegistration' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.148 NAME 'siteLinkBridge' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.81 NAME 'rpcServer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.3.46 NAME 'mailRecipient')",
            "( 1.2.840.113556.1.5.1 NAME 'securityObject')",
            "( 1.2.840.113556.1.5.20 NAME 'leaf')",
            "( 1.2.840.113556.1.5.151 NAME 'intellimirrorSCP' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.********.1 NAME 'msDFSR-LocalSettings' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.186 NAME 'mS-SQL-SQLRepository' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'organizationalRole' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******** NAME 'subSchema' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.284 NAME 'msDS-DeviceRegistrationService' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.84 NAME 'displaySpecifier' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.212 NAME 'msWMI-ShadowObject' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.59 NAME 'fileLinkTrackingEntry' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.4.2161 NAME 'msAuthz-CentralAccessPolicies' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.161 NAME 'mSMQQueue' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.193 NAME 'msCOM-Partition' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.118 NAME 'ipsecFilter' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******* NAME 'country' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.97 NAME 'physicalLocation' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.3.30 NAME 'computer' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ) MUST (objectSid $ sAMAccountName ) MAY (info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ securityIdentifier $ supplementalCredentials $ rid $ sAMAccountType $ sIDHistory $ showInAddressBook $ userCert $ legacyExchangeDN $ altSecurityIdentities $ tokenGroups $ tokenGroupsNoGCAcceptable $ accountNameHistory $ tokenGroupsGlobalAndUniversal $ msDS-KeyVersionNumber $ unixUserPassword $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ msDS-cloudExtensionAttribute1 $ msDS-cloudExtensionAttribute2 $ msDS-cloudExtensionAttribute3 $ msDS-cloudExtensionAttribute4 $ msDS-cloudExtensionAttribute5 $ msDS-cloudExtensionAttribute6 $ msDS-cloudExtensionAttribute7 $ msDS-cloudExtensionAttribute8 $ msDS-cloudExtensionAttribute9 $ msDS-cloudExtensionAttribute10 $ msDS-cloudExtensionAttribute11 $ msDS-cloudExtensionAttribute12 $ msDS-cloudExtensionAttribute13 $ msDS-cloudExtensionAttribute14 $ msDS-cloudExtensionAttribute15 $ msDS-cloudExtensionAttribute16 $ msDS-cloudExtensionAttribute17 $ msDS-cloudExtensionAttribute18 $ msDS-cloudExtensionAttribute19 $ msDS-cloudExtensionAttribute20 $ textEncodedORAddress $ uidNumber $ gidNumber $ gecos $ unixHomeDirectory $ loginShell $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ ipHostNumber ))",
            "( *******.******* NAME 'nisNetgroup' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.153 NAME 'nTFRSMember' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******** NAME 'applicationEntity' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( ******** NAME 'applicationProcess' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.279 NAME 'msDS-ValueType' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.204 NAME 'msWMI-UnknownRangeParam' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.66 NAME 'domain')",
            "( ******** NAME 'dSA' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))",
            "( 1.2.840.113556.1.5.120 NAME 'ipsecISAKMPPolicy' AUX ( mailRecipient $ posixGroup $ ipHost $ samDomain $ dynamicObject $ shadowAccount $ domainRelatedObject $ ieee802Device $ posixAccount $ bootableDevice $ simpleSecurityObject $ securityPrincipal $ msDS-CloudExtensions $ samDomainBase ))"
        ],
        "dSCorePropagationData": [
            "**************.0Z"
        ],
        "distinguishedName": [
            "CN=Aggregate,CN=Schema,CN=Configuration,DC=FOREST,DC=LAB"
        ],
        "instanceType": [
            "4"
        ],
        "modifyTimeStamp": [
            "**************.0Z"
        ],
        "name": [
            "Aggregate"
        ],
        "objectCategory": [
            "CN=SubSchema,CN=Schema,CN=Configuration,DC=FOREST,DC=LAB"
        ],
        "objectClass": [
            "top",
            "subSchema"
        ],
        "objectClasses": [
            "( 1.2.840.113556.********.6 NAME 'msDFSR-Content' SUP top STRUCTURAL MAY (msDFSR-Extension $ msDFSR-Flags $ msDFSR-Options $ msDFSR-Options2 ) )",
            "( ******** NAME 'device' SUP top STRUCTURAL MUST (cn ) MAY (serialNumber $ l $ o $ ou $ owner $ seeAlso $ msSFU30Name $ msSFU30Aliases $ msSFU30NisDomain $ nisMapName ) )",
            "( 1.2.840.113556.1.5.205 NAME 'msWMI-IntRangeParam' SUP msWMI-RangeParam STRUCTURAL MUST (msWMI-IntDefault ) MAY (msWMI-IntMax $ msWMI-IntMin ) )",
            "( 1.2.840.113556.1.5.5 NAME 'samServer' SUP securityObject STRUCTURAL MAY (samDomainUpdates ) )",
            "( 1.2.840.113556.1.5.196 NAME 'msPKI-Enterprise-Oid' SUP top STRUCTURAL MAY (msPKI-Cert-Template-OID $ msPKI-OID-Attribute $ msPKI-OID-CPS $ msPKI-OID-User-Notice $ msPKI-OIDLocalizedName $ msDS-OIDToGroupLink ) )",
            "( 1.2.840.113556.1.5.7000.53 NAME 'crossRefContainer' SUP top STRUCTURAL MAY (uPNSuffixes $ msDS-Behavior-Version $ msDS-SPNSuffixes $ msDS-UpdateScript $ msDS-ExecuteScriptPassword $ msDS-EnabledFeature ) )",
            "( *******.******* NAME 'ipNetwork' SUP top STRUCTURAL MUST (cn $ ipNetworkNumber ) MAY (l $ description $ uid $ manager $ msSFU30Name $ msSFU30Aliases $ msSFU30NisDomain $ ipNetmaskNumber $ nisMapName ) )",
            "( ******* NAME 'organizationalUnit' SUP top STRUCTURAL MUST (ou ) MAY (c $ l $ st $ street $ searchGuide $ businessCategory $ postalAddress $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ telephoneNumber $ telexNumber $ teletexTerminalIdentifier $ facsimileTelephoneNumber $ x121Address $ internationalISDNNumber $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ seeAlso $ userPassword $ co $ countryCode $ desktopProfile $ defaultGroup $ managedBy $ uPNSuffixes $ gPLink $ gPOptions $ msCOM-UserPartitionSetLink $ thumbnailLogo ) )",
            "( 1.2.840.113556.1.5.152 NAME 'intellimirrorGroup' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.253 NAME 'msFVE-RecoveryInformation' SUP top STRUCTURAL MUST (msFVE-RecoveryPassword $ msFVE-RecoveryGuid ) MAY (msFVE-VolumeGuid $ msFVE-KeyPackage ) )",
            "( 1.2.840.113556.1.5.262 NAME 'msImaging-PSPs' SUP container STRUCTURAL )",
            "( 1.2.840.113556.1.5.251 NAME 'ms-net-ieee-80211-GroupPolicy' SUP top STRUCTURAL MAY (ms-net-ieee-80211-GP-PolicyGUID $ ms-net-ieee-80211-GP-PolicyData $ ms-net-ieee-80211-GP-PolicyReserved ) )",
            "( 1.2.840.113556.1.5.138 NAME 'aCSSubnet' SUP top STRUCTURAL MAY (aCSMaxTokenRatePerFlow $ aCSMaxPeakBandwidthPerFlow $ aCSMaxDurationPerFlow $ aCSAllocableRSVPBandwidth $ aCSMaxPeakBandwidth $ aCSEnableRSVPMessageLogging $ aCSEventLogLevel $ aCSEnableACSService $ aCSRSVPLogFilesLocation $ aCSMaxNoOfLogFiles $ aCSMaxSizeOfRSVPLogFile $ aCSDSBMPriority $ aCSDSBMRefresh $ aCSDSBMDeadTime $ aCSCacheTimeout $ aCSNonReservedTxLimit $ aCSNonReservedTxSize $ aCSEnableRSVPAccounting $ aCSRSVPAccountFilesLocation $ aCSMaxNoOfAccountFiles $ aCSMaxSizeOfRSVPAccountFile $ aCSServerList $ aCSNonReservedPeakRate $ aCSNonReservedTokenSize $ aCSNonReservedMaxSDUSize $ aCSNonReservedMinPolicedSize ) )",
            "( 1.2.840.113556.1.5.43 NAME 'fTDfs' SUP top STRUCTURAL MUST (remoteServerName $ pKTGuid $ pKT ) MAY (keywords $ uNCName $ managedBy ) )",
            "( 1.2.840.113556.1.5.27 NAME 'rpcEntry' SUP connectionPoint ABSTRACT )",
            "( 1.2.840.113556.1.5.85 NAME 'dnsZone' SUP top STRUCTURAL MUST (dc ) MAY (dnsAllowDynamic $ dnsAllowXFR $ dnsSecureSecondaries $ dnsNotifySecondaries $ managedBy $ dNSProperty $ msDNS-IsSigned $ msDNS-SignWithNSEC3 $ msDNS-NSEC3OptOut $ msDNS-MaintainTrustAnchor $ msDNS-DSRecordAlgorithms $ msDNS-RFC5011KeyRollovers $ msDNS-NSEC3HashAlgorithm $ msDNS-NSEC3RandomSaltLength $ msDNS-NSEC3Iterations $ msDNS-DNSKEYRecordSetTTL $ msDNS-DSRecordSetTTL $ msDNS-SignatureInceptionOffset $ msDNS-SecureDelegationPollingPeriod $ msDNS-SigningKeyDescriptors $ msDNS-SigningKeys $ msDNS-DNSKEYRecords $ msDNS-ParentHasSecureDelegation $ msDNS-PropagationTime $ msDNS-NSEC3UserSalt $ msDNS-NSEC3CurrentSalt ) )",
            "( 1.2.840.113556.1.4.2163 NAME 'msAuthz-CentralAccessRule' SUP top STRUCTURAL MAY (Enabled $ msAuthz-EffectiveSecurityPolicy $ msAuthz-ProposedSecurityPolicy $ msAuthz-LastEffectiveSecurityPolicy $ msAuthz-ResourceCondition $ msAuthz-MemberRulesInCentralAccessPolicyBL ) )",
            "( 1.2.840.113556.1.5.194 NAME 'msCOM-PartitionSet' SUP top STRUCTURAL MAY (msCOM-PartitionLink $ msCOM-DefaultPartitionLink $ msCOM-ObjectId ) )",
            "( 1.2.840.113556.1.5.242 NAME 'msDS-QuotaContainer' SUP top STRUCTURAL MUST (cn ) MAY (msDS-DefaultQuota $ msDS-TombstoneQuotaFactor $ msDS-QuotaEffective $ msDS-QuotaUsed $ msDS-TopQuotaUsage ) )",
            "( 1.2.840.113556.1.5.281 NAME 'msDS-ClaimsTransformationPolicies' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.146 NAME 'remoteStorageServicePoint' SUP serviceAdministrationPoint STRUCTURAL MAY (remoteStorageGUID ) )",
            "( 1.2.840.113556.1.5.2 NAME 'samDomainBase' SUP top AUXILIARY MAY (nTSecurityDescriptor $ creationTime $ forceLogoff $ lockoutDuration $ lockOutObservationWindow $ lockoutThreshold $ maxPwdAge $ minPwdAge $ minPwdLength $ modifiedCountAtLastProm $ nextRid $ pwdProperties $ pwdHistoryLength $ revision $ objectSid $ oEMInformation $ serverState $ uASCompat $ serverRole $ domainReplica $ modifiedCount ) )",
            "( 1.2.840.113556.1.5.132 NAME 'dHCPClass' SUP top STRUCTURAL MUST (dhcpUniqueKey $ dhcpType $ dhcpFlags $ dhcpIdentification ) MAY (networkAddress $ dhcpObjName $ dhcpObjDescription $ dhcpServers $ dhcpSubnets $ dhcpMask $ dhcpRanges $ dhcpSites $ dhcpReservations $ superScopes $ superScopeDescription $ optionDescription $ optionsLocation $ dhcpOptions $ dhcpClasses $ mscopeId $ dhcpState $ dhcpProperties $ dhcpMaxKey $ dhcpUpdateTime ) )",
            "( 1.2.840.113556.1.5.283 NAME 'msDS-CloudExtensions' SUP top AUXILIARY MAY (msDS-cloudExtensionAttribute1 $ msDS-cloudExtensionAttribute2 $ msDS-cloudExtensionAttribute3 $ msDS-cloudExtensionAttribute4 $ msDS-cloudExtensionAttribute5 $ msDS-cloudExtensionAttribute6 $ msDS-cloudExtensionAttribute7 $ msDS-cloudExtensionAttribute8 $ msDS-cloudExtensionAttribute9 $ msDS-cloudExtensionAttribute10 $ msDS-cloudExtensionAttribute11 $ msDS-cloudExtensionAttribute12 $ msDS-cloudExtensionAttribute13 $ msDS-cloudExtensionAttribute14 $ msDS-cloudExtensionAttribute15 $ msDS-cloudExtensionAttribute16 $ msDS-cloudExtensionAttribute17 $ msDS-cloudExtensionAttribute18 $ msDS-cloudExtensionAttribute19 $ msDS-cloudExtensionAttribute20 ) )",
            "( 1.2.840.113556.1.5.89 NAME 'nTFRSSettings' SUP applicationSettings STRUCTURAL MAY (fRSExtensions $ managedBy ) )",
            "( 1.2.840.113556.1.5.24 NAME 'remoteMailRecipient' SUP top STRUCTURAL MAY (remoteSource $ remoteSourceType $ managedBy ) )",
            "( 1.2.840.113556.1.5.221 NAME 'msTAPI-RtConference' SUP top STRUCTURAL MUST (msTAPI-uid ) MAY (msTAPI-ProtocolId $ msTAPI-ConferenceBlob ) )",
            "( 1.2.840.113556.1.5.201 NAME 'msWMI-SimplePolicyTemplate' SUP msWMI-PolicyTemplate STRUCTURAL MUST (msWMI-TargetObject ) )",
            "( 1.2.840.113556.********.212 NAME 'msSFU30NetId' SUP top STRUCTURAL MAY (msSFU30Name $ msSFU30KeyValues $ msSFU30NisDomain $ nisMapName ) )",
            "( 1.2.840.113556.1.5.49 NAME 'packageRegistration' SUP top STRUCTURAL MAY (msiScriptPath $ cOMClassID $ cOMInterfaceID $ cOMProgID $ localeID $ machineArchitecture $ iconPath $ cOMTypelibId $ vendor $ packageType $ setupCommand $ packageName $ packageFlags $ versionNumberHi $ versionNumberLo $ lastUpdateSequence $ managedBy $ msiFileList $ categories $ upgradeProductCode $ msiScript $ canUpgradeScript $ fileExtPriority $ productCode $ msiScriptName $ msiScriptSize $ installUiLevel ) )",
            "( 1.2.840.113556.1.5.139 NAME 'lostAndFound' SUP top STRUCTURAL MAY (moveTreeState ) )",
            "( 1.2.840.113556.1.5.14 NAME 'connectionPoint' SUP leaf ABSTRACT MUST (cn ) MAY (keywords $ managedBy $ msDS-Settings ) )",
            "( 1.2.840.113556.1.5.6 NAME 'securityPrincipal' SUP top AUXILIARY MUST (objectSid $ sAMAccountName ) MAY (nTSecurityDescriptor $ securityIdentifier $ supplementalCredentials $ rid $ sAMAccountType $ sIDHistory $ altSecurityIdentities $ tokenGroups $ tokenGroupsNoGCAcceptable $ accountNameHistory $ tokenGroupsGlobalAndUniversal $ msDS-KeyVersionNumber ) )",
            "( 1.2.840.113556.1.5.147 NAME 'siteLink' SUP top STRUCTURAL MUST (siteList ) MAY (cost $ schedule $ options $ replInterval ) )",
            "( 1.2.840.113556.1.5.255 NAME 'msDS-PasswordSettings' SUP top STRUCTURAL MUST (msDS-MaximumPasswordAge $ msDS-MinimumPasswordAge $ msDS-MinimumPasswordLength $ msDS-PasswordHistoryLength $ msDS-PasswordComplexityEnabled $ msDS-PasswordReversibleEncryptionEnabled $ msDS-LockoutObservationWindow $ msDS-LockoutDuration $ msDS-LockoutThreshold $ msDS-PasswordSettingsPrecedence ) MAY (msDS-PSOAppliesTo ) )",
            "( 1.2.840.113556.1.4.2162 NAME 'msAuthz-CentralAccessRules' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.30 NAME 'serviceInstance' SUP connectionPoint STRUCTURAL MUST (displayName $ serviceClassID ) MAY (winsockAddresses $ serviceInstanceVersion ) )",
            "( 1.2.840.113556.1.5.156 NAME 'rRASAdministrationDictionary' SUP top STRUCTURAL MAY (msRRASVendorAttributeEntry ) )",
            "( 1.2.840.113556.1.4.2164 NAME 'msAuthz-CentralAccessPolicy' SUP top STRUCTURAL MAY (msAuthz-CentralAccessPolicyID $ msAuthz-MemberRulesInCentralAccessPolicy ) )",
            "( 2.16.840.1.113730.3.2.2 NAME 'inetOrgPerson' SUP user STRUCTURAL MAY (o $ businessCategory $ userCertificate $ givenName $ initials $ x500uniqueIdentifier $ displayName $ employeeNumber $ employeeType $ homePostalAddress $ userSMIMECertificate $ uid $ mail $ roomNumber $ photo $ manager $ homePhone $ secretary $ mobile $ pager $ audio $ jpegPhoto $ carLicense $ departmentNumber $ preferredLanguage $ userPKCS12 $ labeledURI ) )",
            "( 1.2.840.113556.1.5.52 NAME 'fileLinkTracking' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.18 NAME 'domainPolicy' SUP leaf STRUCTURAL MAY (authenticationOptions $ forceLogoff $ defaultLocalPolicyObject $ lockoutDuration $ lockOutObservationWindow $ lockoutThreshold $ maxPwdAge $ maxRenewAge $ maxTicketAge $ minPwdAge $ minPwdLength $ minTicketAge $ pwdProperties $ pwdHistoryLength $ proxyLifetime $ eFSPolicy $ publicKeyPolicy $ domainWidePolicy $ domainPolicyReference $ qualityOfService $ ipsecPolicyReference $ managedBy $ domainCAs ) )",
            "( 1.2.840.113556.********.216 NAME 'msSFU30NetworkUser' SUP top STRUCTURAL MAY (msSFU30Name $ msSFU30KeyValues $ msSFU30NisDomain $ nisMapName ) )",
            "( 0.9.2342.********.100.4.19 NAME 'simpleSecurityObject' SUP top AUXILIARY MAY (userPassword ) )",
            "( 1.2.840.113556.1.5.177 NAME 'pKICertificateTemplate' SUP top STRUCTURAL MAY (displayName $ flags $ pKIDefaultKeySpec $ pKIKeyUsage $ pKIMaxIssuingDepth $ pKICriticalExtensions $ pKIExpirationPeriod $ pKIOverlapPeriod $ pKIExtendedKeyUsage $ pKIDefaultCSPs $ pKIEnrollmentAccess $ msPKI-RA-Signature $ msPKI-Enrollment-Flag $ msPKI-Private-Key-Flag $ msPKI-Certificate-Name-Flag $ msPKI-Minimal-Key-Size $ msPKI-Template-Schema-Version $ msPKI-Template-Minor-Revision $ msPKI-Cert-Template-OID $ msPKI-Supersede-Templates $ msPKI-RA-Policies $ msPKI-Certificate-Policy $ msPKI-Certificate-Application-Policy $ msPKI-RA-Application-Policies ) )",
            "( 1.2.840.113556.1.5.293 NAME 'msDS-AuthNPolicies' SUP top STRUCTURAL )",
            "( 1.2.840.113556.********.2 NAME 'msDFSR-Subscriber' SUP top STRUCTURAL MUST (msDFSR-ReplicationGroupGuid $ msDFSR-MemberReference ) MAY (msDFSR-Extension $ msDFSR-Flags $ msDFSR-Options $ msDFSR-Options2 ) )",
            "( 1.2.840.113556.1.5.31 NAME 'site' SUP top STRUCTURAL MAY (location $ notificationList $ managedBy $ gPLink $ gPOptions $ mSMQSiteID $ mSMQNt4Stub $ mSMQSiteForeign $ mSMQInterval1 $ mSMQInterval2 $ msDS-BridgeHeadServersUsed ) )",
            "( 1.2.840.113556.1.5.222 NAME 'msTAPI-RtPerson' SUP top STRUCTURAL MAY (msTAPI-uid $ msTAPI-IpAddress ) )",
            "( 1.2.840.113556.1.5.68 NAME 'applicationSiteSettings' SUP top ABSTRACT MAY (applicationName $ notificationList ) )",
            "( 1.2.840.113556.1.3.14 NAME 'attributeSchema' SUP top STRUCTURAL MUST (cn $ attributeID $ attributeSyntax $ isSingleValued $ oMSyntax $ lDAPDisplayName $ schemaIDGUID ) MAY (rangeLower $ rangeUpper $ mAPIID $ linkID $ oMObjectClass $ searchFlags $ extendedCharsAllowed $ schemaFlagsEx $ attributeSecurityGUID $ systemOnly $ classDisplayName $ isMemberOfPartialAttributeSet $ isDefunct $ isEphemeral $ msDs-Schema-Extensions $ msDS-IntId ) )",
            "( 1.2.840.113556.1.5.267 NAME 'msSPP-ActivationObject' SUP top STRUCTURAL MUST (msSPP-CSVLKSkuId $ msSPP-KMSIds $ msSPP-CSVLKPid $ msSPP-CSVLKPartialProductKey ) MAY (msSPP-InstallationId $ msSPP-ConfirmationId $ msSPP-OnlineLicense $ msSPP-PhoneLicense $ msSPP-ConfigLicense $ msSPP-IssuanceLicense ) )",
            "( 1.2.840.113556.1.5.220 NAME 'msDS-App-Configuration' SUP applicationSettings STRUCTURAL MAY (owner $ keywords $ managedBy $ msDS-ByteArray $ msDS-DateTime $ msDS-Integer $ msDS-ObjectReference ) )",
            "( 1.2.840.113556.1.3.23 NAME 'container' SUP top STRUCTURAL MUST (cn ) MAY (schemaVersion $ defaultClassStore $ msDS-ObjectReference ) )",
            "( 1.2.840.113556.********.10 NAME 'msDFSR-Connection' SUP top STRUCTURAL MUST (fromServer ) MAY (msDFSR-Extension $ msDFSR-Enabled $ msDFSR-Schedule $ msDFSR-Keywords $ msDFSR-Flags $ msDFSR-Options $ msDFSR-RdcEnabled $ msDFSR-RdcMinFileSizeInKb $ msDFSR-Priority $ msDFSR-DisablePacketPrivacy $ msDFSR-Options2 ) )",
            "( 1.2.840.113556.1.5.207 NAME 'msWMI-UintRangeParam' SUP msWMI-RangeParam STRUCTURAL MUST (msWMI-IntDefault ) MAY (msWMI-IntMax $ msWMI-IntMin ) )",
            "( 1.2.840.113556.1.5.23 NAME 'printQueue' SUP connectionPoint STRUCTURAL MUST (uNCName $ versionNumber $ serverName $ printerName $ shortServerName ) MAY (location $ portName $ driverName $ printSeparatorFile $ priority $ defaultPriority $ printStartTime $ printEndTime $ printFormName $ printBinNames $ printMaxResolutionSupported $ printOrientationsSupported $ printMaxCopies $ printCollate $ printColor $ printLanguage $ printAttributes $ printShareName $ printOwner $ printNotify $ printStatus $ printSpooling $ printKeepPrintedJobs $ driverVersion $ printMaxXExtent $ printMaxYExtent $ printMinXExtent $ printMinYExtent $ printStaplingSupported $ printMemory $ assetNumber $ bytesPerMinute $ printRate $ printRateUnit $ printNetworkAddress $ printMACAddress $ printMediaReady $ printNumberUp $ printMediaSupported $ operatingSystem $ operatingSystemVersion $ operatingSystemServicePack $ operatingSystemHotfix $ physicalLocationObject $ printPagesPerMinute $ printDuplexSupported ) )",
            "( 1.2.840.113556.1.5.260 NAME 'msDFS-DeletedLinkv2' SUP top STRUCTURAL MUST (msDFS-NamespaceIdentityGUIDv2 $ msDFS-LastModifiedv2 $ msDFS-LinkPathv2 $ msDFS-LinkIdentityGUIDv2 ) MAY (msDFS-Commentv2 $ msDFS-ShortNameLinkPathv2 ) )",
            "( 1.2.840.113556.1.5.140 NAME 'interSiteTransportContainer' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.130 NAME 'indexServerCatalog' SUP connectionPoint STRUCTURAL MUST (creator ) MAY (uNCName $ queryPoint $ indexedScopes $ friendlyNames ) )",
            "( 1.2.840.113556.1.5.98 NAME 'ipsecPolicy' SUP ipsecBase STRUCTURAL MAY (ipsecISAKMPReference $ ipsecNFAReference ) )",
            "( ******* NAME 'top'  ABSTRACT MUST (objectClass $ instanceType $ nTSecurityDescriptor $ objectCategory ) MAY (cn $ description $ distinguishedName $ whenCreated $ whenChanged $ subRefs $ displayName $ uSNCreated $ isDeleted $ dSASignature $ objectVersion $ repsTo $ repsFrom $ memberOf $ ownerBL $ uSNChanged $ uSNLastObjRem $ showInAdvancedViewOnly $ adminDisplayName $ proxyAddresses $ adminDescription $ extensionName $ uSNDSALastObjRemoved $ displayNamePrintable $ directReports $ wWWHomePage $ USNIntersite $ name $ objectGUID $ replPropertyMetaData $ replUpToDateVector $ flags $ revision $ wbemPath $ fSMORoleOwner $ systemFlags $ siteObjectBL $ serverReferenceBL $ nonSecurityMemberBL $ queryPolicyBL $ wellKnownObjects $ isPrivilegeHolder $ partialAttributeSet $ managedObjects $ partialAttributeDeletionList $ url $ lastKnownParent $ bridgeheadServerListBL $ netbootSCPBL $ isCriticalSystemObject $ frsComputerReferenceBL $ fRSMemberReferenceBL $ uSNSource $ fromEntry $ allowedChildClasses $ allowedChildClassesEffective $ allowedAttributes $ allowedAttributesEffective $ possibleInferiors $ canonicalName $ proxiedObjectName $ sDRightsEffective $ dSCorePropagationData $ otherWellKnownObjects $ mS-DS-ConsistencyGuid $ mS-DS-ConsistencyChildCount $ masteredBy $ msCOM-PartitionSetLink $ msCOM-UserLink $ msDS-Approx-Immed-Subordinates $ msDS-NCReplCursors $ msDS-NCReplInboundNeighbors $ msDS-NCReplOutboundNeighbors $ msDS-ReplAttributeMetaData $ msDS-ReplValueMetaData $ msDS-NonMembersBL $ msDS-MembersForAzRoleBL $ msDS-OperationsForAzTaskBL $ msDS-TasksForAzTaskBL $ msDS-OperationsForAzRoleBL $ msDS-TasksForAzRoleBL $ msDs-masteredBy $ msDS-ObjectReferenceBL $ msDS-PrincipalName $ msDS-RevealedDSAs $ msDS-KrbTgtLinkBl $ msDS-IsFullReplicaFor $ msDS-IsDomainFor $ msDS-IsPartialReplicaFor $ msDS-AuthenticatedToAccountlist $ msDS-NC-RO-Replica-Locations-BL $ msDS-RevealedListBL $ msDS-PSOApplied $ msDS-NcType $ msDS-OIDToGroupLinkBl $ msDS-HostServiceAccountBL $ isRecycled $ msDS-LocalEffectiveDeletionTime $ msDS-LocalEffectiveRecycleTime $ msDS-LastKnownRDN $ msDS-EnabledFeatureBL $ msDS-ClaimSharesPossibleValuesWithBL $ msDS-MembersOfResourcePropertyListBL $ msDS-IsPrimaryComputerFor $ msDS-ValueTypeReferenceBL $ msDS-TDOIngressBL $ msDS-TDOEgressBL $ msDS-parentdistname $ msDS-ReplValueMetaDataExt $ msds-memberOfTransitive $ msds-memberTransitive $ structuralObjectClass $ createTimeStamp $ modifyTimeStamp $ subSchemaSubEntry $ msSFU30PosixMemberOf $ msDFSR-MemberReferenceBL $ msDFSR-ComputerReferenceBL ) )",
            "( 1.2.840.113556.1.5.36 NAME 'volume' SUP connectionPoint STRUCTURAL MUST (uNCName ) MAY (contentIndexingAllowed $ lastContentIndexed ) )",
            "( 1.2.840.113556.1.5.236 NAME 'msDS-AzOperation' SUP top STRUCTURAL MUST (msDS-AzOperationID ) MAY (description $ msDS-AzApplicationData $ msDS-AzObjectGuid $ msDS-AzGenericData ) )",
            "( ******* NAME 'groupOfNames' SUP top STRUCTURAL MUST (cn $ member ) MAY (o $ ou $ businessCategory $ owner $ seeAlso ) )",
            "( 1.2.840.113556.1.5.12 NAME 'configuration' SUP top STRUCTURAL MUST (cn ) MAY (gPLink $ gPOptions $ msDS-USNLastSyncSuccess ) )",
            "( 1.2.840.113556.1.5.78 NAME 'licensingSiteSettings' SUP applicationSiteSettings STRUCTURAL MAY (siteServer ) )",
            "( 1.2.840.113556.1.5.69 NAME 'nTDSSiteSettings' SUP applicationSiteSettings STRUCTURAL MAY (schedule $ options $ queryPolicyObject $ managedBy $ interSiteTopologyGenerator $ interSiteTopologyRenew $ interSiteTopologyFailover $ msDS-Preferred-GC-Site ) )",
            "( 1.2.840.113556.1.5.269 NAME 'msDS-ClaimTypePropertyBase' SUP top ABSTRACT MAY (Enabled $ msDS-ClaimPossibleValues $ msDS-ClaimSharesPossibleValuesWith ) )",
            "( 1.2.840.113556.1.5.273 NAME 'msDS-ResourceProperty' SUP msDS-ClaimTypePropertyBase STRUCTURAL MUST (msDS-ValueTypeReference ) MAY (msDS-IsUsedAsResourceSecurityAttribute $ msDS-AppliesToResourceTypes ) )",
            "( 1.2.840.113556.1.5.239 NAME 'msDS-AzRole' SUP top STRUCTURAL MAY (description $ msDS-MembersForAzRole $ msDS-OperationsForAzRole $ msDS-TasksForAzRole $ msDS-AzApplicationData $ msDS-AzObjectGuid $ msDS-AzGenericData ) )",
            "( *******.******** NAME 'bootableDevice' SUP top AUXILIARY MAY (cn $ bootParameter $ bootFile ) )",
            "( 1.2.840.113556.1.5.294 NAME 'msDS-AuthNPolicy' SUP top STRUCTURAL MAY (msDS-UserAllowedToAuthenticateTo $ msDS-UserAllowedToAuthenticateFrom $ msDS-UserTGTLifetime $ msDS-ComputerAllowedToAuthenticateTo $ msDS-ComputerTGTLifetime $ msDS-ServiceAllowedToAuthenticateTo $ msDS-ServiceAllowedToAuthenticateFrom $ msDS-ServiceTGTLifetime $ msDS-UserAuthNPolicyBL $ msDS-ComputerAuthNPolicyBL $ msDS-ServiceAuthNPolicyBL $ msDS-AssignedAuthNPolicyBL $ msDS-AuthNPolicyEnforced ) )",
            "( 1.2.840.113556.1.5.86 NAME 'dnsNode' SUP top STRUCTURAL MUST (dc ) MAY (dnsRecord $ dNSProperty $ dNSTombstoned ) )",
            "( 1.2.840.113556.1.5.210 NAME 'msWMI-StringSetParam' SUP msWMI-RangeParam STRUCTURAL MUST (msWMI-StringDefault ) MAY (msWMI-StringValidValues ) )",
            "( 1.2.840.113556.1.5.264 NAME 'msDS-ManagedServiceAccount' SUP computer STRUCTURAL )",
            "( 1.2.840.113556.1.5.15 NAME 'contact' SUP organizationalPerson STRUCTURAL MUST (cn ) MAY (notes $ msDS-SourceObjectDN ) )",
            "( *******.******* NAME 'posixAccount' SUP top AUXILIARY MAY (cn $ description $ userPassword $ homeDirectory $ unixUserPassword $ uid $ uidNumber $ gidNumber $ gecos $ unixHomeDirectory $ loginShell ) )",
            "( 1.2.840.113556.1.5.266 NAME 'msSPP-ActivationObjectsContainer' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.217 NAME 'msWMI-ObjectEncoding' SUP top STRUCTURAL MUST (msWMI-ID $ msWMI-TargetObject $ msWMI-Class $ msWMI-Genus $ msWMI-intFlags1 $ msWMI-intFlags2 $ msWMI-intFlags3 $ msWMI-intFlags4 $ msWMI-Parm1 $ msWMI-Parm2 $ msWMI-Parm3 $ msWMI-Parm4 $ msWMI-ScopeGuid ) )",
            "( 1.2.840.113556.1.5.33 NAME 'storage' SUP connectionPoint STRUCTURAL MAY (moniker $ monikerDisplayName $ iconPath ) )",
            "( 1.2.840.113556.1.5.67 NAME 'domainDNS' SUP domain STRUCTURAL MAY (managedBy $ msDS-Behavior-Version $ msDS-AllowedDNSSuffixes $ msDS-USNLastSyncSuccess $ msDS-EnabledFeature ) )",
            "( 1.2.840.113556.1.5.92 NAME 'linkTrackVolEntry' SUP leaf STRUCTURAL MAY (linkTrackSecret $ volTableIdxGUID $ volTableGUID $ currMachineId $ timeVolChange $ timeRefresh $ seqNotification $ objectCount ) )",
            "( *******.******** NAME 'ieee802Device' SUP top AUXILIARY MAY (cn $ macAddress ) )",
            "( 0.9.2342.********.100.4.17 NAME 'domainRelatedObject' SUP top AUXILIARY MAY (associatedDomain ) )",
            "( 1.2.840.113556.1.5.235 NAME 'msDS-AzApplication' SUP top STRUCTURAL MAY (description $ msDS-AzApplicationName $ msDS-AzGenerateAudits $ msDS-AzClassId $ msDS-AzApplicationVersion $ msDS-AzApplicationData $ msDS-AzObjectGuid $ msDS-AzGenericData ) )",
            "( 1.2.840.113556.1.5.107 NAME 'sitesContainer' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.263 NAME 'msImaging-PostScanProcess' SUP top STRUCTURAL MUST (displayName $ msImaging-PSPIdentifier ) MAY (serverName $ msImaging-PSPString ) )",
            "( 1.2.840.113556.1.5.240 NAME 'msieee80211-Policy' SUP top STRUCTURAL MAY (msieee80211-Data $ msieee80211-DataType $ msieee80211-ID ) )",
            "( 1.2.840.113556.1.5.95 NAME 'subnetContainer' SUP top STRUCTURAL )",
            "( 0.9.2342.********.100.4.6 NAME 'document' SUP top STRUCTURAL MAY (cn $ l $ o $ ou $ description $ seeAlso $ documentIdentifier $ documentTitle $ documentVersion $ documentAuthor $ documentLocation $ documentPublisher ) )",
            "( ******* NAME 'person' SUP top STRUCTURAL MUST (cn ) MAY (sn $ serialNumber $ telephoneNumber $ seeAlso $ userPassword $ attributeCertificateAttribute ) )",
            "( 1.2.840.113556.1.5.274 NAME 'msDS-ResourcePropertyList' SUP top STRUCTURAL MAY (msDS-MembersOfResourcePropertyList ) )",
            "( 1.2.840.113556.1.5.270 NAME 'msDS-ClaimTypes' SUP top STRUCTURAL )",
            "( *******.******* NAME 'shadowAccount' SUP top AUXILIARY MAY (description $ userPassword $ uid $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag ) )",
            "( 1.2.840.113556.1.5.179 NAME 'mSMQMigratedUser' SUP top STRUCTURAL MAY (objectSid $ mSMQSignCertificates $ mSMQDigests $ mSMQDigestsMig $ mSMQSignCertificatesMig $ mSMQUserSid ) )",
            "( 1.2.840.113556.1.5.185 NAME 'mS-SQL-OLAPServer' SUP serviceConnectionPoint STRUCTURAL MAY (mS-SQL-Name $ mS-SQL-RegisteredOwner $ mS-SQL-Contact $ mS-SQL-Build $ mS-SQL-ServiceAccount $ mS-SQL-Status $ mS-SQL-InformationURL $ mS-SQL-PublicationURL $ mS-SQL-Version $ mS-SQL-Language $ mS-SQL-Keywords ) )",
            "( *******.4.1.1466.101.119.2 NAME 'dynamicObject' SUP top AUXILIARY MAY (msDS-Entry-Time-To-Die $ entryTTL ) )",
            "( 1.2.840.113556.1.5.155 NAME 'nTFRSSubscriber' SUP top STRUCTURAL MUST (fRSRootPath $ fRSStagingPath ) MAY (schedule $ fRSUpdateTimeout $ fRSFaultCondition $ fRSServiceCommand $ fRSExtensions $ fRSFlags $ fRSMemberReference $ fRSServiceCommandStatus $ fRSTimeLastCommand $ fRSTimeLastConfigChange ) )",
            "( 1.2.840.113556.1.5.129 NAME 'rIDSet' SUP top STRUCTURAL MUST (rIDAllocationPool $ rIDPreviousAllocationPool $ rIDUsedPool $ rIDNextRID ) )",
            "( 1.2.840.113556.1.3.58 NAME 'addressTemplate' SUP displayTemplate STRUCTURAL MUST (displayName ) MAY (addressSyntax $ perMsgDialogDisplayTable $ perRecipDialogDisplayTable $ addressType $ proxyGenerationEnabled ) )",
            "( 1.2.840.113556.1.5.154 NAME 'nTFRSSubscriptions' SUP top STRUCTURAL MAY (fRSWorkingPath $ fRSExtensions $ fRSVersion ) )",
            "( 1.2.840.113556.1.5.7000.47 NAME 'nTDSDSA' SUP applicationSettings STRUCTURAL MAY (hasMasterNCs $ hasPartialReplicaNCs $ dMDLocation $ invocationId $ networkAddress $ options $ fRSRootPath $ serverReference $ lastBackupRestorationTime $ queryPolicyObject $ managedBy $ retiredReplDSASignatures $ msDS-Behavior-Version $ msDS-HasInstantiatedNCs $ msDS-ReplicationEpoch $ msDS-HasDomainNCs $ msDS-RetiredReplNCSignatures $ msDS-hasMasterNCs $ msDS-RevealedUsers $ msDS-hasFullReplicaNCs $ msDS-NeverRevealGroup $ msDS-RevealOnDemandGroup $ msDS-isGC $ msDS-isRODC $ msDS-SiteName $ msDS-IsUserCachableAtRodc $ msDS-EnabledFeature ) )",
            "( 1.2.840.113556.1.5.175 NAME 'infrastructureUpdate' SUP top STRUCTURAL MAY (dNReferenceUpdate ) )",
            "( 1.2.840.113556.********.215 NAME 'msSFU30DomainInfo' SUP top STRUCTURAL MAY (msSFU30SearchContainer $ msSFU30MasterServerName $ msSFU30OrderNumber $ msSFU30Domains $ msSFU30YpServers $ msSFU30MaxGidNumber $ msSFU30MaxUidNumber $ msSFU30IsValidContainer $ msSFU30CryptMethod ) )",
            "( 1.2.840.113556.1.5.213 NAME 'msWMI-Som' SUP top STRUCTURAL MUST (msWMI-ID $ msWMI-Name ) MAY (msWMI-Author $ msWMI-ChangeDate $ msWMI-CreationDate $ msWMI-SourceOrganization $ msWMI-intFlags1 $ msWMI-intFlags2 $ msWMI-intFlags3 $ msWMI-intFlags4 $ msWMI-Parm1 $ msWMI-Parm2 $ msWMI-Parm3 $ msWMI-Parm4 ) )",
            "( 1.2.840.113556.1.5.82 NAME 'rpcProfile' SUP rpcEntry STRUCTURAL )",
            "( 1.2.840.113556.1.5.164 NAME 'mSMQSiteLink' SUP top STRUCTURAL MUST (mSMQSite1 $ mSMQSite2 $ mSMQCost ) MAY (mSMQSiteGates $ mSMQSiteGatesMig ) )",
            "( 1.2.840.113556.1.5.184 NAME 'mS-SQL-SQLServer' SUP serviceConnectionPoint STRUCTURAL MAY (mS-SQL-Name $ mS-SQL-RegisteredOwner $ mS-SQL-Contact $ mS-SQL-Location $ mS-SQL-Memory $ mS-SQL-Build $ mS-SQL-ServiceAccount $ mS-SQL-CharacterSet $ mS-SQL-SortOrder $ mS-SQL-UnicodeSortOrder $ mS-SQL-Clustered $ mS-SQL-NamedPipe $ mS-SQL-MultiProtocol $ mS-SQL-SPX $ mS-SQL-TCPIP $ mS-SQL-AppleTalk $ mS-SQL-Vines $ mS-SQL-Status $ mS-SQL-LastUpdatedDate $ mS-SQL-InformationURL $ mS-SQL-GPSLatitude $ mS-SQL-GPSLongitude $ mS-SQL-GPSHeight $ mS-SQL-Keywords ) )",
            "( 1.2.840.113556.1.5.106 NAME 'queryPolicy' SUP top STRUCTURAL MAY (lDAPAdminLimits $ lDAPIPDenyList ) )",
            "( 1.2.840.113556.1.5.162 NAME 'mSMQConfiguration' SUP top STRUCTURAL MAY (mSMQQuota $ mSMQJournalQuota $ mSMQOwnerID $ mSMQSites $ mSMQOutRoutingServers $ mSMQInRoutingServers $ mSMQServiceType $ mSMQComputerType $ mSMQForeign $ mSMQOSType $ mSMQEncryptKey $ mSMQSignKey $ mSMQDependentClientServices $ mSMQRoutingServices $ mSMQDsServices $ mSMQComputerTypeEx ) )",
            "( 1.2.840.113556.1.5.257 NAME 'msDFS-NamespaceAnchor' SUP top STRUCTURAL MUST (msDFS-SchemaMajorVersion ) )",
            "( 1.2.840.113556.********.7 NAME 'msDFSR-ContentSet' SUP top STRUCTURAL MAY (description $ msDFSR-Extension $ msDFSR-RootSizeInMb $ msDFSR-StagingSizeInMb $ msDFSR-ConflictSizeInMb $ msDFSR-FileFilter $ msDFSR-DirectoryFilter $ msDFSR-Flags $ msDFSR-Options $ msDFSR-DfsPath $ msDFSR-Priority $ msDFSR-DeletedSizeInMb $ msDFSR-DefaultCompressionExclusionFilter $ msDFSR-OnDemandExclusionFileFilter $ msDFSR-OnDemandExclusionDirectoryFilter $ msDFSR-Options2 ) )",
            "( 1.2.840.113556.1.5.276 NAME 'msTPM-InformationObjectsContainer' SUP top STRUCTURAL MUST (cn ) )",
            "( 1.2.840.113556.1.5.209 NAME 'msWMI-RealRangeParam' SUP msWMI-RangeParam STRUCTURAL MUST (msWMI-Int8Default ) MAY (msWMI-Int8Max $ msWMI-Int8Min ) )",
            "( ******* NAME 'organizationalPerson' SUP person STRUCTURAL MAY (c $ l $ st $ street $ o $ ou $ title $ postalAddress $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ telexNumber $ teletexTerminalIdentifier $ facsimileTelephoneNumber $ x121Address $ internationalISDNNumber $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ givenName $ initials $ generationQualifier $ houseIdentifier $ otherTelephone $ otherPager $ co $ department $ company $ streetAddress $ otherHomePhone $ msExchHouseIdentifier $ personalTitle $ homePostalAddress $ countryCode $ employeeID $ comment $ division $ otherFacsimileTelephoneNumber $ otherMobile $ primaryTelexNumber $ primaryInternationalISDNNumber $ mhsORAddress $ otherMailbox $ assistant $ ipPhone $ otherIpPhone $ msDS-AllowedToDelegateTo $ msDS-PhoneticFirstName $ msDS-PhoneticLastName $ msDS-PhoneticDepartment $ msDS-PhoneticCompanyName $ msDS-PhoneticDisplayName $ msDS-HABSeniorityIndex $ msDS-AllowedToActOnBehalfOfOtherIdentity $ mail $ manager $ homePhone $ mobile $ pager $ middleName $ thumbnailPhoto $ thumbnailLogo ) )",
            "( 1.2.840.113556.1.5.176 NAME 'msExchConfigurationContainer' SUP container STRUCTURAL MAY (addressBookRoots $ globalAddressList $ templateRoots $ addressBookRoots2 $ globalAddressList2 $ templateRoots2 ) )",
            "( 1.2.840.113556.1.5.278 NAME 'msKds-ProvRootKey' SUP top STRUCTURAL MUST (cn $ msKds-KDFAlgorithmID $ msKds-SecretAgreementAlgorithmID $ msKds-PublicKeyLength $ msKds-PrivateKeyLength $ msKds-RootKeyData $ msKds-Version $ msKds-DomainID $ msKds-UseStartTime $ msKds-CreateTime ) MAY (msKds-KDFParam $ msKds-SecretAgreementParam ) )",
            "( 1.2.840.113556.1.5.238 NAME 'msDS-AzTask' SUP top STRUCTURAL MAY (description $ msDS-AzBizRule $ msDS-AzBizRuleLanguage $ msDS-AzLastImportedBizRulePath $ msDS-OperationsForAzTask $ msDS-TasksForAzTask $ msDS-AzTaskIsRoleDefinition $ msDS-AzApplicationData $ msDS-AzObjectGuid $ msDS-AzGenericData ) )",
            "( 1.2.840.113556.1.5.282 NAME 'msDS-GroupManagedServiceAccount' SUP computer STRUCTURAL MUST (msDS-ManagedPasswordInterval ) MAY (msDS-ManagedPassword $ msDS-ManagedPasswordId $ msDS-ManagedPasswordPreviousId $ msDS-GroupMSAMembership ) )",
            "( *******.******* NAME 'nisMap' SUP top STRUCTURAL MUST (cn $ nisMapName ) MAY (description ) )",
            "( *******.*******0 NAME 'nisObject' SUP top STRUCTURAL MUST (cn $ nisMapName $ nisMapEntry ) MAY (description $ msSFU30Name $ msSFU30NisDomain ) )",
            "( 1.2.840.113556.1.5.277 NAME 'msKds-ProvServerConfiguration' SUP top STRUCTURAL MUST (msKds-Version ) MAY (msKds-KDFAlgorithmID $ msKds-KDFParam $ msKds-SecretAgreementAlgorithmID $ msKds-SecretAgreementParam $ msKds-PublicKeyLength $ msKds-PrivateKeyLength ) )",
            "( 1.2.840.113556.********.217 NAME 'msSFU30NISMapConfig' SUP top STRUCTURAL MAY (msSFU30KeyAttributes $ msSFU30FieldSeparator $ msSFU30IntraFieldSeparator $ msSFU30SearchAttributes $ msSFU30ResultAttributes $ msSFU30MapFilter $ msSFU30NSMAPFieldPosition ) )",
            "( 1.2.840.113556.1.5.7000.48 NAME 'serversContainer' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.90 NAME 'linkTrackVolumeTable' SUP fileLinkTracking STRUCTURAL )",
            "( 1.2.840.113556.1.5.188 NAME 'mS-SQL-SQLDatabase' SUP top STRUCTURAL MAY (mS-SQL-Name $ mS-SQL-Contact $ mS-SQL-Status $ mS-SQL-InformationURL $ mS-SQL-Description $ mS-SQL-Alias $ mS-SQL-Size $ mS-SQL-CreationDate $ mS-SQL-LastBackupDate $ mS-SQL-LastDiagnosticDate $ mS-SQL-Applications $ mS-SQL-Keywords ) )",
            "( 1.2.840.113556.1.5.211 NAME 'msWMI-PolicyType' SUP top STRUCTURAL MUST (msWMI-ID $ msWMI-TargetObject ) MAY (msWMI-Author $ msWMI-ChangeDate $ msWMI-CreationDate $ msWMI-SourceOrganization $ msWMI-intFlags1 $ msWMI-intFlags2 $ msWMI-intFlags3 $ msWMI-intFlags4 $ msWMI-Parm1 $ msWMI-Parm2 $ msWMI-Parm3 $ msWMI-Parm4 ) )",
            "( 1.2.840.113556.1.5.183 NAME 'dSUISettings' SUP top STRUCTURAL MAY (dSUIAdminNotification $ dSUIAdminMaximum $ dSUIShellMaximum $ msDS-Security-Group-Extra-Classes $ msDS-Non-Security-Group-Extra-Classes $ msDS-FilterContainers ) )",
            "( 1.2.840.113556.1.5.157 NAME 'groupPolicyContainer' SUP container STRUCTURAL MAY (flags $ versionNumber $ gPCFunctionalityVersion $ gPCFileSysPath $ gPCMachineExtensionNames $ gPCUserExtensionNames $ gPCWQLFilter ) )",
            "( 1.2.840.113556.1.5.3 NAME 'samDomain' SUP top AUXILIARY MAY (description $ cACertificate $ builtinCreationTime $ builtinModifiedCount $ creationTime $ domainPolicyObject $ defaultLocalPolicyObject $ lockoutDuration $ lockOutObservationWindow $ lSACreationTime $ lSAModifiedCount $ lockoutThreshold $ maxPwdAge $ minPwdAge $ minPwdLength $ modifiedCountAtLastProm $ nETBIOSName $ nextRid $ pwdProperties $ pwdHistoryLength $ privateKey $ replicaSource $ controlAccessRights $ auditingPolicy $ eFSPolicy $ desktopProfile $ nTMixedDomain $ rIDManagerReference $ treeName $ pekList $ pekKeyChangeInterval $ gPLink $ gPOptions $ ms-DS-MachineAccountQuota $ msDS-LogonTimeSyncInterval $ msDS-PerUserTrustQuota $ msDS-AllUsersTrustQuota $ msDS-PerUserTrustTombstonesQuota ) )",
            "( 1.2.840.113556.1.5.234 NAME 'msDS-AzAdminManager' SUP top STRUCTURAL MAY (description $ msDS-AzDomainTimeout $ msDS-AzScriptEngineCacheMax $ msDS-AzScriptTimeout $ msDS-AzGenerateAudits $ msDS-AzApplicationData $ msDS-AzMajorVersion $ msDS-AzMinorVersion $ msDS-AzObjectGuid $ msDS-AzGenericData ) )",
            "( 1.2.840.113556.1.5.214 NAME 'msWMI-Rule' SUP top STRUCTURAL MUST (msWMI-Query $ msWMI-QueryLanguage $ msWMI-TargetNameSpace ) )",
            "( 1.2.840.113556.1.5.254 NAME 'nTDSDSARO' SUP nTDSDSA STRUCTURAL )",
            "( 1.2.840.113556.1.5.286 NAME 'msDS-Device' SUP top STRUCTURAL MUST (displayName $ altSecurityIdentities $ msDS-IsEnabled $ msDS-DeviceID ) MAY (msDS-DeviceOSType $ msDS-DeviceOSVersion $ msDS-DevicePhysicalIDs $ msDS-DeviceObjectVersion $ msDS-RegisteredOwner $ msDS-ApproximateLastLogonTimeStamp $ msDS-RegisteredUsers $ msDS-IsManaged $ msDS-CloudIsManaged $ msDS-CloudAnchor ) )",
            "( 1.2.840.113556.1.5.34 NAME 'trustedDomain' SUP leaf STRUCTURAL MAY (securityIdentifier $ trustAuthIncoming $ trustDirection $ trustPartner $ trustPosixOffset $ trustAuthOutgoing $ trustType $ trustAttributes $ domainCrossRef $ flatName $ initialAuthIncoming $ initialAuthOutgoing $ domainIdentifier $ additionalTrustedServiceNames $ mS-DS-CreatorSID $ msDS-TrustForestTrustInfo $ msDS-SupportedEncryptionTypes $ msDS-IngressClaimsTransformationPolicy $ msDS-EgressClaimsTransformationPolicy ) )",
            "( 0.9.2342.********.100.4.7 NAME 'room' SUP top STRUCTURAL MUST (cn ) MAY (description $ telephoneNumber $ seeAlso $ location $ roomNumber ) )",
            "( ******* NAME 'organization' SUP top STRUCTURAL MUST (o ) MAY (l $ st $ street $ searchGuide $ businessCategory $ postalAddress $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ telephoneNumber $ telexNumber $ teletexTerminalIdentifier $ facsimileTelephoneNumber $ x121Address $ internationalISDNNumber $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ seeAlso $ userPassword ) )",
            "( 1.2.840.113556.1.5.272 NAME 'msDS-ClaimType' SUP msDS-ClaimTypePropertyBase STRUCTURAL MAY (msDS-ClaimValueType $ msDS-ClaimAttributeSource $ msDS-ClaimTypeAppliesToClass $ msDS-ClaimSource $ msDS-ClaimSourceType $ msDS-ClaimIsValueSpaceRestricted $ msDS-ClaimIsSingleValued ) )",
            "( *******.******* NAME 'ipService' SUP top STRUCTURAL MUST (cn $ ipServicePort $ ipServiceProtocol ) MAY (description $ msSFU30Name $ msSFU30Aliases $ msSFU30NisDomain $ nisMapName ) )",
            "( *******.******* NAME 'ipProtocol' SUP top STRUCTURAL MUST (cn $ ipProtocolNumber ) MAY (description $ msSFU30Name $ msSFU30Aliases $ msSFU30NisDomain $ nisMapName ) )",
            "( 1.2.840.113556.1.5.80 NAME 'rpcGroup' SUP rpcEntry STRUCTURAL MAY (rpcNsGroup $ rpcNsObjectID ) )",
            "( 1.2.840.113556.1.5.17 NAME 'server' SUP top STRUCTURAL MAY (serialNumber $ serverReference $ dNSHostName $ managedBy $ mailAddress $ bridgeheadTransportList $ msDS-isGC $ msDS-isRODC $ msDS-SiteName $ msDS-IsUserCachableAtRodc ) )",
            "( 1.2.840.113556.1.5.28 NAME 'secret' SUP leaf STRUCTURAL MAY (currentValue $ lastSetTime $ priorSetTime $ priorValue ) )",
            "( 1.2.840.113556.1.5.163 NAME 'mSMQEnterpriseSettings' SUP top STRUCTURAL MAY (mSMQNameStyle $ mSMQCSPName $ mSMQLongLived $ mSMQVersion $ mSMQInterval1 $ mSMQInterval2 ) )",
            "( 1.2.840.113556.1.5.202 NAME 'msWMI-MergeablePolicyTemplate' SUP msWMI-PolicyTemplate STRUCTURAL )",
            "( 1.2.840.113556.1.5.195 NAME 'msPKI-Key-Recovery-Agent' SUP user STRUCTURAL )",
            "( 0.9.2342.********.100.4.18 NAME 'friendlyCountry' SUP country STRUCTURAL MUST (co ) )",
            "( 1.2.840.113556.1.5.258 NAME 'msDFS-Namespacev2' SUP top STRUCTURAL MUST (msDFS-SchemaMajorVersion $ msDFS-SchemaMinorVersion $ msDFS-GenerationGUIDv2 $ msDFS-NamespaceIdentityGUIDv2 $ msDFS-LastModifiedv2 $ msDFS-Ttlv2 $ msDFS-Propertiesv2 $ msDFS-TargetListv2 ) MAY (msDFS-Commentv2 ) )",
            "( 1.2.840.113556.1.5.96 NAME 'subnet' SUP top STRUCTURAL MAY (location $ siteObject $ physicalLocationObject ) )",
            "( 1.2.840.113556.1.5.216 NAME 'applicationVersion' SUP applicationSettings STRUCTURAL MAY (owner $ keywords $ versionNumber $ vendor $ versionNumberHi $ versionNumberLo $ managedBy $ appSchemaVersion ) )",
            "( ******** NAME 'residentialPerson' SUP person STRUCTURAL MAY (l $ st $ street $ ou $ title $ businessCategory $ postalAddress $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ telexNumber $ teletexTerminalIdentifier $ facsimileTelephoneNumber $ x121Address $ internationalISDNNumber $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod ) )",
            "( ******** NAME 'cRLDistributionPoint' SUP top STRUCTURAL MUST (cn ) MAY (authorityRevocationList $ certificateRevocationList $ deltaRevocationList $ cRLPartitionedRevocationList $ certificateAuthorityObject ) )",
            "( 1.2.840.113556.1.5.137 NAME 'aCSPolicy' SUP top STRUCTURAL MAY (aCSTimeOfDay $ aCSDirection $ aCSMaxTokenRatePerFlow $ aCSMaxPeakBandwidthPerFlow $ aCSAggregateTokenRatePerUser $ aCSMaxDurationPerFlow $ aCSServiceType $ aCSTotalNoOfFlows $ aCSPriority $ aCSPermissionBits $ aCSIdentityName $ aCSMaxAggregatePeakRatePerUser $ aCSMaxTokenBucketPerFlow $ aCSMaximumSDUSize $ aCSMinimumPolicedSize $ aCSMinimumLatency $ aCSMinimumDelayVariation ) )",
            "( 1.2.840.113556.1.5.77 NAME 'controlAccessRight' SUP top STRUCTURAL MAY (rightsGuid $ appliesTo $ localizationDisplayId $ validAccesses ) )",
            "( 1.2.840.113556.1.5.219 NAME 'msMQ-Group' SUP top STRUCTURAL MUST (member ) )",
            "( 1.2.840.113556.1.5.8 NAME 'group' SUP top STRUCTURAL MUST (groupType ) MAY (member $ nTGroupMembers $ operatorCount $ adminCount $ groupAttributes $ groupMembershipSAM $ controlAccessRights $ desktopProfile $ nonSecurityMember $ managedBy $ primaryGroupToken $ msDS-AzLDAPQuery $ msDS-NonMembers $ msDS-AzBizRule $ msDS-AzBizRuleLanguage $ msDS-AzLastImportedBizRulePath $ msDS-AzApplicationData $ msDS-AzObjectGuid $ msDS-AzGenericData $ msDS-PrimaryComputer $ mail $ msSFU30Name $ msSFU30NisDomain $ msSFU30PosixMember ) )",
            "( 1.2.840.113556.******** NAME 'msPrint-ConnectionPolicy' SUP top STRUCTURAL MUST (cn ) MAY (uNCName $ serverName $ printAttributes $ printerName ) )",
            "( 1.2.840.113556.1.3.11 NAME 'crossRef' SUP top STRUCTURAL MUST (cn $ nCName $ dnsRoot ) MAY (Enabled $ nETBIOSName $ nTMixedDomain $ trustParent $ superiorDNSRoot $ rootTrust $ msDS-Behavior-Version $ msDS-NC-Replica-Locations $ msDS-Replication-Notify-First-DSA-Delay $ msDS-Replication-Notify-Subsequent-DSA-Delay $ msDS-SDReferenceDomain $ msDS-DnsRootAlias $ msDS-NC-RO-Replica-Locations ) )",
            "( 1.2.840.113556.********.9 NAME 'msDFSR-Member' SUP top STRUCTURAL MUST (msDFSR-ComputerReference ) MAY (serverReference $ msDFSR-Extension $ msDFSR-Keywords $ msDFSR-Flags $ msDFSR-Options $ msDFSR-Options2 ) )",
            "( 1.2.840.113556.1.3.59 NAME 'displayTemplate' SUP top STRUCTURAL MUST (cn ) MAY (helpData32 $ originalDisplayTableMSDOS $ addressEntryDisplayTable $ helpFileName $ addressEntryDisplayTableMSDOS $ helpData16 $ originalDisplayTable ) )",
            "( 1.2.840.113556.1.3.13 NAME 'classSchema' SUP top STRUCTURAL MUST (cn $ subClassOf $ governsID $ objectClassCategory $ schemaIDGUID $ defaultObjectCategory ) MAY (possSuperiors $ mustContain $ mayContain $ rDNAttID $ auxiliaryClass $ lDAPDisplayName $ schemaFlagsEx $ systemOnly $ systemPossSuperiors $ systemMayContain $ systemMustContain $ systemAuxiliaryClass $ defaultSecurityDescriptor $ defaultHidingValue $ classDisplayName $ isDefunct $ msDs-Schema-Extensions $ msDS-IntId ) )",
            "( 1.2.840.113556.1.5.200 NAME 'msWMI-PolicyTemplate' SUP top STRUCTURAL MUST (msWMI-ID $ msWMI-Name $ msWMI-NormalizedClass $ msWMI-TargetClass $ msWMI-TargetNameSpace $ msWMI-TargetPath ) MAY (msWMI-Author $ msWMI-ChangeDate $ msWMI-CreationDate $ msWMI-SourceOrganization $ msWMI-TargetType $ msWMI-intFlags1 $ msWMI-intFlags2 $ msWMI-intFlags3 $ msWMI-intFlags4 $ msWMI-Parm1 $ msWMI-Parm2 $ msWMI-Parm3 $ msWMI-Parm4 ) )",
            "( 1.2.840.113556.1.5.165 NAME 'mSMQSettings' SUP top STRUCTURAL MAY (mSMQOwnerID $ mSMQServices $ mSMQQMID $ mSMQMigrated $ mSMQNt4Flags $ mSMQSiteName $ mSMQRoutingService $ mSMQDsService $ mSMQDependentClientService $ mSMQSiteNameEx ) )",
            "( *******.******* NAME 'oncRpc' SUP top STRUCTURAL MUST (cn $ oncRpcNumber ) MAY (description $ msSFU30Name $ msSFU30Aliases $ msSFU30NisDomain $ nisMapName ) )",
            "( 1.2.840.113556.1.5.126 NAME 'serviceConnectionPoint' SUP connectionPoint STRUCTURAL MAY (versionNumber $ vendor $ versionNumberHi $ versionNumberLo $ serviceClassName $ serviceBindingInformation $ serviceDNSName $ serviceDNSNameType $ appSchemaVersion ) )",
            "( 1.2.840.113556.1.5.4 NAME 'builtinDomain' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.241 NAME 'msDS-AppData' SUP applicationSettings STRUCTURAL MAY (owner $ keywords $ managedBy $ msDS-ByteArray $ msDS-DateTime $ msDS-Integer $ msDS-ObjectReference ) )",
            "( 1.2.840.113556.1.5.73 NAME 'rpcServerElement' SUP rpcEntry STRUCTURAL MUST (rpcNsBindings $ rpcNsInterfaceID $ rpcNsTransferSyntax ) )",
            "( 1.2.840.113556.1.5.150 NAME 'rRASAdministrationConnectionPoint' SUP serviceAdministrationPoint STRUCTURAL MAY (msRRASAttribute ) )",
            "( 1.2.840.113556.1.5.191 NAME 'aCSResourceLimits' SUP top STRUCTURAL MAY (aCSMaxTokenRatePerFlow $ aCSMaxPeakBandwidthPerFlow $ aCSServiceType $ aCSAllocableRSVPBandwidth $ aCSMaxPeakBandwidth ) )",
            "( ******* NAME 'locality' SUP top STRUCTURAL MUST (l ) MAY (st $ street $ searchGuide $ seeAlso ) )",
            "( *******.******* NAME 'ipHost' SUP top AUXILIARY MAY (cn $ l $ description $ uid $ manager $ ipHostNumber ) )",
            "( 1.2.840.113556.1.5.275 NAME 'msTPM-InformationObject' SUP top STRUCTURAL MUST (msTPM-OwnerInformation ) MAY (msTPM-SrkPubThumbprint $ msTPM-OwnerInformationTemp ) )",
            "( 1.2.840.113556.1.5.289 NAME 'msDS-DeviceContainer' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.4.2129 NAME 'msDNS-ServerSettings' SUP top STRUCTURAL MAY (msDNS-KeymasterZones ) )",
            "( 1.2.840.113556.1.5.76 NAME 'foreignSecurityPrincipal' SUP top STRUCTURAL MUST (objectSid ) MAY (foreignIdentifier ) )",
            "( 1.2.840.113556.1.5.44 NAME 'classStore' SUP top STRUCTURAL MAY (versionNumber $ nextLevelStore $ lastUpdateSequence $ appSchemaVersion ) )",
            "( 0.9.2342.********.100.4.5 NAME 'account' SUP top STRUCTURAL MAY (l $ o $ ou $ description $ seeAlso $ uid $ host ) )",
            "( 1.2.840.113556.1.5.26 NAME 'rpcProfileElement' SUP rpcEntry STRUCTURAL MUST (rpcNsInterfaceID $ rpcNsPriority ) MAY (rpcNsProfileEntry $ rpcNsAnnotation ) )",
            "( 1.2.840.113556.1.5.215 NAME 'msWMI-WMIGPO' SUP top STRUCTURAL MUST (msWMI-TargetClass ) MAY (msWMI-intFlags1 $ msWMI-intFlags2 $ msWMI-intFlags3 $ msWMI-intFlags4 $ msWMI-Parm1 $ msWMI-Parm2 $ msWMI-Parm3 $ msWMI-Parm4 ) )",
            "( 1.2.840.113556.1.5.243 NAME 'msDS-QuotaControl' SUP top STRUCTURAL MUST (cn $ msDS-QuotaTrustee $ msDS-QuotaAmount ) )",
            "( 1.2.840.113556.1.5.256 NAME 'msDS-PasswordSettingsContainer' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.187 NAME 'mS-SQL-SQLPublication' SUP top STRUCTURAL MAY (mS-SQL-Name $ mS-SQL-Status $ mS-SQL-Description $ mS-SQL-Type $ mS-SQL-Database $ mS-SQL-AllowAnonymousSubscription $ mS-SQL-Publisher $ mS-SQL-AllowKnownPullSubscription $ mS-SQL-AllowImmediateUpdatingSubscription $ mS-SQL-AllowQueuedUpdatingSubscription $ mS-SQL-AllowSnapshotFilesFTPDownloading $ mS-SQL-ThirdParty ) )",
            "( 1.2.840.113556.1.5.9 NAME 'user' SUP organizationalPerson STRUCTURAL MAY (o $ businessCategory $ userCertificate $ givenName $ initials $ x500uniqueIdentifier $ displayName $ networkAddress $ employeeNumber $ employeeType $ homePostalAddress $ userAccountControl $ badPwdCount $ codePage $ homeDirectory $ homeDrive $ badPasswordTime $ lastLogoff $ lastLogon $ dBCSPwd $ localeID $ scriptPath $ logonHours $ logonWorkstation $ maxStorage $ userWorkstations $ unicodePwd $ otherLoginWorkstations $ ntPwdHistory $ pwdLastSet $ preferredOU $ primaryGroupID $ userParameters $ profilePath $ operatorCount $ adminCount $ accountExpires $ lmPwdHistory $ groupMembershipSAM $ logonCount $ controlAccessRights $ defaultClassStore $ groupsToIgnore $ groupPriority $ desktopProfile $ dynamicLDAPServer $ userPrincipalName $ lockoutTime $ userSharedFolder $ userSharedFolderOther $ servicePrincipalName $ aCSPolicyName $ terminalServer $ mSMQSignCertificates $ mSMQDigests $ mSMQDigestsMig $ mSMQSignCertificatesMig $ msNPAllowDialin $ msNPCallingStationID $ msNPSavedCallingStationID $ msRADIUSCallbackNumber $ msRADIUSFramedIPAddress $ msRADIUSFramedRoute $ msRADIUSServiceType $ msRASSavedCallbackNumber $ msRASSavedFramedIPAddress $ msRASSavedFramedRoute $ mS-DS-CreatorSID $ msCOM-UserPartitionSetLink $ msDS-Cached-Membership $ msDS-Cached-Membership-Time-Stamp $ msDS-Site-Affinity $ msDS-User-Account-Control-Computed $ lastLogonTimestamp $ msIIS-FTPRoot $ msIIS-FTPDir $ msDRM-IdentityCertificate $ msDS-SourceObjectDN $ msPKIRoamingTimeStamp $ msPKIDPAPIMasterKeys $ msPKIAccountCredentials $ msRADIUS-FramedInterfaceId $ msRADIUS-SavedFramedInterfaceId $ msRADIUS-FramedIpv6Prefix $ msRADIUS-SavedFramedIpv6Prefix $ msRADIUS-FramedIpv6Route $ msRADIUS-SavedFramedIpv6Route $ msDS-SecondaryKrbTgtNumber $ msDS-AuthenticatedAtDC $ msDS-SupportedEncryptionTypes $ msDS-LastSuccessfulInteractiveLogonTime $ msDS-LastFailedInteractiveLogonTime $ msDS-FailedInteractiveLogonCount $ msDS-FailedInteractiveLogonCountAtLastSuccessfulLogon $ msTSProfilePath $ msTSHomeDirectory $ msTSHomeDrive $ msTSAllowLogon $ msTSRemoteControl $ msTSMaxDisconnectionTime $ msTSMaxConnectionTime $ msTSMaxIdleTime $ msTSReconnectionAction $ msTSBrokenConnectionAction $ msTSConnectClientDrives $ msTSConnectPrinterDrives $ msTSDefaultToMainPrinter $ msTSWorkDirectory $ msTSInitialProgram $ msTSProperty01 $ msTSProperty02 $ msTSExpireDate $ msTSLicenseVersion $ msTSManagingLS $ msDS-UserPasswordExpiryTimeComputed $ msTSExpireDate2 $ msTSLicenseVersion2 $ msTSManagingLS2 $ msTSExpireDate3 $ msTSLicenseVersion3 $ msTSManagingLS3 $ msTSExpireDate4 $ msTSLicenseVersion4 $ msTSManagingLS4 $ msTSLSProperty01 $ msTSLSProperty02 $ msDS-ResultantPSO $ msPKI-CredentialRoamingTokens $ msTSPrimaryDesktop $ msTSSecondaryDesktops $ msDS-PrimaryComputer $ msDS-SyncServerUrl $ msDS-AssignedAuthNPolicySilo $ msDS-AuthNPolicySiloMembersBL $ msDS-AssignedAuthNPolicy $ userSMIMECertificate $ uid $ mail $ roomNumber $ photo $ manager $ homePhone $ secretary $ mobile $ pager $ audio $ jpegPhoto $ carLicense $ departmentNumber $ preferredLanguage $ userPKCS12 $ labeledURI $ msSFU30Name $ msSFU30NisDomain ) )",
            "( 1.2.840.113556.1.5.259 NAME 'msDFS-Linkv2' SUP top STRUCTURAL MUST (msDFS-GenerationGUIDv2 $ msDFS-NamespaceIdentityGUIDv2 $ msDFS-LastModifiedv2 $ msDFS-Ttlv2 $ msDFS-Propertiesv2 $ msDFS-TargetListv2 $ msDFS-LinkPathv2 $ msDFS-LinkIdentityGUIDv2 ) MAY (msDFS-Commentv2 $ msDFS-LinkSecurityDescriptorv2 $ msDFS-ShortNameLinkPathv2 ) )",
            "( 1.2.840.113556.1.5.141 NAME 'interSiteTransport' SUP top STRUCTURAL MUST (transportDLLName $ transportAddressAttribute ) MAY (options $ replInterval ) )",
            "( 1.2.840.113556.********.4 NAME 'msDFSR-GlobalSettings' SUP top STRUCTURAL MAY (msDFSR-Extension $ msDFSR-Flags $ msDFSR-Options $ msDFSR-Options2 ) )",
            "( 1.2.840.113556.1.5.29 NAME 'serviceClass' SUP leaf STRUCTURAL MUST (displayName $ serviceClassID ) MAY (serviceClassInfo ) )",
            "( 1.2.840.113556.1.5.189 NAME 'mS-SQL-OLAPDatabase' SUP top STRUCTURAL MAY (mS-SQL-Name $ mS-SQL-Contact $ mS-SQL-Status $ mS-SQL-LastUpdatedDate $ mS-SQL-InformationURL $ mS-SQL-ConnectionURL $ mS-SQL-PublicationURL $ mS-SQL-Description $ mS-SQL-Type $ mS-SQL-Size $ mS-SQL-LastBackupDate $ mS-SQL-Applications $ mS-SQL-Keywords ) )",
            "( ******** NAME 'certificationAuthority' SUP top STRUCTURAL MUST (cn $ cACertificate $ authorityRevocationList $ certificateRevocationList ) MAY (searchGuide $ teletexTerminalIdentifier $ supportedApplicationContext $ crossCertificatePair $ deltaRevocationList $ domainPolicyObject $ parentCA $ dNSHostName $ parentCACertificateChain $ domainID $ cAConnect $ cAWEBURL $ cRLObject $ cAUsages $ previousCACertificates $ pendingCACertificates $ previousParentCA $ pendingParentCA $ currentParentCA $ cACertificateDN $ certificateTemplates $ signatureAlgorithms $ enrollmentProviders ) )",
            "( 1.2.840.113556.1.5.104 NAME 'meeting' SUP top STRUCTURAL MUST (meetingName ) MAY (meetingID $ meetingDescription $ meetingKeyword $ meetingLocation $ meetingProtocol $ meetingType $ meetingApplication $ meetingLanguage $ meetingMaxParticipants $ meetingOriginator $ meetingContactInfo $ meetingOwner $ meetingIP $ meetingScope $ meetingAdvertiseScope $ meetingURL $ meetingRating $ meetingIsEncrypted $ meetingRecurrence $ meetingStartTime $ meetingEndTime $ meetingBandwidth $ meetingBlob ) )",
            "( 1.2.840.113556.1.5.287 NAME 'msDS-DeviceRegistrationServiceContainer' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.71 NAME 'nTDSConnection' SUP leaf STRUCTURAL MUST (enabledConnection $ fromServer $ options ) MAY (generatedConnection $ schedule $ transportType $ mS-DS-ReplicatesNCReason ) )",
            "( 1.2.840.113556.1.5.291 NAME 'msDS-AuthNPolicySilos' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.218 NAME 'msMQ-Custom-Recipient' SUP top STRUCTURAL MAY (msMQ-Recipient-FormatName ) )",
            "( 1.2.840.113556.1.5.72 NAME 'nTDSService' SUP top STRUCTURAL MAY (tombstoneLifetime $ dSHeuristics $ garbageCollPeriod $ replTopologyStayOfExecution $ sPNMappings $ msDS-Other-Settings $ msDS-DeletedObjectLifetime ) )",
            "( 1.2.840.113556.1.3.9 NAME 'dMD' SUP top STRUCTURAL MUST (cn ) MAY (dmdName $ schemaUpdate $ prefixMap $ schemaInfo $ msDs-Schema-Extensions $ msDS-IntId $ msDS-USNLastSyncSuccess ) )",
            "( 1.2.840.113556.1.5.280 NAME 'msDS-ClaimsTransformationPolicyType' SUP top STRUCTURAL MAY (msDS-TransformationRules $ msDS-TransformationRulesCompiled ) )",
            "( 0.9.2342.********.100.4.14 NAME 'rFC822LocalPart' SUP domain STRUCTURAL MAY (cn $ sn $ street $ description $ postalAddress $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ telephoneNumber $ telexNumber $ teletexTerminalIdentifier $ facsimileTelephoneNumber $ x121Address $ internationalISDNNumber $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ seeAlso ) )",
            "( 1.2.840.113556.1.5.190 NAME 'mS-SQL-OLAPCube' SUP top STRUCTURAL MAY (mS-SQL-Name $ mS-SQL-Contact $ mS-SQL-Status $ mS-SQL-LastUpdatedDate $ mS-SQL-InformationURL $ mS-SQL-PublicationURL $ mS-SQL-Description $ mS-SQL-Size $ mS-SQL-Keywords ) )",
            "( 1.2.840.113556.1.5.208 NAME 'msWMI-UintSetParam' SUP msWMI-RangeParam STRUCTURAL MUST (msWMI-IntDefault ) MAY (msWMI-IntValidValues ) )",
            "( *******.******* NAME 'posixGroup' SUP top AUXILIARY MAY (cn $ description $ userPassword $ unixUserPassword $ gidNumber $ memberUid ) )",
            "( ******** NAME 'groupOfUniqueNames' SUP top STRUCTURAL MUST (cn $ uniqueMember ) MAY (o $ ou $ description $ businessCategory $ owner $ seeAlso ) )",
            "( 1.2.840.113556.1.5.252 NAME 'ms-net-ieee-8023-GroupPolicy' SUP top STRUCTURAL MAY (ms-net-ieee-8023-GP-PolicyGUID $ ms-net-ieee-8023-GP-PolicyData $ ms-net-ieee-8023-GP-PolicyReserved ) )",
            "( 1.2.840.113556.1.5.119 NAME 'ipsecNegotiationPolicy' SUP ipsecBase STRUCTURAL MAY (iPSECNegotiationPolicyType $ iPSECNegotiationPolicyAction ) )",
            "( 1.2.840.113556.1.5.292 NAME 'msDS-AuthNPolicySilo' SUP top STRUCTURAL MAY (msDS-AssignedAuthNPolicySiloBL $ msDS-AuthNPolicySiloMembers $ msDS-UserAuthNPolicy $ msDS-ComputerAuthNPolicy $ msDS-ServiceAuthNPolicy $ msDS-AuthNPolicySiloEnforced ) )",
            "( 1.2.840.113556.1.5.121 NAME 'ipsecNFA' SUP ipsecBase STRUCTURAL MAY (ipsecNegotiationPolicyReference $ ipsecFilterReference ) )",
            "( 1.2.840.113556.1.5.42 NAME 'dfsConfiguration' SUP top STRUCTURAL )",
            "( 0.9.2342.********.100.4.9 NAME 'documentSeries' SUP top STRUCTURAL MUST (cn ) MAY (l $ o $ ou $ description $ telephoneNumber $ seeAlso ) )",
            "( 1.2.840.113556.1.5.271 NAME 'msDS-ResourceProperties' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.91 NAME 'linkTrackObjectMoveTable' SUP fileLinkTracking STRUCTURAL )",
            "( 1.2.840.113556.1.5.136 NAME 'rpcContainer' SUP container STRUCTURAL MAY (nameServiceFlags ) )",
            "( 1.2.840.113556.1.5.83 NAME 'rIDManager' SUP top STRUCTURAL MUST (rIDAvailablePool ) MAY (msDS-RIDPoolAllocationEnabled ) )",
            "( 1.2.840.113556.1.5.206 NAME 'msWMI-IntSetParam' SUP msWMI-RangeParam STRUCTURAL MUST (msWMI-IntDefault ) MAY (msWMI-IntValidValues ) )",
            "( 1.2.840.113556.********.5 NAME 'msDFSR-ReplicationGroup' SUP top STRUCTURAL MUST (msDFSR-ReplicationGroupType ) MAY (description $ msDFSR-Version $ msDFSR-Extension $ msDFSR-RootSizeInMb $ msDFSR-StagingSizeInMb $ msDFSR-ConflictSizeInMb $ msDFSR-TombstoneExpiryInMin $ msDFSR-FileFilter $ msDFSR-DirectoryFilter $ msDFSR-Schedule $ msDFSR-Flags $ msDFSR-Options $ msDFSR-DeletedSizeInMb $ msDFSR-DefaultCompressionExclusionFilter $ msDFSR-OnDemandExclusionFileFilter $ msDFSR-OnDemandExclusionDirectoryFilter $ msDFSR-Options2 ) )",
            "( 1.2.840.113556.1.5.125 NAME 'addressBookContainer' SUP top STRUCTURAL MUST (displayName ) MAY (purportedSearch ) )",
            "( 1.2.840.113556.1.5.7000.49 NAME 'applicationSettings' SUP top ABSTRACT MAY (applicationName $ notificationList $ msDS-Settings ) )",
            "( 1.2.840.113556.1.5.265 NAME 'msDS-OptionalFeature' SUP top STRUCTURAL MUST (msDS-OptionalFeatureGUID $ msDS-OptionalFeatureFlags ) MAY (msDS-RequiredDomainBehaviorVersion $ msDS-RequiredForestBehaviorVersion ) )",
            "( 1.2.840.113556.1.5.94 NAME 'serviceAdministrationPoint' SUP serviceConnectionPoint STRUCTURAL )",
            "( 1.2.840.113556.1.5.102 NAME 'nTFRSReplicaSet' SUP top STRUCTURAL MAY (fRSReplicaSetType $ fRSVersionGUID $ schedule $ fRSFileFilter $ fRSDirectoryFilter $ fRSDSPoll $ fRSServiceCommand $ fRSReplicaSetGUID $ fRSLevelLimit $ fRSRootSecurity $ fRSExtensions $ managedBy $ fRSFlags $ fRSPartnerAuthLevel $ fRSPrimaryMember $ msFRS-Topology-Pref $ msFRS-Hub-Member ) )",
            "( 1.2.840.113556.1.5.203 NAME 'msWMI-RangeParam' SUP top STRUCTURAL MUST (msWMI-PropertyName $ msWMI-TargetClass $ msWMI-TargetType ) )",
            "( 1.2.840.113556.1.5.7000.56 NAME 'ipsecBase' SUP top ABSTRACT MAY (ipsecName $ ipsecID $ ipsecDataType $ ipsecData $ ipsecOwnersReference ) )",
            "( 1.2.840.113556.********.3 NAME 'msDFSR-Subscription' SUP top STRUCTURAL MUST (msDFSR-ContentSetGuid $ msDFSR-ReplicationGroupGuid ) MAY (msDFSR-Extension $ msDFSR-RootPath $ msDFSR-RootSizeInMb $ msDFSR-StagingPath $ msDFSR-StagingSizeInMb $ msDFSR-ConflictPath $ msDFSR-ConflictSizeInMb $ msDFSR-Enabled $ msDFSR-Flags $ msDFSR-Options $ msDFSR-RootFence $ msDFSR-DfsLinkTarget $ msDFSR-DeletedPath $ msDFSR-DeletedSizeInMb $ msDFSR-ReadOnly $ msDFSR-CachePolicy $ msDFSR-MinDurationCacheInMin $ msDFSR-MaxAgeInCacheInMin $ msDFSR-OnDemandExclusionFileFilter $ msDFSR-OnDemandExclusionDirectoryFilter $ msDFSR-Options2 $ msDFSR-StagingCleanupTriggerInPercent ) )",
            "( 1.2.840.113556.1.5.223 NAME 'msPKI-PrivateKeyRecoveryAgent' SUP top STRUCTURAL MUST (userCertificate ) )",
            "( 1.2.840.113556.1.5.178 NAME 'pKIEnrollmentService' SUP top STRUCTURAL MAY (cACertificate $ dNSHostName $ cACertificateDN $ certificateTemplates $ signatureAlgorithms $ enrollmentProviders $ msPKI-Enrollment-Servers $ msPKI-Site-Name ) )",
            "( 1.2.840.113556.********.211 NAME 'msSFU30MailAliases' SUP top STRUCTURAL MAY (msSFU30Name $ msSFU30Aliases $ msSFU30NisDomain $ nisMapName ) )",
            "( 1.2.840.113556.1.5.53 NAME 'typeLibrary' SUP top STRUCTURAL MAY (cOMClassID $ cOMInterfaceID $ cOMUniqueLIBID ) )",
            "( 1.2.840.113556.********.8 NAME 'msDFSR-Topology' SUP top STRUCTURAL MAY (msDFSR-Extension $ msDFSR-Flags $ msDFSR-Options $ msDFSR-Options2 ) )",
            "( 1.2.840.113556.1.5.237 NAME 'msDS-AzScope' SUP top STRUCTURAL MUST (msDS-AzScopeName ) MAY (description $ msDS-AzApplicationData $ msDS-AzObjectGuid $ msDS-AzGenericData ) )",
            "( 1.2.840.113556.1.5.74 NAME 'categoryRegistration' SUP leaf STRUCTURAL MAY (localeID $ categoryId $ managedBy $ localizedDescription ) )",
            "( 1.2.840.113556.1.5.11 NAME 'comConnectionPoint' SUP connectionPoint STRUCTURAL MUST (cn ) MAY (marshalledInterface $ moniker $ monikerDisplayName ) )",
            "( 1.2.840.113556.1.5.93 NAME 'linkTrackOMTEntry' SUP leaf STRUCTURAL MAY (birthLocation $ oMTIndxGuid $ currentLocation $ timeRefresh $ oMTGuid ) )",
            "( 1.2.840.113556.1.5.10 NAME 'classRegistration' SUP leaf STRUCTURAL MAY (cOMInterfaceID $ cOMProgID $ cOMCLSID $ cOMTreatAsClassId $ cOMOtherProgId $ implementedCategories $ requiredCategories $ managedBy ) )",
            "( 1.2.840.113556.1.5.148 NAME 'siteLinkBridge' SUP top STRUCTURAL MUST (siteLinkList ) )",
            "( 1.2.840.113556.1.5.81 NAME 'rpcServer' SUP rpcEntry STRUCTURAL MAY (rpcNsObjectID $ rpcNsCodeset $ rpcNsEntryFlags ) )",
            "( 1.2.840.113556.1.3.46 NAME 'mailRecipient' SUP top AUXILIARY MUST (cn ) MAY (telephoneNumber $ userCertificate $ info $ garbageCollPeriod $ msExchAssistantName $ msExchLabeledURI $ showInAddressBook $ userCert $ legacyExchangeDN $ msDS-PhoneticDisplayName $ msDS-GeoCoordinatesAltitude $ msDS-GeoCoordinatesLatitude $ msDS-GeoCoordinatesLongitude $ userSMIMECertificate $ textEncodedORAddress $ secretary $ labeledURI ) )",
            "( 1.2.840.113556.1.5.1 NAME 'securityObject' SUP top ABSTRACT MUST (cn ) )",
            "( 1.2.840.113556.1.5.20 NAME 'leaf' SUP top ABSTRACT )",
            "( 1.2.840.113556.1.5.151 NAME 'intellimirrorSCP' SUP serviceAdministrationPoint STRUCTURAL MAY (netbootMachineFilePath $ netbootAllowNewClients $ netbootLimitClients $ netbootMaxClients $ netbootCurrentClientCount $ netbootAnswerRequests $ netbootAnswerOnlyValidClients $ netbootNewMachineNamingPolicy $ netbootNewMachineOU $ netbootIntelliMirrorOSes $ netbootTools $ netbootLocallyInstalledOSes $ netbootServer ) )",
            "( 1.2.840.113556.********.1 NAME 'msDFSR-LocalSettings' SUP top STRUCTURAL MAY (msDFSR-Version $ msDFSR-Extension $ msDFSR-Flags $ msDFSR-Options $ msDFSR-Options2 $ msDFSR-CommonStagingPath $ msDFSR-CommonStagingSizeInMb $ msDFSR-StagingCleanupTriggerInPercent ) )",
            "( 1.2.840.113556.1.5.186 NAME 'mS-SQL-SQLRepository' SUP top STRUCTURAL MAY (mS-SQL-Name $ mS-SQL-Contact $ mS-SQL-Build $ mS-SQL-Status $ mS-SQL-Version $ mS-SQL-Description $ mS-SQL-InformationDirectory ) )",
            "( ******* NAME 'organizationalRole' SUP top STRUCTURAL MUST (cn ) MAY (l $ st $ street $ ou $ postalAddress $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ telephoneNumber $ telexNumber $ teletexTerminalIdentifier $ facsimileTelephoneNumber $ x121Address $ internationalISDNNumber $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ roleOccupant $ seeAlso ) )",
            "( ******** NAME 'subSchema' SUP top STRUCTURAL MAY (extendedClassInfo $ extendedAttributeInfo $ dITContentRules $ attributeTypes $ objectClasses $ modifyTimeStamp ) )",
            "( 1.2.840.113556.1.5.284 NAME 'msDS-DeviceRegistrationService' SUP top STRUCTURAL MUST (msDS-IsEnabled $ msDS-DeviceLocation ) MAY (msDS-IssuerCertificates $ msDS-RegistrationQuota $ msDS-MaximumRegistrationInactivityPeriod $ msDS-IssuerPublicCertificates $ msDS-CloudIssuerPublicCertificates $ msDS-CloudIsEnabled ) )",
            "( 1.2.840.113556.1.5.84 NAME 'displaySpecifier' SUP top STRUCTURAL MAY (iconPath $ creationWizard $ contextMenu $ adminPropertyPages $ shellPropertyPages $ classDisplayName $ adminContextMenu $ shellContextMenu $ attributeDisplayNames $ treatAsLeaf $ createDialog $ createWizardExt $ scopeFlags $ queryFilter $ extraColumns $ adminMultiselectPropertyPages ) )",
            "( 1.2.840.113556.1.5.212 NAME 'msWMI-ShadowObject' SUP top STRUCTURAL MUST (msWMI-TargetObject ) )",
            "( 1.2.840.113556.1.5.59 NAME 'fileLinkTrackingEntry' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.4.2161 NAME 'msAuthz-CentralAccessPolicies' SUP top STRUCTURAL )",
            "( 1.2.840.113556.1.5.161 NAME 'mSMQQueue' SUP top STRUCTURAL MAY (mSMQQueueType $ mSMQJournal $ mSMQBasePriority $ mSMQLabel $ mSMQAuthenticate $ mSMQPrivacyLevel $ mSMQOwnerID $ mSMQTransactional $ mSMQQueueQuota $ mSMQQueueJournalQuota $ mSMQQueueNameExt $ mSMQLabelEx $ MSMQ-SecuredSource $ MSMQ-MulticastAddress ) )",
            "( 1.2.840.113556.1.5.193 NAME 'msCOM-Partition' SUP top STRUCTURAL MAY (msCOM-ObjectId ) )",
            "( 1.2.840.113556.1.5.118 NAME 'ipsecFilter' SUP ipsecBase STRUCTURAL )",
            "( ******* NAME 'country' SUP top STRUCTURAL MUST (c ) MAY (searchGuide $ co ) )",
            "( 1.2.840.113556.1.5.97 NAME 'physicalLocation' SUP locality STRUCTURAL MAY (managedBy ) )",
            "( 1.2.840.113556.1.3.30 NAME 'computer' SUP user STRUCTURAL MAY (cn $ networkAddress $ localPolicyFlags $ defaultLocalPolicyObject $ machineRole $ location $ netbootInitialization $ netbootGUID $ netbootMachineFilePath $ siteGUID $ operatingSystem $ operatingSystemVersion $ operatingSystemServicePack $ operatingSystemHotfix $ volumeCount $ physicalLocationObject $ dNSHostName $ policyReplicationFlags $ managedBy $ rIDSetReferences $ catalogs $ netbootSIFFile $ netbootMirrorDataFile $ msDS-AdditionalDnsHostName $ msDS-AdditionalSamAccountName $ msDS-ExecuteScriptPassword $ msDS-KrbTgtLink $ msDS-RevealedUsers $ msDS-NeverRevealGroup $ msDS-RevealOnDemandGroup $ msDS-RevealedList $ msDS-AuthenticatedAtDC $ msDS-isGC $ msDS-isRODC $ msDS-SiteName $ msDS-PromotionSettings $ msTPM-OwnerInformation $ msTSProperty01 $ msTSProperty02 $ msDS-IsUserCachableAtRodc $ msDS-HostServiceAccount $ msTSEndpointData $ msTSEndpointType $ msTSEndpointPlugin $ msTSPrimaryDesktopBL $ msTSSecondaryDesktopBL $ msTPM-TpmInformationForComputer $ msDS-GenerationId $ msImaging-ThumbprintHash $ msImaging-HashAlgorithm $ netbootDUID $ msSFU30Name $ msSFU30Aliases $ msSFU30NisDomain $ nisMapName ) )",
            "( *******.******* NAME 'nisNetgroup' SUP top STRUCTURAL MUST (cn ) MAY (description $ msSFU30Name $ msSFU30NisDomain $ msSFU30NetgroupHostAtDomain $ msSFU30NetgroupUserAtDomain $ memberNisNetgroup $ nisNetgroupTriple $ nisMapName ) )",
            "( 1.2.840.113556.1.5.153 NAME 'nTFRSMember' SUP top STRUCTURAL MAY (fRSUpdateTimeout $ fRSServiceCommand $ serverReference $ fRSRootSecurity $ fRSExtensions $ frsComputerReference $ fRSControlDataCreation $ fRSControlInboundBacklog $ fRSControlOutboundBacklog $ fRSFlags $ fRSPartnerAuthLevel ) )",
            "( ******** NAME 'applicationEntity' SUP top STRUCTURAL MUST (cn $ presentationAddress ) MAY (l $ o $ ou $ supportedApplicationContext $ seeAlso ) )",
            "( ******** NAME 'applicationProcess' SUP top STRUCTURAL MUST (cn ) MAY (l $ ou $ seeAlso ) )",
            "( 1.2.840.113556.1.5.279 NAME 'msDS-ValueType' SUP top STRUCTURAL MUST (msDS-ClaimValueType $ msDS-ClaimIsValueSpaceRestricted $ msDS-ClaimIsSingleValued $ msDS-IsPossibleValuesPresent ) )",
            "( 1.2.840.113556.1.5.204 NAME 'msWMI-UnknownRangeParam' SUP msWMI-RangeParam STRUCTURAL MUST (msWMI-NormalizedClass $ msWMI-TargetObject ) )",
            "( 1.2.840.113556.1.5.66 NAME 'domain' SUP top ABSTRACT MUST (dc ) )",
            "( ******** NAME 'dSA' SUP applicationEntity STRUCTURAL MAY (knowledgeInformation ) )",
            "( 1.2.840.113556.1.5.120 NAME 'ipsecISAKMPPolicy' SUP ipsecBase STRUCTURAL )"
        ],
        "objectGUID": [
            {
                "encoded": "sr4GScorekOq9Mmm+aY8Ow==",
                "encoding": "base64"
            }
        ],
        "systemFlags": [
            "134217728"
        ],
        "uSNChanged": [
            "5"
        ],
        "uSNCreated": [
            "5"
        ],
        "whenChanged": [
            "20130521164433.0Z"
        ],
        "whenCreated": [
            "20130521164433.0Z"
        ]
    },
    "schema_entry": "CN=Aggregate,CN=Schema,CN=Configuration,DC=AD2012,DC=LAB",
    "type": "SchemaInfo"
}
"""
ad_2012_r2_dsa_info = """
{
    "raw": {
        "configurationNamingContext": [
            "CN=Configuration,DC=AD2012,DC=LAB"
        ],
        "currentTime": [
            "20141111080100.0Z"
        ],
        "defaultNamingContext": [
            "DC=AD2012,DC=LAB"
        ],
        "dnsHostName": [
            "WIN1.AD2012.LAB"
        ],
        "domainControllerFunctionality": [
            "6"
        ],
        "domainFunctionality": [
            "6"
        ],
        "dsServiceName": [
            "CN=NTDS Settings,CN=WIN1,CN=Servers,CN=Default-First-Site-Name,CN=Sites,CN=Configuration,DC=AD2012,DC=LAB"
        ],
        "forestFunctionality": [
            "6"
        ],
        "highestCommittedUSN": [
            "22591"
        ],
        "isGlobalCatalogReady": [
            "TRUE"
        ],
        "isSynchronized": [
            "TRUE"
        ],
        "ldapServiceName": [
            "AD2012.LAB:win1$@AD2012.LAB"
        ],
        "namingContexts": [
            "DC=AD2012,DC=LAB",
            "CN=Configuration,DC=AD2012,DC=LAB",
            "CN=Schema,CN=Configuration,DC=AD2012,DC=LAB",
            "DC=DomainDnsZones,DC=AD2012,DC=LAB",
            "DC=ForestDnsZones,DC=AD2012,DC=LAB"
        ],
        "rootDomainNamingContext": [
            "DC=AD2012,DC=LAB"
        ],
        "schemaNamingContext": [
            "CN=Schema,CN=Configuration,DC=AD2012,DC=LAB"
        ],
        "serverName": [
            "CN=WIN1,CN=Servers,CN=Default-First-Site-Name,CN=Sites,CN=Configuration,DC=AD2012,DC=LAB"
        ],
        "subschemaSubentry": [
            "CN=Aggregate,CN=Schema,CN=Configuration,DC=AD2012,DC=LAB"
        ],
        "supportedCapabilities": [
            "1.2.840.113556.1.4.800",
            "1.2.840.11355*******670",
            "1.2.840.11355*******791",
            "1.2.840.11355*******935",
            "1.2.840.113556.1.4.2080",
            "1.2.840.113556.1.4.2237"
        ],
        "supportedControl": [
            "1.2.840.113556.1.4.319",
            "1.2.840.113556.1.4.801",
            "1.2.840.113556.1.4.473",
            "1.2.840.113556.1.4.528",
            "1.2.840.113556.1.4.417",
            "1.2.840.113556.1.4.619",
            "1.2.840.113556.1.4.841",
            "1.2.840.113556.1.4.529",
            "1.2.840.113556.1.4.805",
            "1.2.840.113556.1.4.521",
            "1.2.840.113556.1.4.970",
            "1.2.840.11355*******338",
            "1.2.840.113556.1.4.474",
            "1.2.840.11355*******339",
            "1.2.840.11355*******340",
            "1.2.840.11355*******413",
            "2.16.840.1.113730.3.4.9",
            "2.16.840.1.113730.3.4.10",
            "1.2.840.11355*******504",
            "1.2.840.11355*******852",
            "1.2.840.113556.1.4.802",
            "1.2.840.11355*******907",
            "1.2.840.11355*******948",
            "1.2.840.11355*******974",
            "1.2.840.11355*******341",
            "1.2.840.113556.1.4.2026",
            "1.2.840.113556.1.4.2064",
            "1.2.840.113556.1.4.2065",
            "1.2.840.113556.1.4.2066",
            "1.2.840.113556.1.4.2090",
            "1.2.840.113556.1.4.2205",
            "1.2.840.113556.1.4.2204",
            "1.2.840.113556.1.4.2206",
            "1.2.840.113556.1.4.2211",
            "1.2.840.113556.1.4.2239",
            "1.2.840.113556.1.4.2255",
            "1.2.840.113556.1.4.2256"
        ],
        "supportedExtension": [
            "*******.4.1.1466.20037",
            "*******.4.1.1466.101.119.1",
            "1.2.840.11355*******781",
            "*******.4.1.4203.1.11.3",
            "1.2.840.113556.1.4.2212"
        ],
        "supportedLDAPPolicies": [
            "MaxPoolThreads",
            "MaxPercentDirSyncRequests",
            "MaxDatagramRecv",
            "MaxReceiveBuffer",
            "InitRecvTimeout",
            "MaxConnections",
            "MaxConnIdleTime",
            "MaxPageSize",
            "MaxBatchReturnMessages",
            "MaxQueryDuration",
            "MaxTempTableSize",
            "MaxResultSetSize",
            "MinResultSets",
            "MaxResultSetsPerConn",
            "MaxNotificationPerConn",
            "MaxValRange",
            "MaxValRangeTransitive",
            "ThreadMemoryLimit",
            "SystemMemoryLimitPercent"
        ],
        "supportedLDAPVersion": [
            "3",
            "2"
        ],
        "supportedSASLMechanisms": [
            "GSSAPI",
            "GSS-SPNEGO",
            "EXTERNAL",
            "DIGEST-MD5"
        ]
    },
    "type": "DsaInfo"
}
"""
