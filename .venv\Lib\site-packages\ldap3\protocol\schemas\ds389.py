"""
"""

# Created on 2014.11.11
#
# Author: <PERSON>
#
# Copyright 2014 - 2020 <PERSON>
#
# This file is part of ldap3.
#
# ldap3 is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published
# by the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# ldap3 is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with ldap3 in the COPYING and COPYING.LESSER files.
# If not, see <http://www.gnu.org/licenses/>.

ds389_1_3_3_schema = """
{
    "raw": {
        "aci": [
            "(target=\\"ldap:///cn=schema\\")(targetattr !=\\"aci\\")(version 3.0;acl \\"anonymous, no acis\\"; allow (read, search, compare) userdn = \\"ldap:///anyone\\";)"
        ],
        "attributeTypes": [
            "( 2.16.840.1.113730.3.1.582 NAME 'nsDS5ReplicaCredentials' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.15953.9.1.1 NAME 'sudoUser' DESC 'User(s) who may  run sudo' EQUALITY caseExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'SUDO' )",
            "( 2.16.840.1.113730.3.1.2274 NAME 'nsslapd-instancedir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.532 NAME 'ntUserCountryCode' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( ********.2.4.1139 NAME 'printer-info' DESC 'Descriptive information about this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.34 NAME 'ref' DESC 'LDAP referrals attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'LDAPv3 referrals Internet Draft' )",
            "( *******.4.1.13769.2.4 NAME ( 'nsAIMid' 'nscpaimscreenname' )  EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'Mozilla Address Book' )",
            "( sslVersionMin-oid NAME 'sslVersionMin' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.204 NAME 'replicaNickName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2243 NAME 'nsslapd-securelistenhost' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2108 NAME 'nsPagedLookThroughLimit' DESC 'Binder-based simple paged search operation look through limit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN '389' )",
            "( *******.4.1.6981.11.3.7 NAME 'FTPStatus' DESC 'Account status: enabled or disabled' EQUALITY booleanMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.2091 NAME 'nsslapd-suffix' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( ******** NAME 'houseIdentifier'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( nsUserRDNComponent-oid NAME 'nsUserRDNComponent' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( ********.2.4.1117 NAME 'printer-media-local-supported' DESC 'Site-specific names of media supported by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2301 NAME 'nsslapd-plugin-logging' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.3 NAME ( 'mail' 'rfc822mailbox' )  EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' X-DEPRECATED 'rfc822mailbox' )",
            "( 2.16.840.1.113730.3.1.607 NAME 'nsDS5Flags' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsTaskLabel-oid NAME 'nsTaskLabel' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2068 NAME 'pamExcludeSuffix' DESC 'Suffixes to exclude from PAM authentication' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.2157 NAME 'dnaRemoteBindCred' DESC 'Remote bind credentials' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( nsBindDN-oid NAME 'nsBindDN' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( ******** NAME 'postOfficeBox'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2261 NAME 'nsslapd-attribute-name-exceptions' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.*********.2 NAME 'multiLineDescription' DESC 'Pilot attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Internet White Pages Pilot' )",
            "( 2.16.840.1.113730.3.1.102 NAME ( 'passwordChange' 'pwdAllowUserChange' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.527 NAME 'ntUserLastLogoff' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( *******.******** NAME 'shadowExpire' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.21 NAME 'mailQuota' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 1.2.840.113556.1.4.482 NAME 'calOtherCalURIs' DESC 'RFC2739: multi-value URI for snapshots of other calendars' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc2739' )",
            "( 2.16.840.1.113730.3.1.2238 NAME 'nsslapd-security' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.********.27.4.1.6 NAME 'javaClassName' DESC 'Fully qualified name of distinguished Java class or interface' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2713' )",
            "( 2.16.840.1.113730.3.1.240 NAME 'replicatedattributelist' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2175 NAME 'nsslapd-accesslog-logrotationsync-enabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsRevisionNumber-oid NAME 'nsRevisionNumber' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2207 NAME 'nsslapd-rootdn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsHelpRef-oid NAME 'nsHelpRef' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.43 NAME 'ntUserDeleteAccount' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.217 NAME 'replicaCFUpdated' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.6 NAME 'targetDn' DESC 'Changelog attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Changelog Internet Draft' )",
            "( 2.5.4.25 NAME 'internationalISDNNumber'  EQUALITY numericStringMatch SUBSTR numericStringSubstringsMatch SYNTAX *******.4.1.14************.36 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.998 NAME ( 'passwordGraceUserTime' 'pwdGraceUserTime' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2225 NAME 'nsslapd-workingdir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******* NAME 'shadowLastChange' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 0.9.2342.********.100.1.11 NAME 'documentIdentifier'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.65 NAME 'ntUserLogonServer' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.781 NAME 'mgrpAddHeader' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.2295 NAME 'nsslapd-allowed-sasl-mechanisms' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2162 NAME 'winSyncDirectoryFilter' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.15953.9.1.9 NAME 'sudoNotAfter' DESC 'End of time interval for which the entry is valid' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.14************.24 X-ORIGIN 'SUDO' )",
            "( ********.2.4.1121 NAME 'printer-resolution-supported' DESC 'List of resolutions supported for printing documents by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2139 NAME 'winSyncMoveAction' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsExpirationDate-oid NAME 'nsExpirationDate' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******.4.1.5923.******* NAME 'eduPersonAffiliation' DESC 'Affiliation' SYNTAX *******.4.1.1466.************ X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( nsVendor-oid NAME 'nsVendor' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.87 NAME 'cirUpdateSchedule' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******2 NAME 'owner'  SUP distinguishedName EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.253 NAME 'nsValueSyntax' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( nsLdapSchemaVersion-oid NAME 'nsLdapSchemaVersion' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2100 NAME 'autoMemberInclusiveRegex' DESC 'Auto Membership inclusive regex rule' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2089 NAME 'mepMappedAttr' DESC 'Managed Entries mapped attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2212 NAME 'nsslapd-useroc' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2282 NAME 'nsslapd-rundir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.3.3 NAME 'mozillaHomeLocalityName'  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( ******** NAME ( 'o' 'organizationname' )  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'organizationname' )",
            "( 2.16.840.1.113730.3.1.2259 NAME 'nsslapd-return-exact-case' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsAdminAccessAddresses-oid NAME 'nsAdminAccessAddresses' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 0.9.2342.********.100.1.45 NAME 'organizationalStatus'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( nsAdminUsers-oid NAME 'nsAdminUsers' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.******** NAME 'oncRpcNumber' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.19 NAME 'mailMessageStore' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.221 NAME 'passwordStorageScheme' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2126 NAME 'dnaHostname' DESC 'DNA hostname of replica to get new range of values' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2230 NAME 'nsslapd-ldapiautobind' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2196 NAME 'nsslapd-accesslog-logexpirationtime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.576 NAME 'nsRoleFilter' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.70 NAME 'serverRoot' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.******* NAME 'trustModel' DESC 'Access scheme' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'nss_ldap/pam_ldap' )",
            "( 2.16.840.1.113730.3.1.248 NAME 'nsValueDN' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( *******.4.1.1466.101.120.41 NAME 'parentOrganization'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape' )",
            "( *******.4.1.15953.9.1.4 NAME 'sudoRunAs' DESC 'User(s) impersonated by sudo' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ X-ORIGIN 'SUDO' )",
            "( nsAdminEnableDSGW-oid NAME 'nsAdminEnableDSGW' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( ********.2.4.1132 NAME 'printer-multiple-document-jobs-supported' DESC 'Indicates whether or not this printer supports more than one document per job.' EQUALITY booleanMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( *******.4.1.13769.2.1 NAME ( 'mozillaNickname' 'xmozillanickname' )  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'Mozilla Address Book' )",
            "( ******** NAME 'modifyTimestamp'  EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.14************.24 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.92 NAME ( 'passwordExpWarned' 'pwdExpirationWarned' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2246 NAME 'nsslapd-maxdescriptors' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2113 NAME 'internalModifiersName' DESC 'plugin dn' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2094 NAME 'nsslapd-parent-suffix' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.692 NAME 'inetUserStatus' DESC '\\"active\\", \\"inactive\\", or \\"deleted\\" status of a user' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape subscriber interoperability' )",
            "( ********.2.4.1110 NAME 'printer-job-priority-supported' DESC 'Indicates the number of job priority levels supported by this printer.' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2183 NAME 'nsslapd-audit-logrotationsyncmin' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2304 NAME 'nsslapd-dynamic-plugins' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.789 NAME 'mgrpNoDuplicateChecks' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.602 NAME 'entrydn' DESC 'internal server defined attribute type' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113730.3.1.1098 NAME 'nsds5replicaSessionPauseTime' DESC 'Netscape defined attribute type' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2073 NAME 'pamSecure' DESC 'Require secure (TLS/SSL) connection for PAM auth' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.2264 NAME 'nsslapd-max-filter-nest-level' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.21.2 NAME 'dITContentRules'  EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 0.9.2342.********.100.1.56 NAME 'documentPublisher'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.522 NAME 'ntUserComment' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( ********.2.4.1129 NAME 'printer-color-supported' DESC 'Indicates whether this printer is capable of any type of color printing at all, including highlight color.' EQUALITY booleanMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.24 NAME 'mailRoutingAddress' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( nsmsgDisallowAccess-oid NAME 'nsmsgDisallowAccess' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 1.2.840.113556.1.4.485 NAME 'calOtherCalAdrURIs' DESC 'RFC2739: multi-value URI to other request destinations' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc2739' )",
            "( 2.16.840.1.113730.3.1.2131 NAME 'pamFilter' DESC 'Filter to match entries that should use PAM authentication' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.234 NAME 'nsSNMPLocation' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.1466.101.120.15 NAME 'supportedLDAPVersion'  SYNTAX *******.4.1.1466.************ USAGE dSAOperation X-ORIGIN 'RFC 4512' )",
            "( *******.4.1.5923.******* NAME 'eduPersonScopedAffiliation' DESC 'Scoped Affiliation' SYNTAX *******.4.1.1466.************ X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( nsHostLocation-oid NAME 'nsHostLocation' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.590 NAME 'nsDS5ReplicaName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2178 NAME 'nsslapd-accesslog-logrotationsynchour' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2081 NAME ( 'passwordMaxRepeats' 'pwdMaxRepeats' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.687 NAME 'nsds5replicaChangesSentSinceStartup' DESC 'Netscape defined attribute type' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1107 NAME 'printer-xri-supported' DESC 'The unordered list of XRI (extended resource identifiers) supported by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.46 NAME 'ntGroupDeleteGroup' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.9 NAME 'newRdn' DESC 'Changelog attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Changelog Internet Draft' )",
            "( 2.16.840.1.113730.3.1.2147 NAME 'rootdn-allow-host' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2251 NAME 'nsslapd-accesscontrol' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.1466.101.120.6 NAME 'altServer'  SYNTAX *******.4.1.1466.************ USAGE dSAOperation X-ORIGIN 'RFC 4512' )",
            "( ******** NAME 'preferredDeliveryMethod'  SYNTAX *******.4.1.14************.14 SINGLE-VALUE X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.11 NAME 'newSuperior' DESC 'Changelog attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Changelog Internet Draft' )",
            "( 2.16.840.1.113730.3.1.229 NAME 'nsslapd-pluginVendor' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2228 NAME 'nsslapd-ldapifilepath' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.1.1.1.8 NAME 'shadowWarning' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.5.4.47 NAME 'enhancedSearchGuide'  SYNTAX *******.4.1.14************.21 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.68 NAME 'ntUserPasswordExpired' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2298 NAME 'nsslapd-enable-turbo-mode' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.585 NAME 'nsDS5ReplicatedAttributeList' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2165 NAME 'schemaUpdateObjectclassAccept' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2277 NAME 'nsslapd-tmpdir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.1002 NAME 'nsds7NewWinUserSyncEnabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.23 NAME 'lastModifiedTime' DESC 'old variant of modifyTimestamp' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 1274' )",
            "( 2.16.840.1.113730.3.1.110 NAME 'ntGroupId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.535 NAME 'ntUserHomeDirDrive' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.33 NAME 'mgrpModerator' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.207 NAME 'vlvBase' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsServerMigrationClassname-oid NAME 'nsServerMigrationClassname' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( nsSSLPersonalitySSL-oid NAME 'nsSSLPersonalitySSL' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******5 NAME 'userPassword'  EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( *******.1.4 NAME 'vendorName'  EQUALITY *******.4.1.1466.109.114.1 SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE dSAOperation X-ORIGIN 'RFC 3045' )",
            "( *******.4.1.6981.11.3.4 NAME 'FTPDownloadRatio' DESC 'Ratio (compared with FTPRatioUp) for downloaded files' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.801 NAME 'mgrpRemoveHeader' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.2215 NAME 'nsslapd-allow-unauthenticated-binds' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1118 NAME 'printer-copies-supported' DESC 'The maximum number of copies of a document that may be printed as a single job on this printer.' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.55 NAME 'aci' DESC 'Netscape defined access control information attribute type' SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2285 NAME 'nsslapd-hash-filters' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.6 NAME 'roomNumber'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.5.4.7 NAME ( 'l' 'locality' 'localityname' )  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'locality localityname' )",
            "( nsSSL3SessionTimeout-oid NAME 'nsSSL3SessionTimeout' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2152 NAME 'nsds5ReplicaProtocolTimeout' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.105 NAME ( 'passwordLockout' 'pwdLockOut' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******** NAME 'ipServicePort' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2129 NAME 'dnaNextRange' DESC 'DNA range of values to get from replica' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( nsSSL3-oid NAME 'nsSSL3' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2199 NAME 'nsslapd-accesslog-logexpirationtimeunit' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.571 NAME 'nsSizeLimit' DESC 'Binder-based search operation size limit (entries)' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.77 NAME 'changeTime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.243 NAME 'nsValueCIS' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( 2.16.840.1.113730.3.1.2170 NAME 'nsslapd-accesslog-level' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2202 NAME 'nsslapd-accesslog-logging-enabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.*******3 NAME 'automountInformation' DESC 'Information used by the autofs automounter' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'draft-howard-rfc2307bis' )",
            "( 2.16.840.1.113730.3.1.1 NAME 'carLicense' DESC 'vehicle license or registration plate' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2798' )",
            "( nsCertConfig-oid NAME 'nsCertConfig' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Certificate Management System' )",
            "( 2.16.840.1.113730.3.1.99 NAME ( 'passwordMinLength' 'pwdMinLength' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2249 NAME 'nsslapd-idletimeout' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.4.20 NAME 'telephoneNumber'  EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2116 NAME 'dnaPrefix' DESC 'DNA string prefix for dna value' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2220 NAME 'nsslapd-minssf-exclude-rootdse' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******* NAME 'uidNumber' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2186 NAME 'nsslapd-auditlog-logrotationtime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.12 NAME 'documentTitle'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.60 NAME 'ntUserAuthFlags' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2290 NAME 'nsslapd-disk-monitoring-threshold' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2076 NAME ( 'passwordMinAlphas' 'pwdMinAlphas' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.406 NAME 'nsSynchUserIDFormat' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.21.5 NAME 'attributeTypes'  EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( ********.2.4.1122 NAME 'printer-media-supported' DESC 'The standard names/types/sizes (and optional color suffixes) of the media supported by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( nsAdminEnableEnduser-oid NAME 'nsAdminEnableEnduser' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.******** NAME 'nisMapName' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2134 NAME 'nsds5ReplicaStripAttrs' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.544 NAME 'nsParentUniqueId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.5923.******* NAME 'eduPersonOrgUnitDN' DESC 'Organizational Unit DN' SYNTAX *******.4.1.1466.************ X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( 2.16.840.1.113730.3.1.82 NAME 'cirBindDn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.60 NAME 'jpegPhoto' DESC 'a JPEG image' SYNTAX *******.4.1.14************.28 X-ORIGIN 'RFC 2798' )",
            "( *******.********.********* NAME 'javaClassNames' DESC 'Fully qualified Java class or interface name' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2713' )",
            "( 2.16.840.1.113730.3.1.2103 NAME 'autoMemberDisabled' DESC 'Auto Membership disabled attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.809 NAME 'nsds5replicaLastInitStatus' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2084 NAME 'nsSymmetricKey' DESC 'A symmetric key - currently used by attribute encryption' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'attribute encryption' )",
            "( 2.16.840.1.113730.3.1.682 NAME 'nsds5ReplicaPurgeDelay' DESC 'Netscape defined attribute type' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.39 NAME 'homePostalAddress'  EQUALITY caseIgnoreListMatch SUBSTR caseIgnoreListSubstringsMatch SYNTAX *******.4.1.14************.41 X-ORIGIN 'RFC 4524' )",
            "( nsTLS1-oid NAME 'nsTLS1' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2063 NAME 'nsEncryptionAlgorithm' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.3.4 NAME 'mozillaHomeState'  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( ******** NAME 'description'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2254 NAME 'nsslapd-pwpolicy-local' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2121 NAME 'dnaScope' DESC 'DNA base DN for finding entries' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.14 NAME 'mailAutoReplyMode' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.224 NAME 'nsslapd-pluginPath' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.********.******* NAME 'acctPolicySubentry' DESC 'Account policy pointer' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Account Policy Plugin' )",
            "( 2.16.840.1.113730.3.1.2191 NAME 'nsslapd-errorlog-logmaxdiskspace' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2233 NAME 'nsslapd-ldapiuidnumbertype' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.579 NAME 'nsDS5ReplicaPort' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.610 NAME 'nsAccountLock' DESC 'Operational attribute for Account Inactivation' SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.707 NAME 'vacationstartdate' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.580 NAME 'nsDS5ReplicaTransportInfo' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2168 NAME 'schemaUpdateAttributeReject' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.15953.9.1.3 NAME 'sudoCommand' DESC 'Command(s) to be executed by sudo' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ X-ORIGIN 'SUDO' )",
            "( ********.2.4.1137 NAME 'printer-generated-natural-language-supported' DESC 'Natural language(s) supported for this directory entry.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.1005 NAME 'nsds7DirsyncCookie' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.24 NAME 'lastModifiedBy' DESC 'old variant of modifiersName' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 1274' )",
            "( 2.16.840.1.113730.3.1.530 NAME 'ntUserLogonHours' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.36 NAME 'nsLicensedFor' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.202 NAME 'replicaCredentials' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.2.2 NAME ( 'mozillaSecondEmail' 'xmozillasecondemail' )  EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.3023 NAME 'nsViewFilter' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsSSL2Ciphers-oid NAME 'nsSSL2Ciphers' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( nsServerAddress-oid NAME 'nsServerAddress' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.91 NAME 'passwordExpirationTime' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.14************.24 SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2241 NAME 'nsslapd-errorlog' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsProductName-oid NAME 'nsProductName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2027 NAME 'nsruvReplicaLastModified' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'authorityRevocationList' DESC 'X.509 authority revocation list' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4523' )",
            "( 2.16.840.1.113730.3.1.2097 NAME 'autoMemberScope' DESC 'Auto Membership scope criteria' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.695 NAME 'inetSubscriberChallenge' DESC 'Used to confirm subscriberIdentity.  This attribute holds the challenge phrase and is used in conjunction with the inetSubscriberResponse' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape subscriber interoperability' )",
            "( 2.16.840.1.113730.3.1.2218 NAME 'nsslapd-localssf' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1115 NAME 'printer-stacking-order-supported' DESC 'The possible stacking order of pages as they are printed and ejected from this printer.' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 1.2.840.113556.1.4.479 NAME 'calFBURL' DESC 'RFC2739: URI to the users default freebusy data' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc2739' )",
            "( 2.16.840.1.113730.3.1.2307 NAME 'nsslapd-allow-hashed-passwords' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.58 NAME 'replicaBindDn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.605 NAME 'entryid' DESC 'internal server defined attribute type' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113730.3.1.2288 NAME 'nsslapd-defaultnamingcontext' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.3.9 NAME 'mozillaWorkUrl'  EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.2155 NAME 'nsds5ReplicaBackoffMax' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2267 NAME 'nsslapd-certmap-basedn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.100 NAME 'passwordKeepHistory' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.525 NAME 'ntUserWorkstations' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.23 NAME 'mgrpAllowedDomain' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 1.2.840.113556.1.4.480 NAME 'calCAPURI' DESC 'RFC2739: URI used to communicate with the users calendar' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc2739' )",
            "( 2.16.840.1.113730.3.1.237 NAME 'nsSNMPMasterHost' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsDefaultAcceptLanguage-oid NAME 'nsDefaultAcceptLanguage' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.593 NAME 'nsSNMPName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2205 NAME 'nsslapd-auditlog-logging-hide-unhashed-pw' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1108 NAME 'printer-aliases' DESC 'List of site-specific administrative names of this printer in addition to the value specified for printer-name.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.45 NAME 'ntGroupCreateNewGroup' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.215 NAME 'oid' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2142 NAME 'nsSaslMapPriority' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.4 NAME 'employeeType' DESC 'type of employment for a person' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2798' )",
            "( 2.16.840.1.113730.3.1.2119 NAME 'dnaMagicRegen' DESC 'DNA value that will trigger regeneration of attribute value' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.5.4.42 NAME 'givenName'  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2189 NAME 'nsslapd-auditlog-logrotationtimeunit' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.67 NAME 'ntUserProfile' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2079 NAME ( 'passwordMinSpecials' 'pwdMinSpecials' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.588 NAME 'nsDS5ReplicaId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2160 NAME 'dnaRemoteBindMethod' DESC 'Remote bind method: SIMPLE, SSL, SASL/DIGEST-MD5, or SASL/GSSAPI' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( *******.4.1.13769.4.3 NAME 'mozillaCustom3'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.2272 NAME 'nsslapd-plugin-binddn-tracking' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'matchingRuleUse'  EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( *******.*********.57 NAME ( 'labeledURI' 'labeledurl' )  EQUALITY caseExactMatch SUBSTR caseExactSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2079' X-DEPRECATED 'labeledurl' )",
            "( *******.******** NAME 'bootParameter' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.89 NAME 'cirSyncInterval' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.251 NAME 'nsValueFlags' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( 2.16.840.1.113730.3.1.2106 NAME 'nsIDListScanLimit' DESC 'Binder-based search operation ID list scan limit (candidate entries)' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN '389' )",
            "( *******.4.1.6981.11.3.1 NAME 'FTPQuotaFiles' DESC 'Quota (in number of files) for an FTP user' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.804 NAME 'nsSchemaCSN' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( nsServerSecurity-oid NAME 'nsServerSecurity' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2210 NAME 'nsslapd-auditlog' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.5 NAME ( 'drink' 'favouriteDrink' )  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' X-DEPRECATED 'favouriteDrink' )",
            "( 2.16.840.1.113730.3.1.50 NAME 'replicaBeginOrc' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2280 NAME 'nsslapd-bakdir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.4.4 NAME ( 'sn' 'surName' )  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'surName' )",
            "( 2.16.840.1.113730.3.1.2066 NAME 'nsSaslMapFilterTemplate' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.3.1 NAME 'mozillaHomeStreet'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.198 NAME 'memberURL' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******6 NAME 'postalAddress'  EQUALITY caseIgnoreListMatch SUBSTR caseIgnoreListSubstringsMatch SYNTAX *******.4.1.14************.41 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.108 NAME 'passwordUnlock' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.*******6 NAME 'ipServiceProtocol' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( nsSSLClientAuth-oid NAME 'nsSSLClientAuth' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2124 NAME 'dnaRemainingValues' DESC 'DNA remaining values left to assign' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2236 NAME 'nsslapd-anonlimitsdn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2194 NAME 'nsslapd-errorlog-logminfreediskspace' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.574 NAME 'nsRole' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( nsAdminGroupName-oid NAME 'nsAdminGroupName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.72 NAME 'serverVersionNumber' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.246 NAME 'nsValueInt' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( *******.4.1.1466.101.120.43 NAME 'preferredTimeZone' DESC 'preferred time zone for a person' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2173 NAME 'nsslapd-errorlog-maxlogsize' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.15953.9.1.6 NAME 'sudoRunAsUser' DESC 'User(s) impersonated by sudo' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ X-ORIGIN 'SUDO' )",
            "( ********.2.4.1130 NAME 'printer-document-format-supported' DESC 'The possible source document formats which may be interpreted and printed by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.552 NAME 'costargettree' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.94 NAME 'retryCountResetTime' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.14************.24 SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.4.23 NAME ( 'facsimileTelephoneNumber' 'fax' )  SYNTAX *******.4.1.14************.22 X-ORIGIN 'RFC 4519' X-DEPRECATED 'fax' )",
            "( 2.16.840.1.113730.3.1.2244 NAME 'nnslapd-threadnumber' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 1.2.840.113556.1.2.102 NAME 'memberOf' DESC 'Group that the entry belongs to' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Delegated Administrator' )",
            "( 2.16.840.1.113730.3.1.2111 NAME 'tombstoneNumSubordinates' DESC 'count of immediate subordinates for tombstone entries' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN '389 directory server' )",
            "( nsDirectoryURL-oid NAME 'nsDirectoryURL' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.690 NAME 'inetDomainBaseDN' DESC 'Base DN of user subtree for a DNS domain' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape subscriber interoperability' )",
            "( 2.16.840.1.113730.3.1.2223 NAME 'nsslapd-localhost' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******* NAME 'homeDirectory' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2181 NAME 'nsslapd-accesslog-logrotationsyncmin' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.569 NAME 'cosPriority' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsOsVersion-oid NAME 'nsOsVersion' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( nsJarfilename-oid NAME 'nsJarfilename' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2293 NAME 'nsslapd-ndn-cache-enabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2071 NAME 'pamIDAttr' DESC 'Name of attribute holding PAM ID' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.2158 NAME 'dnaRemoteBindDN' DESC 'Remote bind DN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( ********.2.4.1127 NAME 'printer-pages-per-minute' DESC 'The nominal number of pages per minute which may be output by this printer.' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 0.9.2342.********.100.1.54 NAME 'ditRedirect' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 1274' )",
            "( 2.16.840.1.113730.3.1.520 NAME 'nswmExtendedUserPrefs' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.26 NAME 'mgrpErrorsTo' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( *******.4.1.4203.1.3.5 NAME 'supportedFeatures'  EQUALITY objectIdentifierMatch SYNTAX *******.4.1.14************.38 USAGE dSAOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.232 NAME 'nsSNMPEnabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2137 NAME 'nsds5ReplicaAbortCleanRUV' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.1466.101.120.17 NAME 'ldapSchemas'  SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 2927' )",
            "( *******.4.1.5923.******* NAME 'eduPersonEntitlement' DESC 'Entitlement' SYNTAX *******.4.1.1466.************ X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( 2.16.840.1.113730.3.1.81 NAME 'cirPort' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.6981.11.3.9 NAME 'FTPgid' DESC 'System uid (overrides gidNumber if present)' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.2087 NAME 'mepManagedEntry' DESC 'Managed Entries pointer' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.685 NAME 'nsds5replicaLastUpdateStart' DESC 'Netscape defined attribute type' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.14************.24 SINGLE-VALUE NO-USER-MODIFICATION X-ORIGIN 'Netscape Directory Server' )",
            "( nsAdminSIEDN-oid NAME 'nsAdminSIEDN' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2208 NAME 'nsslapd-rootdnpw' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.48 NAME 'replicaPort' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.210 NAME 'vlvSort' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2145 NAME 'rootdn-close-time' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2257 NAME 'nsslapd-accesslog-logbuffering' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.43 NAME ( 'co' 'friendlycountryname' )  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' X-DEPRECATED 'friendlycountryname' )",
            "( 2.16.840.1.113730.3.1.13 NAME 'mailAlternateAddress' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.227 NAME 'nsslapd-pluginId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'x500UniqueIdentifier'  EQUALITY bitStringMatch SYNTAX *******.4.1.14************.6 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.613 NAME 'copiedFrom' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( nsServerPort-oid NAME 'nsServerPort' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******.********.******** NAME 'javaSerializedData' DESC 'Serialized form of a Java object' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2713' )",
            "( 2.16.840.1.113730.3.1.583 NAME 'nsDS5ReplicaBindMethod' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2275 NAME 'nsslapd-schemadir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsSSLActivation-oid NAME 'nsSSLActivation' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.1000 NAME 'nsds7WindowsReplicaSubtree' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.21 NAME 'secretary'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.533 NAME 'ntUserCodePage' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( ********.2.4.1138 NAME 'printer-make-and-model' DESC 'Make and model of this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.35 NAME 'changeLog' DESC 'the distinguished name of the entry which contains the set of entries comprising this servers changelog' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'Changelog Internet Draft' )",
            "( 2.16.840.1.113730.3.1.205 NAME 'changeLogMaximumConcurrentWrites' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsDirectoryInfoRef-oid NAME 'nsDirectoryInfoRef' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******.4.1.6981.11.3.6 NAME 'FTPDownloadBandwidth' DESC 'Bandwidth (in KB/s) to limit download speeds to' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.2109 NAME 'nsPagedIDListScanLimit' DESC 'Binder-based simple paged search operation ID list scan limit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN '389' )",
            "( 2.16.840.1.113730.3.1.2092 NAME 'nsslapd-ldapiautodnsuffix' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******2 NAME 'supportedAlgorithms' DESC 'X.509 supported algorithms' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4523' )",
            "( ********.2.4.1116 NAME 'printer-output-features-supported' DESC 'The possible output features supported by this printer.' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2302 NAME 'nsslapd-listen-backlog-size' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.57 NAME 'replicaRoot' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.608 NAME 'nsDS5Task' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( ******* NAME ( 'street' 'streetaddress' )  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'streetaddress' )",
            "( 2.16.840.1.113730.3.1.2069 NAME 'pamMissingSuffix' DESC 'How to handle missing include or exclude suffixes' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.2150 NAME 'rootdn-deny-ip' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsGroupRDNComponent-oid NAME 'nsGroupRDNComponent' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.2262 NAME 'nsslapd-maxbersize' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'physicalDeliveryOfficeName'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.528 NAME 'ntUserAcctExpires' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.103 NAME ( 'passwordCheckSyntax' 'pwdCheckSyntax' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******** NAME 'memberNisNetgroup' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( 1.2.840.113556.1.4.483 NAME 'calOtherFBURLs' DESC 'RFC2739: multi-value URI for other free/busy data' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc2739' )",
            "( 2.16.840.1.113730.3.1.2239 NAME 'nsslapd-SSL3ciphers' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.79 NAME 'cirReplicaRoot' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.241 NAME 'displayName' DESC 'preferred name of a person to be used when displaying entries' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2798' )",
            "( nsProductVersion-oid NAME 'nsProductVersion' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2176 NAME 'nsslapd-errorlog-logrotationsync-enabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2200 NAME 'nsslapd-errorlog-logexpirationtimeunit' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.40 NAME 'userSMIMECertificate' DESC 'signed message used to support S/MIME' SYNTAX *******.4.1.14************.5 X-ORIGIN 'RFC 2798' )",
            "( nsSecureServerPort-oid NAME 'nsSecureServerPort' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.218 NAME 'replicaAbandonedChanges' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.7 NAME 'changeType' DESC 'Changelog attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Changelog Internet Draft' )",
            "( ******** NAME 'registeredAddress'  SUP postalAddress EQUALITY caseIgnoreListMatch SUBSTR caseIgnoreListSubstringsMatch SYNTAX *******.4.1.14************.41 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.999 NAME ( 'passwordGraceLimit' 'pwdGraceLoginLimit' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2114 NAME 'internalCreatorsName' DESC 'plugin dn' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN '389 Directory Server' )",
            "( nsBindPassword-oid NAME 'nsBindPassword' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.812 NAME 'netscapeReversiblePassword' DESC 'password for HTTP Digest/MD5 authentication' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Web Server' )",
            "( 2.16.840.1.113730.3.1.2226 NAME 'nsslapd-listenhost' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******* NAME 'shadowMin' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2184 NAME 'nsslapd-accesslog-logrotationtime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.10 NAME 'manager'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.62 NAME 'ntUserParms' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2296 NAME 'nsslapd-ignore-virtual-attrs' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2074 NAME 'pamService' DESC 'Service name to pass to pam_start' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.2163 NAME 'winSyncWindowsFilter' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.4.4 NAME 'mozillaCustom4'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( ********.2.4.1120 NAME 'printer-print-quality-supported' DESC 'List of print qualities supported for printing documents on this printer.' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( *******.*******4 NAME 'bootFile' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( *******.4.1.5923.******* NAME 'eduPersonNickName' DESC 'NickName' SYNTAX *******.4.1.1466.************ X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( 2.16.840.1.113730.3.1.542 NAME 'nsUniqueId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.84 NAME 'cirUseSsl' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******3 NAME 'roleOccupant'  SUP distinguishedName EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( nsServerID-oid NAME 'nsServerID' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.254 NAME 'nsValueHelpURL' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( 2.16.840.1.113730.3.1.807 NAME 'nsds5replicaLastInitStart' DESC 'Netscape defined attribute type' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.14************.24 SINGLE-VALUE NO-USER-MODIFICATION X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2101 NAME 'autoMemberDefaultGroup' DESC 'Auto Membership default group' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2213 NAME 'nsslapd-userat' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2283 NAME 'nsslapd-SSLclientAuth' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.8 NAME 'userClass'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( ******* NAME 'aliasedObjectName'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 4512' )",
            "( *******.4.1.13769.3.2 NAME 'mozillaHomeStreet2'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.2148 NAME 'rootdn-deny-host' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******1 NAME ( 'ou' 'organizationalUnitName' )  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'organizationalUnitName' )",
            "( 0.9.2342.********.100.1.44 NAME 'uniqueIdentifier'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.16 NAME 'mailDeliveryOption' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.2127 NAME 'dnaPortNum' DESC 'DNA port number of replica to get new range of values' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.222 NAME ( 'passwordMinAge' 'pwdMinAge' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113719.1.1.4.1.35 NAME 'lastLoginTime' DESC 'Last login time' SYNTAX *******.4.1.14************.24 SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Account Policy Plugin' )",
            "( 2.16.840.1.113730.3.1.2231 NAME 'nsslapd-ldapimaprootdn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2197 NAME 'nsslapd-errorlog-logexpirationtime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.577 NAME 'cosIndirectSpecifier' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.71 NAME 'serverProductName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.******* NAME 'accessTo' DESC 'Access to which servers user is allowed' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'nss_ldap/pam_ldap' )",
            "( 2.16.840.1.113730.3.1.249 NAME 'nsValueType' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( *******.4.1.15953.9.1.5 NAME 'sudoOption' DESC 'Options(s) followed by sudo' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ X-ORIGIN 'SUDO' )",
            "( 2.16.840.1.113730.3.1.2278 NAME 'nsslapd-certdir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1135 NAME 'printer-name' DESC 'The site-specific administrative name of this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.38 NAME 'nsLicenseEndTime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.200 NAME 'changeLogMaximumAge' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.18.3 NAME 'creatorsName'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.93 NAME 'passwordRetryCount' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2247 NAME 'nsslapd-conntablesize' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2095 NAME 'connection' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( nsSuiteSpotUser-oid NAME 'nsSuiteSpotUser' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.693 NAME 'inetUserHttpURL' DESC 'A users Web addresses' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape subscriber interoperability' )",
            "( ********.2.4.1113 NAME 'printer-service-person' DESC 'The identity of the current human service person responsible for servicing this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2305 NAME 'nsslapd-moddn-aci' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.603 NAME 'dncomp' DESC 'internal server defined attribute type' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113730.3.1.1099 NAME 'winSyncInterval' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsBaseDN-oid NAME 'nsBaseDN' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2265 NAME 'nsslapd-versionstring' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.523 NAME 'ntUserFlags' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( ********.2.4.1128 NAME 'printer-compression-supported' DESC 'Compression algorithms supported by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2132 NAME 'nsds5ReplicaEnabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsExecRef-oid NAME 'nsExecRef' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.25 NAME 'mgrpDeliverTo' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.235 NAME 'nsSNMPContact' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.591 NAME 'nsDS5ReplicaReferral' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2179 NAME 'nsslapd-errorlog-logrotationsynchour' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2082 NAME ( 'passwordMinCategories' 'pwdMinCategories' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.688 NAME 'nsds5replicaLastUpdateStatus' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.37 NAME 'associatedDomain'  EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.213 NAME 'vlvEnabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2140 NAME 'passwordTrackUpdateTime' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2252 NAME 'nsslapd-groupevalnestlevel' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******* NAME 'shadowInactive' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2229 NAME 'nsslapd-ldapilisten' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.4.40 NAME 'crossCertificatePair' DESC 'X.509 cross certificate pair' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4523' )",
            "( 0.9.2342.********.100.1.15 NAME 'documentLocation'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.69 NAME 'subtreeACI' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server 1.0' )",
            "( 2.16.840.1.113730.3.1.2299 NAME 'nsslapd-connection-buffer' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2166 NAME 'schemaUpdateObjectclassReject' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.586 NAME 'nsDS5ReplicaUpdateSchedule' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.4.1 NAME 'mozillaCustom1'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.2270 NAME 'nsslapd-auditlog-list' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.1003 NAME 'nsds7NewWinGroupSyncEnabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.111 NAME 'ntUniqueId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.536 NAME 'ntGroupAttributes' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( *******.******** NAME 'ipNetmaskNumber' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.30 NAME 'mgrpRFC822MailMember' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.208 NAME 'vlvScope' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsNickName-oid NAME 'nsNickName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( ******** NAME 'userCertificate' DESC 'X.509 user certificate' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4523' )",
            "( 2.16.840.1.113730.3.1.2104 NAME 'nsslapd-pluginConfigArea' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.1.5 NAME 'vendorVersion'  EQUALITY *******.4.1.1466.109.114.1 SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE dSAOperation X-ORIGIN 'RFC 3045' )",
            "( *******.4.1.6981.11.3.3 NAME 'FTPUploadRatio' DESC 'Ratio (compared with FTPRatioDown) for uploaded files' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.802 NAME 'nsds5ReplicaLegacyConsumer' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2216 NAME 'nsslapd-require-secure-binds' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsKeyfile-oid NAME 'nsKeyfile' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.52 NAME 'replicaUpdateSchedule' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.7 NAME 'photo'  SYNTAX *******.4.1.14************.23 X-ORIGIN 'RFC 1274' )",
            "( 2.5.4.6 NAME ( 'c' 'countryName' )  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.14************.11 SINGLE-VALUE X-ORIGIN 'RFC 4519' X-DEPRECATED 'countryName' )",
            "( 2.16.840.1.113730.3.1.2064 NAME 'nsSaslMapRegexString' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2286 NAME 'nsslapd-outbound-ldap-io-timeout' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2153 NAME ( 'passwordAdminDN' 'pwdAdminDN' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'searchGuide'  SYNTAX *******.4.1.14************.25 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.106 NAME ( 'passwordMaxFailure' 'pwdMaxFailure' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******** NAME 'nisNetgroupTriple' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2234 NAME 'nsslapd-ldapigidnumbertype' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.572 NAME 'nsTimeLimit' DESC 'Binder-based search operation time limit (seconds)' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.74 NAME 'administratorContactInfo' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( nsClassname-oid NAME 'nsClassname' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.244 NAME 'nsValueCES' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( *******.4.1.5322.17.2.1 NAME 'authorizedService' DESC 'IANA GSS-API authorized service name' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'NSS LDAP schema' )",
            "( 2.16.840.1.113730.3.1.2171 NAME 'nsslapd-accesslog-maxlogsperdir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2203 NAME 'nsslapd-errorlog-logging-enabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsDeleteclassname-oid NAME 'nsDeleteclassname' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( nsmsgNumMsgQuota-oid NAME 'nsmsgNumMsgQuota' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( nsAdminCgiWaitPid-oid NAME 'nsAdminCgiWaitPid' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.2 NAME 'departmentNumber' DESC 'identifies a department within an organization' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2798' )",
            "( 2.16.840.1.113730.3.1.550 NAME 'cosAttribute' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.96 NAME ( 'passwordHistory' 'pwdHistory' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.14************.5 USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'telexNumber'  SYNTAX *******.4.1.14************.52 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2117 NAME 'dnaNextValue' DESC 'DNA next available value for assignment' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2098 NAME 'autoMemberFilter' DESC 'Auto Membership filter criteria' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( *******.******* NAME 'gidNumber' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2187 NAME 'nsslapd-accesslog-logrotationtimeunit' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2221 NAME 'nsslapd-validate-cert' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2308 NAME 'nstombstonecsn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.61 NAME 'ntUserUsrComment' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2291 NAME 'nsslapd-disk-monitoring-grace-period' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2077 NAME ( 'passwordMinUppers' 'pwdMinUppers' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.407 NAME 'nsSynchUniqueAttribute' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2268 NAME 'nsslapd-accesslog-list' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1125 NAME 'printer-finishings-supported' DESC 'The possible finishing operations supported by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( ******** NAME 'objectClasses'  EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.28 NAME 'mgrpMsgRejectAction' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.230 NAME 'nsslapd-pluginDescription' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2135 NAME 'nsds5ReplicaCleanRUV' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsAdminCacheLifetime-oid NAME 'nsAdminCacheLifetime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.327 NAME 'nsIndexType' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.545 NAME 'nscpEntryDN' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.5923.******* NAME 'eduPersonPrimaryAffiliation' DESC 'Primary Affiliation' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( 2.16.840.1.113730.3.1.83 NAME 'cirUsePersistentSearch' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.********.27.4.1.10 NAME 'javaFactory' DESC 'Fully qualified Java class name of a JNDI object factory' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2713' )",
            "( ********0 NAME 'subschemaSubentry'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.2085 NAME 'isReplicated' DESC 'Changelog attribute type' SYNTAX *******.4.1.1466.*********** X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.683 NAME 'nsds5ReplicaTombstonePurgeInterval' DESC 'Netscape defined attribute type' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.38 NAME 'associatedName'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.1100 NAME 'oneWaySync' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsConfigRoot-oid NAME 'nsConfigRoot' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******.4.1.13769.3.7 NAME 'mozillaHomeUrl'  EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.2255 NAME 'passwordIsGlobalPolicy' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.41 NAME ( 'mobile' 'mobileTelephoneNumber' )  EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' X-DEPRECATED 'mobileTelephoneNumber' )",
            "( 2.16.840.1.113730.3.1.2122 NAME 'dnaMaxValue' DESC 'DNA maximum value to assign' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( nsAdminDomainName-oid NAME 'nsAdminDomainName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.15 NAME 'mailAutoReplyText' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.225 NAME 'nsslapd-pluginInitfunc' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsAdminEndUserHTMLIndex-oid NAME 'nsAdminEndUserHTMLIndex' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.********.3.2.1.3 NAME 'accountInactivityLimit' DESC 'Account inactivity limit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Account Policy Plugin' )",
            "( 2.16.840.1.113730.3.1.2192 NAME 'nsslapd-auditlog-logmaxdiskspace' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsBuildSecurity-oid NAME 'nsBuildSecurity' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.708 NAME 'vacationenddate' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.581 NAME 'nsDS5ReplicaBindDN' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2169 NAME 'nsslapd-pagedsizelimit' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( mgrpApprovePassword-oid NAME 'mgrpApprovePassword' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( ********.2.4.1136 NAME 'printer-location' DESC 'The physical location of this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.531 NAME 'ntUserBadPwCount' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.37 NAME 'nsLicenseStartTime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.203 NAME 'replicaEntryFilter' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2242 NAME 'nsslapd-securePort' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******9 NAME 'certificateRevocationList' DESC 'X.509 certificate revocation list' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4523' )",
            "( nsAdminAccountInfo-oid NAME 'nsAdminAccountInfo' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.2090 NAME 'mepRDNAttr' DESC 'Managed Entries RDN attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.696 NAME 'inetSubscriberResponse' DESC 'Used to confirm subscriberIdentity.  This attribute holds the response phrase and is used in conjunction with the inetSubscriberChallenge' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape subscriber interoperability' )",
            "( *******0 NAME 'uniqueMember'  EQUALITY uniqueMemberMatch SYNTAX *******.4.1.14************.34 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2219 NAME 'nsslapd-minssf' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1114 NAME 'printer-delivery-orientation-supported' DESC 'The possible delivery orientations of pages as they are printed and ejected from this printer.' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( *******.*********.60 NAME ( 'ttl' 'timeToLive' ) DESC 'time to live in seconds for cached objects' SYNTAX *******.4.1.1466.************ X-ORIGIN 'LDAP Caching Internet Draft' )",
            "( 2.16.840.1.113730.3.1.2300 NAME 'nsslapd-connection-nocanon' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.59 NAME 'ntUserPriv' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2289 NAME 'nsslapd-disk-monitoring' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsDefaultObjectClass-oid NAME 'nsDefaultObjectClass' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.4.1.13769.3.8 NAME 'mozillaWorkStreet2'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.2156 NAME 'nsslapd-sasl-max-buffer-size' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2260 NAME 'nsslapd-result-tweak' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.101 NAME ( 'passwordInHistory' 'pwdInHistory' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.526 NAME 'ntUserLastLogon' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( *******.******** NAME 'shadowFlag' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.20 NAME 'mailProgramDeliveryInfo' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 1.2.840.113556.1.4.481 NAME 'calCalAdrURI' DESC 'RFC2739: URI for event equests destination' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc2739' )",
            "( 2.16.840.1.113730.3.1.238 NAME 'nsSNMPMasterPort' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.15953.9.1.10 NAME 'sudoOrder' DESC 'an integer to order the sudoRole entries' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'SUDO' )",
            "( *******.********.27.4.1.7 NAME 'javaCodebase' DESC 'URL(s) specifying the location of class definition' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2713' )",
            "( 2.16.840.1.113730.3.1.594 NAME 'nsDS5ReplicatedAttributeListTotal' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2174 NAME 'nsslapd-auditlog-maxlogsize' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2206 NAME 'nsslapd-unhashed-pw-switch' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.42 NAME 'ntUserCreateNewAccount' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.216 NAME 'userPKCS12' DESC 'PKCS #12 PFX PDU for exchange of personal identity information' SYNTAX *******.4.1.14************.5 X-ORIGIN 'RFC 2798' )",
            "( 2.16.840.1.113730.3.1.2143 NAME 'nsslapd-sasl-mapping-fallback' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.5 NAME 'changeNumber' DESC 'Changelog attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Changelog Internet Draft' )",
            "( ******** NAME 'hasSubordinates' DESC 'if TRUE, subordinate entries may exist' EQUALITY booleanMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'numSubordinates Internet Draft' )",
            "( ******** NAME 'x121Address'  EQUALITY numericStringMatch SUBSTR numericStringSubstringsMatch SYNTAX *******.4.1.14************.36 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.997 NAME 'pwdpolicysubentry' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'initials'  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2224 NAME 'nsslapd-port' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******* NAME 'loginShell' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.64 NAME 'ntUserNumLogons' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2294 NAME 'nsslapd-ndn-cache-max-size' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2161 NAME 'nsIndexIDListScanLimit' DESC 'fine grained idlistscanlimit - per index/type/value' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.589 NAME 'nsDS5ReplicaType' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.4.2 NAME 'mozillaCustom2'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( *******.4.1.15953.9.1.8 NAME 'sudoNotBefore' DESC 'Start of time interval for which the entry is valid' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.14************.24 X-ORIGIN 'SUDO' )",
            "( 2.16.840.1.113730.3.1.2273 NAME 'nsslapd-config' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'structuralObjectClass'  EQUALITY objectIdentifierMatch SYNTAX *******.4.1.14************.38 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( nsDisplayName-oid NAME 'nsDisplayName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.******** NAME 'macAddress' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2138 NAME 'nsslapd-readonly' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.86 NAME 'cirLastUpdateApplied' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'member'  SUP distinguishedName EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( sslVersionMax-oid NAME 'sslVersionMax' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.252 NAME 'nsValueDescription' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( 2.16.840.1.113730.3.1.2107 NAME 'nsPagedSizeLimit' DESC 'Binder-based simple paged search operation size limit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN '389' )",
            "( 2.16.840.1.113730.3.1.805 NAME 'nsds5replicaTimeout' DESC 'Netscape defined attribute type' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2088 NAME 'mepStaticAttr' DESC 'Managed Entries static attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2211 NAME 'nsslapd-dynamicconf' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.51 NAME 'replicaUpdateReplayed' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2281 NAME 'nsslapd-saslpath' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******* NAME ( 'cn' 'commonName' )  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'commonName' )",
            "( 2.16.840.1.113730.3.1.2067 NAME 'pamIncludeSuffix' DESC 'Suffixes to include for PAM authentication' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.199 NAME 'memberCertificateDescription' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.2312.******* NAME 'sabayonProfileURL' DESC 'The URL of a sabayon profile' SUP labeledURI EQUALITY caseExactMatch SUBSTR caseExactSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'Sabayon' )",
            "( ******** NAME 'postalCode'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2258 NAME 'nsslapd-csnlogging' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsSSL2-oid NAME 'nsSSL2' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.109 NAME ( 'passwordLockoutDuration' 'pwdLockoutDuration' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******** NAME 'ipHostNumber' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.18 NAME 'mailHost' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.220 NAME ( 'passwordMustChange' 'pwdMustChange' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2125 NAME 'dnaThreshold' DESC 'DNA threshold for getting next range of values' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2237 NAME 'nsslapd-counters' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2195 NAME 'nsslapd-auditlog-logminfreediskspace' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.575 NAME 'nsRoleDN' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.73 NAME 'installationTimeStamp' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.247 NAME 'nsValueBin' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 X-ORIGIN 'Netscape servers - value item' )",
            "( *******.4.1.15953.9.1.7 NAME 'sudoRunAsGroup' DESC 'Group(s) impersonated by sudo' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ X-ORIGIN 'SUDO' )",
            "( ********.2.4.1133 NAME 'printer-ipp-versions-supported' DESC 'IPP protocol version(s) that this printer supports.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( ******** NAME 'createTimestamp'  EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.14************.24 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.553 NAME 'costemplatedn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.95 NAME 'accountUnlockTime' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.14************.24 SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2245 NAME 'nsslapd-maxthreadsperconn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2112 NAME 'ntGroupType' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.691 NAME 'inetDomainStatus' DESC '\\"active\\", \\"inactive\\", or \\"deleted\\" status of a domain' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape subscriber interoperability' )",
            "( ********.2.4.1111 NAME 'printer-job-k-octets-supported' DESC 'The maximum size in kilobytes (1,024 octets actually) incoming print job that this printer will accept.' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2182 NAME 'nsslapd-errorlog-logrotationsyncmin' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.788 NAME 'mgrpBroadcasterPolicy' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.601 NAME 'adminRole' DESC 'Administrative role' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Delegated Administrator' )",
            "( 2.16.840.1.113730.3.1.2072 NAME 'pamFallback' DESC 'Fallback to regular LDAP BIND if PAM auth fails' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.2159 NAME 'dnaRemoteConnProtocol' DESC 'Connection protocol: LDAP, TLS, or SSL' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( nsLogSuppress-oid NAME 'nsLogSuppress' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.5.21.1 NAME 'dITStructureRules'  EQUALITY integerFirstComponentMatch SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.521 NAME 'ntUserHomeDir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( ********.2.4.1126 NAME 'printer-pages-per-minute-color' DESC 'The nominal number of color pages per minute which may be output by this printer.' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 1.2.840.113556.1.4.484 NAME 'calOtherCAPURIs' DESC 'RFC2739: multi-value URI to other calendars' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc2739' )",
            "( 2.16.840.1.113730.3.1.2130 NAME 'dnaRangeRequestTimeout' DESC 'DNA timeout for querying replica for next range of values' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.233 NAME 'nsSNMPOrganization' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.1466.101.120.14 NAME 'supportedSASLMechanisms'  SYNTAX *******.4.1.1466.************ USAGE dSAOperation X-ORIGIN 'RFC 4512' )",
            "( *******.4.1.5923.1.1.1.8 NAME 'eduPersonPrimaryOrgUnitDN' DESC 'Primary Organizational Unit' SYNTAX *******.4.1.1466.************ X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( nsHardwarePlatform-oid NAME 'nsHardwarePlatform' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******.4.1.6981.11.3.8 NAME 'FTPuid' DESC 'System uid (overrides uidNumber if present)' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.686 NAME 'nsds5replicaLastUpdateEnd' DESC 'Netscape defined attribute type' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.14************.24 SINGLE-VALUE NO-USER-MODIFICATION X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2080 NAME ( 'passwordMin8bit' 'pwdMin8bit' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2209 NAME 'nsslapd-rootpwstoragescheme' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.49 NAME 'replicaUpdateFailedAt' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.8 NAME 'changes' DESC 'Changelog attribute type' SYNTAX *******.4.1.14************.5 X-ORIGIN 'Changelog Internet Draft' )",
            "( 2.16.840.1.113730.3.1.2146 NAME 'rootdn-days-allowed' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2250 NAME 'nsslapd-ioblocktimeout' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.1466.101.120.7 NAME 'supportedExtension'  SYNTAX *******.4.1.14************.38 USAGE dSAOperation X-ORIGIN 'RFC 4512' )",
            "( 0.9.2342.********.100.1.42 NAME ( 'pager' 'pagerTelephoneNumber' )  EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' X-DEPRECATED 'pagerTelephoneNumber' )",
            "( 2.16.840.1.113730.3.1.10 NAME 'deleteOldRdn' DESC 'Changelog attribute type' SYNTAX *******.4.1.1466.*********** X-ORIGIN 'Changelog Internet Draft' )",
            "( 2.16.840.1.113730.3.1.228 NAME 'nsslapd-pluginVersion' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.4.46 NAME 'dnQualifier'  EQUALITY caseIgnoreMatch ORDERING caseIgnoreOrderingMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.14************.44 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.614 NAME 'copyingFrom' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( nsSSLToken-oid NAME 'nsSSLToken' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.584 NAME 'nsDS5ReplicaRoot' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2164 NAME 'winSyncSubtreePair' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2276 NAME 'nsslapd-lockdir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.1001 NAME 'nsds7DirectoryReplicaSubtree' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.534 NAME 'ntUserPrimaryGroupId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 0.9.2342.********.100.1.20 NAME ( 'homePhone' 'homeTelephoneNumber' )  EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' X-DEPRECATED 'homeTelephoneNumber' )",
            "( 2.16.840.1.113730.3.1.32 NAME 'mgrpMsgMaxSize' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.206 NAME 'filterInfo' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1140 NAME 'printer-uri' DESC 'A URI supported by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( *******4 NAME 'seeAlso'  SUP distinguishedName EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( nsSSL3Ciphers-oid NAME 'nsSSL3Ciphers' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******.4.1.6981.11.3.5 NAME 'FTPUploadBandwidth' DESC 'Bandwidth (in KB/s) to limit upload speeds to' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.2093 NAME 'nsslapd-changelogsuffix' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2214 NAME 'nsslapd-svrtab' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******3 NAME 'deltaRevocationList' DESC 'X.509 delta revocation list' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4523' )",
            "( nsUniqueAttribute-oid NAME 'nsUniqueAttribute' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( ********.2.4.1119 NAME 'printer-natural-language-configured' DESC 'The configured natural language in which error and status messages will be generated (by default) by this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2303 NAME 'nsslapd-ignore-time-skew' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.54 NAME 'replicaUseSSL' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.1 NAME ( 'uid' 'userid' )  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'userid' )",
            "( 2.16.840.1.113730.3.1.609 NAME 'nsds5BeginReplicaRefresh' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2284 NAME 'nsslapd-ssl-check-hostname' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******* NAME ( 'st' 'stateOrProvinceName' )  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'stateOrProvinceName' )",
            "( 2.16.840.1.113730.3.1.1097 NAME 'nsds5replicaBusyWaitTime' DESC 'Netscape defined attribute type' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2151 NAME 'nsslapd-plugin-depends-on-type' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsViewConfiguration-oid NAME 'nsViewConfiguration' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.2263 NAME 'nsslapd-maxsasliosize' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.529 NAME 'ntUserMaxStorage' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.104 NAME ( 'passwordWarning' 'pwdExpireWarning' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.*******2 NAME 'memberUid' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2307' )",
            "( nsAccessLog-oid NAME 'nsAccessLog' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2128 NAME 'dnaSecurePortNum' DESC 'DNA secure port number of replica to get new range of values' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( nsPidLog-oid NAME 'nsPidLog' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2198 NAME 'nsslapd-auditlog-logexpirationtime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.570 NAME 'nsLookThroughLimit' DESC 'Binder-based search operation look through limit (candidate entries)' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( nsCertfile-oid NAME 'nsCertfile' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.76 NAME 'serverHostName' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.242 NAME 'nsSystemIndex' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2177 NAME 'nsslapd-auditlog-logrotationsync-enabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2201 NAME 'nsslapd-auditlog-logexpirationtimeunit' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsServerCreationClassname-oid NAME 'nsServerCreationClassname' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.41 NAME 'ntUserDomainId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.219 NAME 'vlvUses' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.973 NAME 'nsds5ReplConflict' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.18.4 NAME 'modifiersName'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.98 NAME 'passwordExp' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'destinationIndicator'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.14************.44 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2248 NAME 'nsslapd-reservedescriptors' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2115 NAME 'dnaType' DESC 'DNA attribute type to maintain' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.2227 NAME 'nsslapd-snmp-index' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******* NAME 'shadowMax' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2185 NAME 'nsslapd-errorlog-logrotationtime' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.13 NAME 'documentVersion'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.63 NAME 'ntUserUnitsPerWeek' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2297 NAME 'nsslapd-search-return-original-type-switch' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2075 NAME ( 'passwordMinDigits' 'pwdMinDigits' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.5.21.4 NAME 'matchingRules'  EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( ********.2.4.1123 NAME 'printer-sides-supported' DESC 'The number of impression sides (one or two) and the two-sided impression rotations supported by this printer.' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( *******.*******7 NAME 'nisMapEntry' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( *******.4.1.5923.******* NAME 'eduPersonOrgDN' DESC 'Organization DN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( 2.16.840.1.113730.3.1.543 NAME 'nsState' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.85 NAME 'cirBindCredentials' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.********.********* NAME 'javaDoc' DESC 'The Java documentation for the class' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2713' )",
            "( 2.16.840.1.113730.3.1.2102 NAME 'autoMemberGroupingAttr' DESC 'Auto Membership grouping attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.808 NAME 'nsds5replicaLastInitEnd' DESC 'Netscape defined attribute type' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.14************.24 SINGLE-VALUE NO-USER-MODIFICATION X-ORIGIN 'Netscape Directory Server' )",
            "( nsUserIDFormat-oid NAME 'nsUserIDFormat' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 0.9.2342.********.100.1.9 NAME 'host'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( ******* NAME 'objectClass'  EQUALITY objectIdentifierMatch SYNTAX *******.4.1.14************.38 X-ORIGIN 'RFC 4512' )",
            "( nsAdminOneACLDir-oid NAME 'nsAdminOneACLDir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( nsBuildNumber-oid NAME 'nsBuildNumber' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( *******.4.1.13769.3.5 NAME 'mozillaHomePostalCode'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.2149 NAME 'rootdn-allow-ip' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.2312.******* NAME 'sabayonProfileName' DESC 'The Name of a sabayon profile' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Sabayon' )",
            "( ******** NAME 'title'  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.17 NAME 'mailForwardingAddress' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.2120 NAME 'dnaFilter' DESC 'DNA filter for finding entries' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.223 NAME ( 'passwordResetFailureCount' 'pwdFailureCountInterval' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME ( 'distinguishedName' 'dn' )  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' X-DEPRECATED 'dn' )",
            "( 2.16.840.1.113730.3.1.578 NAME 'nsDS5ReplicaHost' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2190 NAME 'nsslapd-accesslog-logmaxdiskspace' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2232 NAME 'nsslapd-ldapimaptoentries' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********* NAME 'governingStructureRule'  EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( *******.4.1.15953.9.1.2 NAME 'sudoHost' DESC 'Host(s) who may run sudo' EQUALITY caseExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'SUDO' )",
            "( 2.16.840.1.113730.3.1.2279 NAME 'nsslapd-ldifdir' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1134 NAME 'printer-more-info' DESC 'A URI for more information about this specific printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 0.9.2342.********.100.1.25 NAME ( 'dc' 'domaincomponent' )  EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 4519' X-DEPRECATED 'domaincomponent' )",
            "( 2.16.840.1.113730.3.1.1004 NAME 'nsds7WindowsDomain' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.39 NAME 'preferredLanguage' DESC 'preferred written or spoken language for a person' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2798' )",
            "( 2.16.840.1.113730.3.1.201 NAME 'changeLogMaximumSize' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.13769.2.3 NAME ( 'mozillaUseHtmlMail' 'xmozillausehtmlmail' )  SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( nsSerialNumber-oid NAME 'nsSerialNumber' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.90 NAME 'cirBeginORC' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2240 NAME 'nsslapd-accesslog' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2096 NAME 'entryusn' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.694 NAME 'inetSubscriberAccountId' DESC 'A unique attribute linking the subscriber to a billing system' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape subscriber interoperability' )",
            "( ********.2.4.1112 NAME 'printer-current-operator' DESC 'The identity of the current human operator responsible for operating this printer.' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.2306 NAME 'nsslapd-return-default-opattr' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 1.2.840.113556.1.4.478 NAME 'calCalURI' DESC 'RFC2739: URI of entire default calendar' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc2739' )",
            "( 2.16.840.1.113730.3.1.604 NAME 'parentid' DESC 'internal server defined attribute type' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113730.3.1.2154 NAME 'nsds5ReplicaBackoffMin' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2266 NAME 'nsslapd-enquote-sup-oc' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.524 NAME 'ntUserScriptPath' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2133 NAME 'pwdUpdateTime' DESC 'Last password update time' SYNTAX *******.4.1.14************.24 SINGLE-VALUE USAGE directoryOperation X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.22 NAME 'mgrpAllowedBroadcaster' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.236 NAME 'nsSNMPDescription' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.1466.101.120.13 NAME 'supportedControl'  SYNTAX *******.4.1.14************.38 USAGE dSAOperation X-ORIGIN 'RFC 4512' )",
            "( nsPreference-oid NAME 'nsPreference' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.592 NAME 'nsDS5ReplicaAutoReferral' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2083 NAME ( 'passwordMinTokenLength' 'pwdMinTokenLength' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.689 NAME 'nsds5replicaUpdateInProgress' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE NO-USER-MODIFICATION X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2204 NAME 'nsslapd-auditlog-logging-enabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1109 NAME 'printer-charset-configured' DESC 'The configured charset in which error and status messages will be generated (by default) by this printer.' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.44 NAME 'ntGroupDomainId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.214 NAME 'passwordAllowChangeTime' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.14************.24 SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2141 NAME 'dsOnlyMemberUid' DESC 'Elements from a memberuid attribute created to reflect dynamic group membership' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Red Hat Directory Server' )",
            "( nsDirectoryFailoverList-oid NAME 'nsDirectoryFailoverList' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( nsSSLSessionTimeout-oid NAME 'nsSSLSessionTimeout' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2253 NAME 'nsslapd-nagle' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2118 NAME 'dnaInterval' DESC 'DNA interval between values' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( ******** NAME 'name'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2188 NAME 'nsslapd-errorlog-logrotationtimeunit' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.14 NAME 'documentAuthor'  EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.66 NAME 'ntUserUniqueId' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.14************.5 SINGLE-VALUE X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.1.2078 NAME ( 'passwordMinLowers' 'pwdMinLowers' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.587 NAME 'nsds50ruv' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2167 NAME 'schemaUpdateAttributeAccept' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2271 NAME 'nsslapd-rewrite-rfc1274' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.******** NAME 'ipNetworkNumber' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.31 NAME 'mailEnhancedUniqueMember' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.209 NAME 'vlvFilter' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsErrorLog-oid NAME 'nsErrorLog' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.88 NAME 'cirUpdateFailedat' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.250 NAME 'nsValueDefault' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( *******7 NAME 'cACertificate' DESC 'X.509 CA certificate' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4523' )",
            "( 2.16.840.1.113730.3.1.2105 NAME 'autoMemberTargetGroup' DESC 'Auto Membership target group' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( *******.4.1.6981.11.3.2 NAME 'FTPQuotaMBytes' DESC 'Quota (in megabytes) for an FTP user' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Pure-FTPd' )",
            "( 2.16.840.1.113730.3.1.803 NAME 'nsBackendSuffix' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2217 NAME 'nsslapd-allow-anonymous-access' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.53 NAME 'replicaBindMethod' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.4 NAME 'info'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.2065 NAME 'nsSaslMapBaseDNTemplate' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsSSLSupportedCiphers-oid NAME 'nsSSLSupportedCiphers' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.2287 NAME 'nsslapd-force-sasl-external' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******* NAME 'serialNumber'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.14************.44 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.197 NAME 'replicaHost' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'businessCategory'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.107 NAME 'passwordResetDuration' DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 0.9.2342.********.100.1.48 NAME 'buildingName'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( *******.******** NAME 'ipProtocolNumber' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2235 NAME 'nsslapd-ldapientrysearchbase' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( nsInstalledLocation-oid NAME 'nsInstalledLocation' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.1.573 NAME 'nsIdleTimeout' DESC 'Binder-based connection idle timeout (seconds)' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.75 NAME 'adminUrl' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.245 NAME 'nsValueTel' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape servers - value item' )",
            "( *******.4.1.1466.101.120.42 NAME 'preferredLocale' DESC 'preferred locale for a person' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape' )",
            "( nsNYR-oid NAME 'nsNYR' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.1.2172 NAME 'nsslapd-accesslog-maxlogsize' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1131 NAME 'printer-charset-supported' DESC 'Set of charsets supported for the attribute values of syntax DirectoryString for this directory entry.' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.16.840.1.113730.3.1.3 NAME 'employeeNumber' DESC 'numerically identifies an employee within an organization' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2798' )",
            "( 2.16.840.1.113730.3.1.551 NAME 'cosspecifier' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.97 NAME ( 'passwordMaxAge' 'pwdMaxAge' ) DESC 'Netscape defined password policy attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'teletexTerminalIdentifier'  SYNTAX *******.4.1.14************.51 X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2099 NAME 'autoMemberExclusiveRegex' DESC 'Auto Membership exclusive regex rule' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( *******.******* NAME 'gecos' DESC 'Standard LDAP attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.1.2180 NAME 'nsslapd-auditlog-logrotationsynchour' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2222 NAME 'nsslapd-localuser' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2309 NAME 'nsds5ReplicaPreciseTombstonePurging' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2292 NAME 'nsslapd-disk-monitoring-logging-critical' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2070 NAME 'pamIDMapMethod' DESC 'How to map BIND DN to PAM identity' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.1.408 NAME 'replicaLastRelevantChange' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2269 NAME 'nsslapd-errorlog-list' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( ********.2.4.1124 NAME 'printer-number-up-supported' DESC 'The possible numbers of print-stream pages to impose upon a single side of an instance of a selected medium.' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'rfc3712' )",
            "( 2.5.21.7 NAME 'nameForms'  EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 0.9.2342.********.100.1.55 NAME 'audio'  EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 1274' )",
            "( 2.16.840.1.113730.3.1.29 NAME 'mgrpMsgRejectText' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.231 NAME 'nsslapd-pluginEnabled' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2136 NAME 'nsds5ReplicaCleanRUVNotified' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsWellKnownJarfiles-oid NAME 'nsWellKnownJarfiles' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.4.1.1466.101.120.16 NAME 'ldapSyntaxes'  EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-ORIGIN 'RFC 4512' )",
            "( 2.16.840.1.113730.3.1.328 NAME 'nsMatchingRule' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( nsAdminAccessHosts-oid NAME 'nsAdminAccessHosts' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Administration Services' )",
            "( *******.4.1.5923.******* NAME 'eduPersonPrincipalName' DESC 'Principal Name' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( 2.16.840.1.113730.3.1.80 NAME 'cirHost' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )",
            "( *******.********.********* NAME 'javaReferenceAddress' DESC 'Addresses associated with a JNDI Reference' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 2713' )",
            "( 2.16.840.1.113730.3.1.2086 NAME 'mepManagedBy' DESC 'Managed Entries backpointer' SYNTAX *******.4.1.1466.************ X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.1.684 NAME 'nsds5ReplicaChangeCount' DESC 'Netscape defined attribute type' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.1101 NAME 'nsRoleScopeDN' DESC 'Scope of a role' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( *******.4.1.453.16.2.103 NAME 'numSubordinates' DESC 'count of immediate subordinates' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-ORIGIN 'numSubordinates Internet Draft' )",
            "( *******.4.1.13769.3.6 NAME 'mozillaHomeCountryName'  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Mozilla Address Book' )",
            "( 2.16.840.1.113730.3.1.2144 NAME 'rootdn-open-time' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.9999999 NAME 'nsds5debugreplicatimeout' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2256 NAME 'passwordLegacyPolicy' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( *******.4.1.1466.101.120.5 NAME 'namingContexts'  SYNTAX *******.4.1.1466.************ USAGE dSAOperation X-ORIGIN 'RFC 4512' )",
            "( 0.9.2342.********.100.1.40 NAME 'personalTitle'  EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.1.12 NAME 'mailAccessDomain' DESC 'Netscape Messaging Server 4.x defined attribute' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.1.226 NAME 'nsslapd-pluginType' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.2123 NAME 'dnaSharedCfgDN' DESC 'DNA shared configuration entry DN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN '389 Directory Server' )",
            "( 2.5.4.44 NAME 'generationQualifier'  SUP name EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113730.3.1.2193 NAME 'nsslapd-accesslog-logminfreediskspace' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.1.612 NAME 'generation' DESC 'Netscape defined attribute type' SYNTAX *******.4.1.1466.************ X-ORIGIN 'Netscape Directory Server' )"
        ],
        "cn": [
            "schema"
        ],
        "ldapSyntaxes": [
            "( *******.4.1.14************.5 DESC 'Binary' )",
            "( *******.4.1.14************.6 DESC 'Bit String' )",
            "( *******.4.1.1466.*********** DESC 'Boolean' )",
            "( *******.4.1.1466.************ DESC 'IA5String' )",
            "( *******.4.1.1466.************ DESC 'DirectoryString' )",
            "( *******.4.1.14************.11 DESC 'Country String' )",
            "( *******.4.1.14************.14 DESC 'Delivery Method' )",
            "( *******.4.1.1466.************ DESC 'DN' )",
            "( *******.4.1.14************.21 DESC 'Enhanced Guide' )",
            "( *******.4.1.14************.22 DESC 'Facsimile Telephone Number' )",
            "( *******.4.1.14************.23 DESC 'FAX' )",
            "( *******.4.1.14************.24 DESC 'GeneralizedTime' )",
            "( *******.4.1.14************.25 DESC 'Guide' )",
            "( *******.4.1.1466.************ DESC 'INTEGER' )",
            "( *******.4.1.14************.28 DESC 'JPEG' )",
            "( *******.4.1.14************.34 DESC 'Name And Optional UID' )",
            "( *******.4.1.14************.36 DESC 'Numeric String' )",
            "( *******.4.1.1466.************ DESC 'OctetString' )",
            "( *******.4.1.14************.38 DESC 'OID' )",
            "( *******.4.1.14************.41 DESC 'Postal Address' )",
            "( *******.4.1.14************.44 DESC 'Printable String' )",
            "( *******.4.1.1466.************ DESC 'TelephoneNumber' )",
            "( *******.4.1.14************.51 DESC 'Teletex Terminal Identifier' )",
            "( *******.4.1.14************.52 DESC 'Telex Number' )"
        ],
        "matchingRules": [
            "( ********7 NAME 'octetStringMatch' DESC 'The octetStringMatch rule compares an assertion value of the Octet String syntax to an attribute value of a syntax (e.g., the Octet String or JPEG syntax) whose corresponding ASN.1 type is the OCTET STRING ASN.1 type.  The rule evaluates to TRUE if and only if the attribute value and the assertion value are the same length and corresponding octets (by position) are the same.' SYNTAX *******.4.1.1466.************ )",
            "( ********8 NAME 'octetStringOrderingMatch' DESC 'The octetStringOrderingMatch rule compares an assertion value of the Octet String syntax to an attribute value of a syntax (e.g., the Octet String or JPEG syntax) whose corresponding ASN.1 type is the OCTET STRING ASN.1 type.  The rule evaluates to TRUE if and only if the attribute value appears earlier in the collation order than the assertion value.  The rule compares octet strings from the first octet to the last octet, and from the most significant bit to the least significant bit within the octet.  The first occurrence of a different bit determines the ordering of the strings.  A zero bit precedes a one bit.  If the strings contain different numbers of octets but the longer string is identical to the shorter string up to the length of the shorter string, then the shorter string precedes the longer string.' SYNTAX *******.4.1.1466.************ )",
            "( ********6 NAME 'bitStringMatch' DESC 'The bitStringMatch rule compares an assertion value of the Bit String syntax to an attribute value of a syntax (e.g., the Bit String syntax) whose corresponding ASN.1 type is BIT STRING.  If the corresponding ASN.1 type of the attribute syntax does not have a named bit list [ASN.1] (which is the case for the Bit String syntax), then the rule evaluates to TRUE if and only if the attribute value has the same number of bits as the assertion value and the bits match on a bitwise basis.  If the corresponding ASN.1 type does have a named bit list, then bitStringMatch operates as above, except that trailing zero bits in the attribute and assertion values are treated as absent.' SYNTAX *******.4.1.14************.6 )",
            "( *******.4.1.1466.109.114.1 NAME 'caseExactIA5Match' DESC 'The caseExactIA5Match rule compares an assertion value of the IA5 String syntax to an attribute value of a syntax (e.g., the IA5 String syntax) whose corresponding ASN.1 type is IA5String. The rule evaluates to TRUE if and only if the prepared attribute value character string and the prepared assertion value character string have the same number of characters and corresponding characters have the same code point. In preparing the attribute value and assertion value for comparison, characters are not case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.1466.************ )",
            "( 2.5.13.5 NAME 'caseExactMatch' DESC 'The caseExactMatch rule compares an assertion value of the Directory String syntax to an attribute value of a syntax (e.g., the Directory String, Printable String, Country String, or Telephone Number syntax) whose corresponding ASN.1 type is DirectoryString or one of the alternative string types of DirectoryString, such as PrintableString (the other alternatives do not correspond to any syntax defined in this document). The rule evaluates to TRUE if and only if the prepared attribute value character string and the prepared assertion value character string have the same number of characters and corresponding characters have the same code point. In preparing the attribute value and assertion value for comparison, characters are not case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.1466.************ )",
            "( 2.5.13.6 NAME 'caseExactOrderingMatch' DESC 'The caseExactOrderingMatch rule compares an assertion value of the Directory String syntax to an attribute value of a syntax (e.g., the Directory String, Printable String, Country String, or Telephone Number syntax) whose corresponding ASN.1 type is DirectoryString or one of its alternative string types. The rule evaluates to TRUE if and only if, in the code point collation order, the prepared attribute value character string appears earlier than the prepared assertion value character string; i.e., the attribute value is \\"less than\\" the assertion value. In preparing the attribute value and assertion value for comparison, characters are not case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.1466.************ )",
            "( 2.5.13.7 NAME 'caseExactSubstringsMatch' DESC 'The caseExactSubstringsMatch rule compares an assertion value of the Substring Assertion syntax to an attribute value of a syntax (e.g., the Directory String, Printable String, Country String, or Telephone Number syntax) whose corresponding ASN.1 type is DirectoryString or one of its alternative string types. The rule evaluates to TRUE if and only if (1) the prepared substrings of the assertion value match disjoint portions of the prepared attribute value character string in the order of the substrings in the assertion value, (2) an <initial> substring, if present, matches the beginning of the prepared attribute value character string, and (3) a <final> substring, if present, matches the end of the prepared attribute value character string.  A prepared substring matches a portion of the prepared attribute value character string if corresponding characters have the same code point. In preparing the attribute value and assertion value substrings for comparison, characters are not case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.14************.58 )",
            "( 2.16.840.1.113730.3.3.1 NAME 'caseExactIA5SubstringsMatch' DESC 'The caseExactIA5SubstringsMatch rule compares an assertion value of the Substring Assertion syntax to an attribute value of a syntax (e.g., the IA5 syntax) whose corresponding ASN.1 type is IA5 String or one of its alternative string types. The rule evaluates to TRUE if and only if (1) the prepared substrings of the assertion value match disjoint portions of the prepared attribute value character string in the order of the substrings in the assertion value, (2) an <initial> substring, if present, matches the beginning of the prepared attribute value character string, and (3) a <final> substring, if present, matches the end of the prepared attribute value character string.  A prepared substring matches a portion of the prepared attribute value character string if corresponding characters have the same code point. In preparing the attribute value and assertion value substrings for comparison, characters are not case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.14************.58 )",
            "( 2.5.13.27 NAME 'generalizedTimeMatch' DESC 'The rule evaluates to TRUE if and only if the attribute value represents the same universal coordinated time as the assertion value.' SYNTAX *******.4.1.14************.24 )",
            "( 2.5.13.28 NAME 'generalizedTimeOrderingMatch' DESC 'The rule evaluates to TRUE if and only if the attribute value represents a universal coordinated time that is earlier than the universal coordinated time represented by the assertion value.' SYNTAX *******.4.1.14************.24 )",
            "( ********3 NAME 'booleanMatch' DESC 'The booleanMatch rule compares an assertion value of the Boolean syntax to an attribute value of a syntax (e.g., the Boolean syntax) whose corresponding ASN.1 type is BOOLEAN.  The rule evaluates to TRUE if and only if the attribute value and the assertion value are both TRUE or both FALSE.' SYNTAX *******.4.1.1466.*********** )",
            "( *******.4.1.1466.109.114.2 NAME 'caseIgnoreIA5Match' DESC 'The caseIgnoreIA5Match rule compares an assertion value of the IA5 String syntax to an attribute value of a syntax (e.g., the IA5 String syntax) whose corresponding ASN.1 type is IA5String.  The rule evaluates to TRUE if and only if the prepared attribute value character string and the prepared assertion value character string have the same number of characters and corresponding characters have the same code point.  In preparing the attribute value and assertion value for comparison, characters are case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.1466.109.114.3 NAME 'caseIgnoreIA5SubstringsMatch' DESC 'The caseIgnoreIA5SubstringsMatch rule compares an assertion value of the Substring Assertion syntax to an attribute value of a syntax (e.g., the IA5 String syntax) whose corresponding ASN.1 type is IA5String.  The rule evaluates to TRUE if and only if (1) the prepared substrings of the assertion value match disjoint portions of the prepared attribute value character string in the order of the substrings in the assertion value, (2) an <initial> substring, if present, matches the beginning of the prepared attribute value character string, and (3) a <final> substring, if present, matches the end of the prepared attribute value character string.  A prepared substring matches a portion of the prepared attribute value character string if corresponding characters have the same code point.  In preparing the attribute value and assertion value substrings for comparison, characters are case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.14************.58 )",
            "( 2.5.13.2 NAME 'caseIgnoreMatch' DESC 'The caseIgnoreMatch rule compares an assertion value of the Directory String syntax to an attribute value of a syntax (e.g., the Directory String, Printable String, Country String, or Telephone Number syntax) whose corresponding ASN.1 type is DirectoryString or one of its alternative string types.  The rule evaluates to TRUE if and only if the prepared attribute value character string and the prepared assertion value character string have the same number of characters and corresponding characters have the same code point. In preparing the attribute value and assertion value for comparison, characters are case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.1466.************ )",
            "( 2.5.13.3 NAME 'caseIgnoreOrderingMatch' DESC 'The caseIgnoreOrderingMatch rule compares an assertion value of the Directory String syntax to an attribute value of a syntax (e.g., the Directory String, Printable String, Country String, or Telephone Number syntax) whose corresponding ASN.1 type is DirectoryString or one of its alternative string types. The rule evaluates to TRUE if and only if, in the code point collation order, the prepared attribute value character string appears earlier than the prepared assertion value character string; i.e., the attribute value is \\"less than\\" the assertion value. In preparing the attribute value and assertion value for comparison, characters are case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.1466.************ )",
            "( 2.5.13.4 NAME 'caseIgnoreSubstringsMatch' DESC 'The caseIgnoreSubstringsMatch rule compares an assertion value of the Substring Assertion syntax to an attribute value of a syntax (e.g., the Directory String, Printable String, Country String, or Telephone Number syntax) whose corresponding ASN.1 type is DirectoryString or one of its alternative string types. The rule evaluates to TRUE if and only if (1) the prepared substrings of the assertion value match disjoint portions of the prepared attribute value character string in the order of the substrings in the assertion value, (2) an <initial> substring, if present, matches the beginning of the prepared attribute value character string, and (3) a <final> substring, if present, matches the end of the prepared attribute value character string.  A prepared substring matches a portion of the prepared attribute value character string if corresponding characters have the same code point. In preparing the attribute value and assertion value substrings for comparison, characters are case folded in the Map preparation step, and only Insignificant Space Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.14************.58 )",
            "( ********1 NAME 'caseIgnoreListMatch' DESC 'The caseIgnoreListMatch rule compares an assertion value that is a sequence of strings to an attribute value of a syntax (e.g., the Postal Address syntax) whose corresponding ASN.1 type is a SEQUENCE OF the DirectoryString ASN.1 type. The rule evaluates to TRUE if and only if the attribute value and the assertion value have the same number of strings and corresponding strings (by position) match according to the caseIgnoreMatch matching rule. In [X.520], the assertion syntax for this matching rule is defined to be:       SEQUENCE OF DirectoryString {ub-match} That is, it is different from the corresponding type for the Postal Address syntax.  The choice of the Postal Address syntax for the assertion syntax of the caseIgnoreListMatch in LDAP should not be seen as limiting the matching rule to apply only to attributes with the Postal Address syntax.' SYNTAX *******.4.1.14************.41 )",
            "( ********2 NAME 'caseIgnoreListSubstringsMatch' DESC 'The caseIgnoreListSubstringsMatch rule compares an assertion value of the Substring Assertion syntax to an attribute value of a syntax (e.g., the Postal Address syntax) whose corresponding ASN.1 type is a SEQUENCE OF the DirectoryString ASN.1 type. The rule evaluates to TRUE if and only if the assertion value matches, per the caseIgnoreSubstringsMatch rule, the character string formed by concatenating the strings of the attribute value, except that none of the <initial>, <any>, or <final> substrings of the assertion value are considered to match a substring of the concatenated string which spans more than one of the original strings of the attribute value. Note that, in terms of the LDAP-specific encoding of the Postal Address syntax, the concatenated string omits the <DOLLAR> line separator and the escaping of \\"\\\\\\" and \\"$\\" characters.' SYNTAX *******.4.1.14************.58 )",
            "( ******** NAME 'objectIdentifierMatch' DESC 'The objectIdentifierMatch rule compares an assertion value of the OID syntax to an attribute value of a syntax (e.g., the OID syntax) whose corresponding ASN.1 type is OBJECT IDENTIFIER. The rule evaluates to TRUE if and only if the assertion value and the attribute value represent the same object identifier; that is, the same sequence of integers, whether represented explicitly in the <numericoid> form of <oid> or implicitly in the <descr> form (see [RFC4512]). If an LDAP client supplies an assertion value in the <descr> form and the chosen descriptor is not recognized by the server, then the objectIdentifierMatch rule evaluates to Undefined.' SYNTAX *******.4.1.14************.38 )",
            "( ********* NAME 'directoryStringFirstComponentMatch' DESC 'The directoryStringFirstComponentMatch rule compares an assertion value of the Directory String syntax to an attribute value of a syntax whose corresponding ASN.1 type is a SEQUENCE with a mandatory first component of the DirectoryString ASN.1 type. Note that the assertion syntax of this matching rule differs from the attribute syntax of attributes for which this is the equality matching rule. The rule evaluates to TRUE if and only if the assertion value matches the first component of the attribute value using the rules of caseIgnoreMatch.' SYNTAX *******.4.1.1466.************ )",
            "( ********* NAME 'objectIdentifierFirstComponentMatch' DESC 'The objectIdentifierFirstComponentMatch rule compares an assertion value of the OID syntax to an attribute value of a syntax (e.g., the Attribute Type Description, DIT Content Rule Description, LDAP Syntax Description, Matching Rule Description, Matching Rule Use Description, Name Form Description, or Object Class Description syntax) whose corresponding ASN.1 type is a SEQUENCE with a mandatory first component of the OBJECT IDENTIFIER ASN.1 type. Note that the assertion syntax of this matching rule differs from the attribute syntax of attributes for which this is the equality matching rule. The rule evaluates to TRUE if and only if the assertion value matches the first component of the attribute value using the rules of objectIdentifierMatch.' SYNTAX *******.4.1.14************.38 )",
            "( ******** NAME 'distinguishedNameMatch' DESC 'The distinguishedNameMatch rule compares an assertion value of the DN syntax to an attribute value of a syntax (e.g., the DN syntax) whose corresponding ASN.1 type is DistinguishedName. The rule evaluates to TRUE if and only if the attribute value and the assertion value have the same number of relative distinguished names and corresponding relative distinguished names (by position) are the same.  A relative distinguished name (RDN) of the assertion value is the same as an RDN of the attribute value if and only if they have the same number of attribute value assertions and each attribute value assertion (AVA) of the first RDN is the same as the AVA of the second RDN with the same attribute type.  The order of the AVAs is not significant.  Also note that a particular attribute type may appear in at most one AVA in an RDN.  Two AVAs with the same attribute type are the same if their values are equal according to the equality matching rule of the attribute type.  If one or more of the AVA comparisons evaluate to Undefined and the remaining AVA comparisons return TRUE then the distinguishedNameMatch rule evaluates to Undefined.' SYNTAX *******.4.1.1466.************ )",
            "( ********4 NAME 'integerMatch' DESC 'The rule evaluates to TRUE if and only if the attribute value and the assertion value are the same integer value.' SYNTAX *******.4.1.1466.************ )",
            "( ********5 NAME 'integerOrderingMatch' DESC 'The rule evaluates to TRUE if and only if the integer value of the attribute value is less than the integer value of the assertion value.' SYNTAX *******.4.1.1466.************ )",
            "( ********* NAME 'integerFirstComponentMatch' DESC 'The integerFirstComponentMatch rule compares an assertion value of the Integer syntax to an attribute value of a syntax (e.g., the DIT Structure Rule Description syntax) whose corresponding ASN.1 type is a SEQUENCE with a mandatory first component of the INTEGER ASN.1 type.  Note that the assertion syntax of this matching rule differs from the attribute syntax of attributes for which this is the equality matching rule.  The rule evaluates to TRUE if and only if the assertion value and the first component of the attribute value are the same integer value.' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******.1 NAME 'caseIgnoreOrderingMatch-default' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******.1.6 NAME 'caseIgnoreSubstringMatch-default' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******.1 NAME 'caseIgnoreOrderingMatch-ar' DESC 'ar' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******.1.6 NAME 'caseIgnoreSubstringMatch-ar' DESC 'ar' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******.1 NAME 'caseIgnoreOrderingMatch-be' DESC 'be' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******.1.6 NAME 'caseIgnoreSubstringMatch-be' DESC 'be' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.3.1 NAME 'caseIgnoreOrderingMatch-bg' DESC 'bg' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.3.1.6 NAME 'caseIgnoreSubstringMatch-bg' DESC 'bg' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.4.1 NAME 'caseIgnoreOrderingMatch-ca' DESC 'ca' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.4.1.6 NAME 'caseIgnoreSubstringMatch-ca' DESC 'ca' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.5.1 NAME 'caseIgnoreOrderingMatch-cs' DESC 'cs' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.5.1.6 NAME 'caseIgnoreSubstringMatch-cs' DESC 'cs' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.6.1 NAME 'caseIgnoreOrderingMatch-da' DESC 'da' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.6.1.6 NAME 'caseIgnoreSubstringMatch-da' DESC 'da' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.7.1 NAME 'caseIgnoreOrderingMatch-de' DESC 'de' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.7.1.6 NAME 'caseIgnoreSubstringMatch-de' DESC 'de' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.8.1 NAME 'caseIgnoreOrderingMatch-de-AT' DESC 'de-AT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.8.1.6 NAME 'caseIgnoreSubstringMatch-de-AT' DESC 'de-AT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.9.1 NAME 'caseIgnoreOrderingMatch-de-CH' DESC 'de-CH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.9.1.6 NAME 'caseIgnoreSubstringMatch-de-CH' DESC 'de-CH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******0.1 NAME 'caseIgnoreOrderingMatch-el' DESC 'el' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******0.1.6 NAME 'caseIgnoreSubstringMatch-el' DESC 'el' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******1.1 NAME 'caseIgnoreOrderingMatch-en' DESC 'en' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******1.1.6 NAME 'caseIgnoreSubstringMatch-en' DESC 'en' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******2.1 NAME 'caseIgnoreOrderingMatch-en-CA' DESC 'en-CA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******2.1.6 NAME 'caseIgnoreSubstringMatch-en-CA' DESC 'en-CA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******3.1 NAME 'caseIgnoreOrderingMatch-en-GB' DESC 'en-GB' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******3.1.6 NAME 'caseIgnoreSubstringMatch-en-GB' DESC 'en-GB' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******4.1 NAME 'caseIgnoreOrderingMatch-en-IE' DESC 'en-IE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******4.1.6 NAME 'caseIgnoreSubstringMatch-en-IE' DESC 'en-IE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******5.1 NAME 'caseIgnoreOrderingMatch-es' DESC 'es' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******5.1.6 NAME 'caseIgnoreSubstringMatch-es' DESC 'es' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******6.1 NAME 'caseIgnoreOrderingMatch-et' DESC 'et' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******6.1.6 NAME 'caseIgnoreSubstringMatch-et' DESC 'et' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******7.1 NAME 'caseIgnoreOrderingMatch-fi' DESC 'fi' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******7.1.6 NAME 'caseIgnoreSubstringMatch-fi' DESC 'fi' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******8.1 NAME 'caseIgnoreOrderingMatch-fr' DESC 'fr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******8.1.6 NAME 'caseIgnoreSubstringMatch-fr' DESC 'fr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******9.1 NAME 'caseIgnoreOrderingMatch-fr-BE' DESC 'fr-BE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******9.1.6 NAME 'caseIgnoreSubstringMatch-fr-BE' DESC 'fr-BE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******0.1 NAME 'caseIgnoreOrderingMatch-fr-CA' DESC 'fr-CA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******0.1.6 NAME 'caseIgnoreSubstringMatch-fr-CA' DESC 'fr-CA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******1.1 NAME 'caseIgnoreOrderingMatch-fr-CH' DESC 'fr-CH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******1.1.6 NAME 'caseIgnoreSubstringMatch-fr-CH' DESC 'fr-CH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******2.1 NAME 'caseIgnoreOrderingMatch-hr' DESC 'hr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******2.1.6 NAME 'caseIgnoreSubstringMatch-hr' DESC 'hr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******3.1 NAME 'caseIgnoreOrderingMatch-hu' DESC 'hu' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******3.1.6 NAME 'caseIgnoreSubstringMatch-hu' DESC 'hu' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******4.1 NAME 'caseIgnoreOrderingMatch-is' DESC 'is' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******4.1.6 NAME 'caseIgnoreSubstringMatch-is' DESC 'is' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******5.1 NAME 'caseIgnoreOrderingMatch-it' DESC 'it' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******5.1.6 NAME 'caseIgnoreSubstringMatch-it' DESC 'it' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******6.1 NAME 'caseIgnoreOrderingMatch-it-CH' DESC 'it-CH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******6.1.6 NAME 'caseIgnoreSubstringMatch-it-CH' DESC 'it-CH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******7.1 NAME 'caseIgnoreOrderingMatch-iw' DESC 'iw' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******7.1.6 NAME 'caseIgnoreSubstringMatch-iw' DESC 'iw' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******8.1 NAME 'caseIgnoreOrderingMatch-ja' DESC 'ja' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******8.1.6 NAME 'caseIgnoreSubstringMatch-ja' DESC 'ja' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******9.1 NAME 'caseIgnoreOrderingMatch-ko' DESC 'ko' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******9.1.6 NAME 'caseIgnoreSubstringMatch-ko' DESC 'ko' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.30.1 NAME 'caseIgnoreOrderingMatch-lt' DESC 'lt' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.30.1.6 NAME 'caseIgnoreSubstringMatch-lt' DESC 'lt' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.31.1 NAME 'caseIgnoreOrderingMatch-lv' DESC 'lv' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.31.1.6 NAME 'caseIgnoreSubstringMatch-lv' DESC 'lv' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.32.1 NAME 'caseIgnoreOrderingMatch-mk' DESC 'mk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.32.1.6 NAME 'caseIgnoreSubstringMatch-mk' DESC 'mk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.33.1 NAME 'caseIgnoreOrderingMatch-nl' DESC 'nl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.33.1.6 NAME 'caseIgnoreSubstringMatch-nl' DESC 'nl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.34.1 NAME 'caseIgnoreOrderingMatch-nl-BE' DESC 'nl-BE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.34.1.6 NAME 'caseIgnoreSubstringMatch-nl-BE' DESC 'nl-BE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.35.1 NAME 'caseIgnoreOrderingMatch-no' DESC 'no' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.35.1.6 NAME 'caseIgnoreSubstringMatch-no' DESC 'no' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.36.1 NAME 'caseIgnoreOrderingMatch-no-NO-B' DESC 'no-NO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.36.1.6 NAME 'caseIgnoreSubstringMatch-no-NO-B' DESC 'no-NO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.37.1 NAME 'caseIgnoreOrderingMatch-no-NO-NY' DESC 'no-NO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.37.1.6 NAME 'caseIgnoreSubstringMatch-no-NO-NY' DESC 'no-NO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.38.1 NAME 'caseIgnoreOrderingMatch-pl' DESC 'pl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.38.1.6 NAME 'caseIgnoreSubstringMatch-pl' DESC 'pl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.39.1 NAME 'caseIgnoreOrderingMatch-ro' DESC 'ro' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.39.1.6 NAME 'caseIgnoreSubstringMatch-ro' DESC 'ro' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.40.1 NAME 'caseIgnoreOrderingMatch-ru' DESC 'ru' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.40.1.6 NAME 'caseIgnoreSubstringMatch-ru' DESC 'ru' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.41.1 NAME 'caseIgnoreOrderingMatch-sh' DESC 'sh' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.41.1.6 NAME 'caseIgnoreSubstringMatch-sh' DESC 'sh' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.42.1 NAME 'caseIgnoreOrderingMatch-sk' DESC 'sk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.42.1.6 NAME 'caseIgnoreSubstringMatch-sk' DESC 'sk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.43.1 NAME 'caseIgnoreOrderingMatch-sl' DESC 'sl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.43.1.6 NAME 'caseIgnoreSubstringMatch-sl' DESC 'sl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.44.1 NAME 'caseIgnoreOrderingMatch-sq' DESC 'sq' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.44.1.6 NAME 'caseIgnoreSubstringMatch-sq' DESC 'sq' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.45.1 NAME 'caseIgnoreOrderingMatch-sr' DESC 'sr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.45.1.6 NAME 'caseIgnoreSubstringMatch-sr' DESC 'sr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.46.1 NAME 'caseIgnoreOrderingMatch-sv' DESC 'sv' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.46.1.6 NAME 'caseIgnoreSubstringMatch-sv' DESC 'sv' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.47.1 NAME 'caseIgnoreOrderingMatch-tr' DESC 'tr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.47.1.6 NAME 'caseIgnoreSubstringMatch-tr' DESC 'tr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.48.1 NAME 'caseIgnoreOrderingMatch-uk' DESC 'uk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.48.1.6 NAME 'caseIgnoreSubstringMatch-uk' DESC 'uk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.49.1 NAME 'caseIgnoreOrderingMatch-zh' DESC 'zh' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.49.1.6 NAME 'caseIgnoreSubstringMatch-zh' DESC 'zh' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.50.1 NAME 'caseIgnoreOrderingMatch-zh-TW' DESC 'zh-TW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.50.1.6 NAME 'caseIgnoreSubstringMatch-zh-TW' DESC 'zh-TW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.51.1 NAME 'caseIgnoreOrderingMatch-af' DESC 'af' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.51.1.6 NAME 'caseIgnoreSubstringMatch-af' DESC 'af' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.52.1 NAME 'caseIgnoreOrderingMatch-af-NA' DESC 'af-NA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.52.1.6 NAME 'caseIgnoreSubstringMatch-af-NA' DESC 'af-NA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.53.1 NAME 'caseIgnoreOrderingMatch-af-ZA' DESC 'af-ZA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.53.1.6 NAME 'caseIgnoreSubstringMatch-af-ZA' DESC 'af-ZA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.54.1 NAME 'caseIgnoreOrderingMatch-ar-AE' DESC 'ar-AE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.54.1.6 NAME 'caseIgnoreSubstringMatch-ar-AE' DESC 'ar-AE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.55.1 NAME 'caseIgnoreOrderingMatch-ar-BH' DESC 'ar-BH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.55.1.6 NAME 'caseIgnoreSubstringMatch-ar-BH' DESC 'ar-BH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.56.1 NAME 'caseIgnoreOrderingMatch-ar-DZ' DESC 'ar-DZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.56.1.6 NAME 'caseIgnoreSubstringMatch-ar-DZ' DESC 'ar-DZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.57.1 NAME 'caseIgnoreOrderingMatch-ar-EG' DESC 'ar-EG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.57.1.6 NAME 'caseIgnoreSubstringMatch-ar-EG' DESC 'ar-EG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.58.1 NAME 'caseIgnoreOrderingMatch-ar-IQ' DESC 'ar-IQ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.58.1.6 NAME 'caseIgnoreSubstringMatch-ar-IQ' DESC 'ar-IQ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.59.1 NAME 'caseIgnoreOrderingMatch-ar-JO' DESC 'ar-JO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.59.1.6 NAME 'caseIgnoreSubstringMatch-ar-JO' DESC 'ar-JO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.60.1 NAME 'caseIgnoreOrderingMatch-ar-KW' DESC 'ar-KW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.60.1.6 NAME 'caseIgnoreSubstringMatch-ar-KW' DESC 'ar-KW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.61.1 NAME 'caseIgnoreOrderingMatch-ar-LB' DESC 'ar-LB' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.61.1.6 NAME 'caseIgnoreSubstringMatch-ar-LB' DESC 'ar-LB' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.62.1 NAME 'caseIgnoreOrderingMatch-ar-LY' DESC 'ar-LY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.62.1.6 NAME 'caseIgnoreSubstringMatch-ar-LY' DESC 'ar-LY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.63.1 NAME 'caseIgnoreOrderingMatch-ar-MA' DESC 'ar-MA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.63.1.6 NAME 'caseIgnoreSubstringMatch-ar-MA' DESC 'ar-MA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.64.1 NAME 'caseIgnoreOrderingMatch-ar-OM' DESC 'ar-OM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.64.1.6 NAME 'caseIgnoreSubstringMatch-ar-OM' DESC 'ar-OM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.65.1 NAME 'caseIgnoreOrderingMatch-ar-QA' DESC 'ar-QA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.65.1.6 NAME 'caseIgnoreSubstringMatch-ar-QA' DESC 'ar-QA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.66.1 NAME 'caseIgnoreOrderingMatch-ar-SA' DESC 'ar-SA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.66.1.6 NAME 'caseIgnoreSubstringMatch-ar-SA' DESC 'ar-SA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.67.1 NAME 'caseIgnoreOrderingMatch-ar-SD' DESC 'ar-SD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.67.1.6 NAME 'caseIgnoreSubstringMatch-ar-SD' DESC 'ar-SD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.68.1 NAME 'caseIgnoreOrderingMatch-ar-SY' DESC 'ar-SY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.68.1.6 NAME 'caseIgnoreSubstringMatch-ar-SY' DESC 'ar-SY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.69.1 NAME 'caseIgnoreOrderingMatch-ar-TN' DESC 'ar-TN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.69.1.6 NAME 'caseIgnoreSubstringMatch-ar-TN' DESC 'ar-TN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.70.1 NAME 'caseIgnoreOrderingMatch-ar-YE' DESC 'ar-YE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.70.1.6 NAME 'caseIgnoreSubstringMatch-ar-YE' DESC 'ar-YE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.71.1 NAME 'caseIgnoreOrderingMatch-as' DESC 'as' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.71.1.6 NAME 'caseIgnoreSubstringMatch-as' DESC 'as' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.72.1 NAME 'caseIgnoreOrderingMatch-as-IN' DESC 'as-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.72.1.6 NAME 'caseIgnoreSubstringMatch-as-IN' DESC 'as-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.73.1 NAME 'caseIgnoreOrderingMatch-az' DESC 'az' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.73.1.6 NAME 'caseIgnoreSubstringMatch-az' DESC 'az' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.74.1 NAME 'caseIgnoreOrderingMatch-az-Latn' DESC 'az-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.74.1.6 NAME 'caseIgnoreSubstringMatch-az-Latn' DESC 'az-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.75.1 NAME 'caseIgnoreOrderingMatch-az-Latn-AZ' DESC 'az-Latn_AZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.75.1.6 NAME 'caseIgnoreSubstringMatch-az-Latn-AZ' DESC 'az-Latn_AZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.76.1 NAME 'caseIgnoreOrderingMatch-bn' DESC 'bn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.76.1.6 NAME 'caseIgnoreSubstringMatch-bn' DESC 'bn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.77.1 NAME 'caseIgnoreOrderingMatch-bn-BD' DESC 'bn-BD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.77.1.6 NAME 'caseIgnoreSubstringMatch-bn-BD' DESC 'bn-BD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.78.1 NAME 'caseIgnoreOrderingMatch-bn-IN' DESC 'bn-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.78.1.6 NAME 'caseIgnoreSubstringMatch-bn-IN' DESC 'bn-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.79.1 NAME 'caseIgnoreOrderingMatch-bs' DESC 'bs' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.79.1.6 NAME 'caseIgnoreSubstringMatch-bs' DESC 'bs' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.80.1 NAME 'caseIgnoreOrderingMatch-chr' DESC 'chr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.80.1.6 NAME 'caseIgnoreSubstringMatch-chr' DESC 'chr' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.81.1 NAME 'caseIgnoreOrderingMatch-chr-US' DESC 'chr-US' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.81.1.6 NAME 'caseIgnoreSubstringMatch-chr-US' DESC 'chr-US' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.82.1 NAME 'caseIgnoreOrderingMatch-cy' DESC 'cy' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.82.1.6 NAME 'caseIgnoreSubstringMatch-cy' DESC 'cy' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.83.1 NAME 'caseIgnoreOrderingMatch-de-BE' DESC 'de-BE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.83.1.6 NAME 'caseIgnoreSubstringMatch-de-BE' DESC 'de-BE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.84.1 NAME 'caseIgnoreOrderingMatch-de-LI' DESC 'de-LI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.84.1.6 NAME 'caseIgnoreSubstringMatch-de-LI' DESC 'de-LI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.85.1 NAME 'caseIgnoreOrderingMatch-de-LU' DESC 'de-LU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.85.1.6 NAME 'caseIgnoreSubstringMatch-de-LU' DESC 'de-LU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.86.1 NAME 'caseIgnoreOrderingMatch-el-CY' DESC 'el-CY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.86.1.6 NAME 'caseIgnoreSubstringMatch-el-CY' DESC 'el-CY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.87.1 NAME 'caseIgnoreOrderingMatch-el-GR' DESC 'el-GR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.87.1.6 NAME 'caseIgnoreSubstringMatch-el-GR' DESC 'el-GR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.88.1 NAME 'caseIgnoreOrderingMatch-en-AS' DESC 'en-AS' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.88.1.6 NAME 'caseIgnoreSubstringMatch-en-AS' DESC 'en-AS' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.89.1 NAME 'caseIgnoreOrderingMatch-en-AU' DESC 'en-AU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.89.1.6 NAME 'caseIgnoreSubstringMatch-en-AU' DESC 'en-AU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.90.1 NAME 'caseIgnoreOrderingMatch-en-BE' DESC 'en-BE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.90.1.6 NAME 'caseIgnoreSubstringMatch-en-BE' DESC 'en-BE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.91.1 NAME 'caseIgnoreOrderingMatch-en-BW' DESC 'en-BW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.91.1.6 NAME 'caseIgnoreSubstringMatch-en-BW' DESC 'en-BW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.92.1 NAME 'caseIgnoreOrderingMatch-en-BZ' DESC 'en-BZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.92.1.6 NAME 'caseIgnoreSubstringMatch-en-BZ' DESC 'en-BZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.93.1 NAME 'caseIgnoreOrderingMatch-en-GU' DESC 'en-GU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.93.1.6 NAME 'caseIgnoreSubstringMatch-en-GU' DESC 'en-GU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.94.1 NAME 'caseIgnoreOrderingMatch-en-GY' DESC 'en-GY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.94.1.6 NAME 'caseIgnoreSubstringMatch-en-GY' DESC 'en-GY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.95.1 NAME 'caseIgnoreOrderingMatch-en-HK' DESC 'en-HK' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.95.1.6 NAME 'caseIgnoreSubstringMatch-en-HK' DESC 'en-HK' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.96.1 NAME 'caseIgnoreOrderingMatch-en-IN' DESC 'en-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.96.1.6 NAME 'caseIgnoreSubstringMatch-en-IN' DESC 'en-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.97.1 NAME 'caseIgnoreOrderingMatch-en-JM' DESC 'en-JM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.97.1.6 NAME 'caseIgnoreSubstringMatch-en-JM' DESC 'en-JM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.98.1 NAME 'caseIgnoreOrderingMatch-en-MH' DESC 'en-MH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.98.1.6 NAME 'caseIgnoreSubstringMatch-en-MH' DESC 'en-MH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.99.1 NAME 'caseIgnoreOrderingMatch-en-MP' DESC 'en-MP' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.3.2.99.1.6 NAME 'caseIgnoreSubstringMatch-en-MP' DESC 'en-MP' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******00.1 NAME 'caseIgnoreOrderingMatch-en-MT' DESC 'en-MT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******00.1.6 NAME 'caseIgnoreSubstringMatch-en-MT' DESC 'en-MT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******01.1 NAME 'caseIgnoreOrderingMatch-en-MU' DESC 'en-MU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******01.1.6 NAME 'caseIgnoreSubstringMatch-en-MU' DESC 'en-MU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******02.1 NAME 'caseIgnoreOrderingMatch-en-NA' DESC 'en-NA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******02.1.6 NAME 'caseIgnoreSubstringMatch-en-NA' DESC 'en-NA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******03.1 NAME 'caseIgnoreOrderingMatch-en-NZ' DESC 'en-NZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******03.1.6 NAME 'caseIgnoreSubstringMatch-en-NZ' DESC 'en-NZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******04.1 NAME 'caseIgnoreOrderingMatch-en-PH' DESC 'en-PH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******04.1.6 NAME 'caseIgnoreSubstringMatch-en-PH' DESC 'en-PH' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******05.1 NAME 'caseIgnoreOrderingMatch-en-PK' DESC 'en-PK' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******05.1.6 NAME 'caseIgnoreSubstringMatch-en-PK' DESC 'en-PK' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******06.1 NAME 'caseIgnoreOrderingMatch-en-SG' DESC 'en-SG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******06.1.6 NAME 'caseIgnoreSubstringMatch-en-SG' DESC 'en-SG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******07.1 NAME 'caseIgnoreOrderingMatch-en-TT' DESC 'en-TT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******07.1.6 NAME 'caseIgnoreSubstringMatch-en-TT' DESC 'en-TT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******08.1 NAME 'caseIgnoreOrderingMatch-en-UM' DESC 'en-UM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******08.1.6 NAME 'caseIgnoreSubstringMatch-en-UM' DESC 'en-UM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******09.1 NAME 'caseIgnoreOrderingMatch-en-US' DESC 'en-US' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******09.1.6 NAME 'caseIgnoreSubstringMatch-en-US' DESC 'en-US' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******10.1 NAME 'caseIgnoreOrderingMatch-en-US-POSIX' DESC 'en-US' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******10.1.6 NAME 'caseIgnoreSubstringMatch-en-US-POSIX' DESC 'en-US' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******11.1 NAME 'caseIgnoreOrderingMatch-en-VI' DESC 'en-VI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******11.1.6 NAME 'caseIgnoreSubstringMatch-en-VI' DESC 'en-VI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******12.1 NAME 'caseIgnoreOrderingMatch-en-ZA' DESC 'en-ZA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******12.1.6 NAME 'caseIgnoreSubstringMatch-en-ZA' DESC 'en-ZA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******13.1 NAME 'caseIgnoreOrderingMatch-en-ZW' DESC 'en-ZW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******13.1.6 NAME 'caseIgnoreSubstringMatch-en-ZW' DESC 'en-ZW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******14.1 NAME 'caseIgnoreOrderingMatch-es-AR' DESC 'es-AR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******14.1.6 NAME 'caseIgnoreSubstringMatch-es-AR' DESC 'es-AR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******15.1 NAME 'caseIgnoreOrderingMatch-es-BO' DESC 'es-BO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******15.1.6 NAME 'caseIgnoreSubstringMatch-es-BO' DESC 'es-BO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******16.1 NAME 'caseIgnoreOrderingMatch-es-CL' DESC 'es-CL' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******16.1.6 NAME 'caseIgnoreSubstringMatch-es-CL' DESC 'es-CL' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******17.1 NAME 'caseIgnoreOrderingMatch-es-CO' DESC 'es-CO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******17.1.6 NAME 'caseIgnoreSubstringMatch-es-CO' DESC 'es-CO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******18.1 NAME 'caseIgnoreOrderingMatch-es-CR' DESC 'es-CR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******18.1.6 NAME 'caseIgnoreSubstringMatch-es-CR' DESC 'es-CR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******19.1 NAME 'caseIgnoreOrderingMatch-es-DO' DESC 'es-DO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******19.1.6 NAME 'caseIgnoreSubstringMatch-es-DO' DESC 'es-DO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******20.1 NAME 'caseIgnoreOrderingMatch-es-EC' DESC 'es-EC' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******20.1.6 NAME 'caseIgnoreSubstringMatch-es-EC' DESC 'es-EC' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******21.1 NAME 'caseIgnoreOrderingMatch-es-ES' DESC 'es-ES' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******21.1.6 NAME 'caseIgnoreSubstringMatch-es-ES' DESC 'es-ES' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******22.1 NAME 'caseIgnoreOrderingMatch-es-GQ' DESC 'es-GQ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******22.1.6 NAME 'caseIgnoreSubstringMatch-es-GQ' DESC 'es-GQ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******23.1 NAME 'caseIgnoreOrderingMatch-es-GT' DESC 'es-GT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******23.1.6 NAME 'caseIgnoreSubstringMatch-es-GT' DESC 'es-GT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******24.1 NAME 'caseIgnoreOrderingMatch-es-HN' DESC 'es-HN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******24.1.6 NAME 'caseIgnoreSubstringMatch-es-HN' DESC 'es-HN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******25.1 NAME 'caseIgnoreOrderingMatch-es-MX' DESC 'es-MX' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******25.1.6 NAME 'caseIgnoreSubstringMatch-es-MX' DESC 'es-MX' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******26.1 NAME 'caseIgnoreOrderingMatch-es-NI' DESC 'es-NI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******26.1.6 NAME 'caseIgnoreSubstringMatch-es-NI' DESC 'es-NI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******27.1 NAME 'caseIgnoreOrderingMatch-es-PA' DESC 'es-PA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******27.1.6 NAME 'caseIgnoreSubstringMatch-es-PA' DESC 'es-PA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******28.1 NAME 'caseIgnoreOrderingMatch-es-PE' DESC 'es-PE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******28.1.6 NAME 'caseIgnoreSubstringMatch-es-PE' DESC 'es-PE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******29.1 NAME 'caseIgnoreOrderingMatch-es-PR' DESC 'es-PR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******29.1.6 NAME 'caseIgnoreSubstringMatch-es-PR' DESC 'es-PR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******30.1 NAME 'caseIgnoreOrderingMatch-es-PY' DESC 'es-PY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******30.1.6 NAME 'caseIgnoreSubstringMatch-es-PY' DESC 'es-PY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******31.1 NAME 'caseIgnoreOrderingMatch-es-SV' DESC 'es-SV' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******31.1.6 NAME 'caseIgnoreSubstringMatch-es-SV' DESC 'es-SV' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******32.1 NAME 'caseIgnoreOrderingMatch-es-US' DESC 'es-US' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******32.1.6 NAME 'caseIgnoreSubstringMatch-es-US' DESC 'es-US' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******33.1 NAME 'caseIgnoreOrderingMatch-es-UY' DESC 'es-UY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******33.1.6 NAME 'caseIgnoreSubstringMatch-es-UY' DESC 'es-UY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******34.1 NAME 'caseIgnoreOrderingMatch-es-VE' DESC 'es-VE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******34.1.6 NAME 'caseIgnoreSubstringMatch-es-VE' DESC 'es-VE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******35.1 NAME 'caseIgnoreOrderingMatch-fa' DESC 'fa' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******35.1.6 NAME 'caseIgnoreSubstringMatch-fa' DESC 'fa' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******36.1 NAME 'caseIgnoreOrderingMatch-fil' DESC 'fil' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******36.1.6 NAME 'caseIgnoreSubstringMatch-fil' DESC 'fil' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******37.1 NAME 'caseIgnoreOrderingMatch-fo' DESC 'fo' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******37.1.6 NAME 'caseIgnoreSubstringMatch-fo' DESC 'fo' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******38.1 NAME 'caseIgnoreOrderingMatch-fr-BF' DESC 'fr-BF' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******38.1.6 NAME 'caseIgnoreSubstringMatch-fr-BF' DESC 'fr-BF' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******39.1 NAME 'caseIgnoreOrderingMatch-fr-BI' DESC 'fr-BI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******39.1.6 NAME 'caseIgnoreSubstringMatch-fr-BI' DESC 'fr-BI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******40.1 NAME 'caseIgnoreOrderingMatch-fr-BJ' DESC 'fr-BJ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******40.1.6 NAME 'caseIgnoreSubstringMatch-fr-BJ' DESC 'fr-BJ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******41.1 NAME 'caseIgnoreOrderingMatch-fr-BL' DESC 'fr-BL' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******41.1.6 NAME 'caseIgnoreSubstringMatch-fr-BL' DESC 'fr-BL' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******42.1 NAME 'caseIgnoreOrderingMatch-fr-CD' DESC 'fr-CD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******42.1.6 NAME 'caseIgnoreSubstringMatch-fr-CD' DESC 'fr-CD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******43.1 NAME 'caseIgnoreOrderingMatch-fr-CF' DESC 'fr-CF' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******43.1.6 NAME 'caseIgnoreSubstringMatch-fr-CF' DESC 'fr-CF' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******44.1 NAME 'caseIgnoreOrderingMatch-fr-CG' DESC 'fr-CG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******44.1.6 NAME 'caseIgnoreSubstringMatch-fr-CG' DESC 'fr-CG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******45.1 NAME 'caseIgnoreOrderingMatch-fr-CI' DESC 'fr-CI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******45.1.6 NAME 'caseIgnoreSubstringMatch-fr-CI' DESC 'fr-CI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******46.1 NAME 'caseIgnoreOrderingMatch-fr-CM' DESC 'fr-CM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******46.1.6 NAME 'caseIgnoreSubstringMatch-fr-CM' DESC 'fr-CM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******47.1 NAME 'caseIgnoreOrderingMatch-fr-DJ' DESC 'fr-DJ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******47.1.6 NAME 'caseIgnoreSubstringMatch-fr-DJ' DESC 'fr-DJ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******48.1 NAME 'caseIgnoreOrderingMatch-fr-GA' DESC 'fr-GA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******48.1.6 NAME 'caseIgnoreSubstringMatch-fr-GA' DESC 'fr-GA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******49.1 NAME 'caseIgnoreOrderingMatch-fr-GN' DESC 'fr-GN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******49.1.6 NAME 'caseIgnoreSubstringMatch-fr-GN' DESC 'fr-GN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******50.1 NAME 'caseIgnoreOrderingMatch-fr-GP' DESC 'fr-GP' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******50.1.6 NAME 'caseIgnoreSubstringMatch-fr-GP' DESC 'fr-GP' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******51.1 NAME 'caseIgnoreOrderingMatch-fr-GQ' DESC 'fr-GQ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******51.1.6 NAME 'caseIgnoreSubstringMatch-fr-GQ' DESC 'fr-GQ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******52.1 NAME 'caseIgnoreOrderingMatch-fr-KM' DESC 'fr-KM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******52.1.6 NAME 'caseIgnoreSubstringMatch-fr-KM' DESC 'fr-KM' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******53.1 NAME 'caseIgnoreOrderingMatch-fr-LU' DESC 'fr-LU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******53.1.6 NAME 'caseIgnoreSubstringMatch-fr-LU' DESC 'fr-LU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******54.1 NAME 'caseIgnoreOrderingMatch-fr-MC' DESC 'fr-MC' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******54.1.6 NAME 'caseIgnoreSubstringMatch-fr-MC' DESC 'fr-MC' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******55.1 NAME 'caseIgnoreOrderingMatch-fr-MF' DESC 'fr-MF' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******55.1.6 NAME 'caseIgnoreSubstringMatch-fr-MF' DESC 'fr-MF' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******56.1 NAME 'caseIgnoreOrderingMatch-fr-MG' DESC 'fr-MG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******56.1.6 NAME 'caseIgnoreSubstringMatch-fr-MG' DESC 'fr-MG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******57.1 NAME 'caseIgnoreOrderingMatch-fr-ML' DESC 'fr-ML' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******57.1.6 NAME 'caseIgnoreSubstringMatch-fr-ML' DESC 'fr-ML' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******58.1 NAME 'caseIgnoreOrderingMatch-fr-MQ' DESC 'fr-MQ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******58.1.6 NAME 'caseIgnoreSubstringMatch-fr-MQ' DESC 'fr-MQ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******59.1 NAME 'caseIgnoreOrderingMatch-fr-NE' DESC 'fr-NE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******59.1.6 NAME 'caseIgnoreSubstringMatch-fr-NE' DESC 'fr-NE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******60.1 NAME 'caseIgnoreOrderingMatch-fr-RE' DESC 'fr-RE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******60.1.6 NAME 'caseIgnoreSubstringMatch-fr-RE' DESC 'fr-RE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******61.1 NAME 'caseIgnoreOrderingMatch-fr-RW' DESC 'fr-RW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******61.1.6 NAME 'caseIgnoreSubstringMatch-fr-RW' DESC 'fr-RW' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******62.1 NAME 'caseIgnoreOrderingMatch-fr-SN' DESC 'fr-SN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******62.1.6 NAME 'caseIgnoreSubstringMatch-fr-SN' DESC 'fr-SN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******63.1 NAME 'caseIgnoreOrderingMatch-fr-TD' DESC 'fr-TD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******63.1.6 NAME 'caseIgnoreSubstringMatch-fr-TD' DESC 'fr-TD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******64.1 NAME 'caseIgnoreOrderingMatch-fr-TG' DESC 'fr-TG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******64.1.6 NAME 'caseIgnoreSubstringMatch-fr-TG' DESC 'fr-TG' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******65.1 NAME 'caseIgnoreOrderingMatch-ga' DESC 'ga' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******65.1.6 NAME 'caseIgnoreSubstringMatch-ga' DESC 'ga' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******66.1 NAME 'caseIgnoreOrderingMatch-ga-IE' DESC 'ga-IE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******66.1.6 NAME 'caseIgnoreSubstringMatch-ga-IE' DESC 'ga-IE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******67.1 NAME 'caseIgnoreOrderingMatch-ga-IN' DESC 'ga-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******67.1.6 NAME 'caseIgnoreSubstringMatch-ga-IN' DESC 'ga-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******68.1 NAME 'caseIgnoreOrderingMatch-ha' DESC 'ha' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******68.1.6 NAME 'caseIgnoreSubstringMatch-ha' DESC 'ha' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******69.1 NAME 'caseIgnoreOrderingMatch-ha-Latn' DESC 'ha-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******69.1.6 NAME 'caseIgnoreSubstringMatch-ha-Latn' DESC 'ha-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******70.1 NAME 'caseIgnoreOrderingMatch-ha-Latn-GH' DESC 'ha-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******70.1.6 NAME 'caseIgnoreSubstringMatch-ha-Latn-GH' DESC 'ha-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******71.1 NAME 'caseIgnoreOrderingMatch-ha-Latn-NE' DESC 'ha-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******71.1.6 NAME 'caseIgnoreSubstringMatch-ha-Latn-NE' DESC 'ha-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******72.1 NAME 'caseIgnoreOrderingMatch-ha-Latn-NG' DESC 'ha-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******72.1.6 NAME 'caseIgnoreSubstringMatch-ha-Latn-NG' DESC 'ha-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******73.1 NAME 'caseIgnoreOrderingMatch-he' DESC 'he' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******73.1.6 NAME 'caseIgnoreSubstringMatch-he' DESC 'he' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******74.1 NAME 'caseIgnoreOrderingMatch-hi' DESC 'hi' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******74.1.6 NAME 'caseIgnoreSubstringMatch-hi' DESC 'hi' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******75.1 NAME 'caseIgnoreOrderingMatch-hy' DESC 'hy' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******75.1.6 NAME 'caseIgnoreSubstringMatch-hy' DESC 'hy' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******76.1 NAME 'caseIgnoreOrderingMatch-id-ID' DESC 'id' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******76.1.6 NAME 'caseIgnoreSubstringMatch-id-ID' DESC 'id' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******77.1 NAME 'caseIgnoreOrderingMatch-ig-NG' DESC 'id-ID' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******77.1.6 NAME 'caseIgnoreSubstringMatch-ig-NG' DESC 'id-ID' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******78.1 NAME 'caseIgnoreOrderingMatch-it-IT' DESC 'it-IT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******78.1.6 NAME 'caseIgnoreSubstringMatch-it-IT' DESC 'it-IT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******79.1 NAME 'caseIgnoreOrderingMatch-ka' DESC 'ka' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******79.1.6 NAME 'caseIgnoreSubstringMatch-ka' DESC 'ka' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******80.1 NAME 'caseIgnoreOrderingMatch-ka-GE' DESC 'ka-GE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******80.1.6 NAME 'caseIgnoreSubstringMatch-ka-GE' DESC 'ka-GE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******81.1 NAME 'caseIgnoreOrderingMatch-kk' DESC 'kk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******81.1.6 NAME 'caseIgnoreSubstringMatch-kk' DESC 'kk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******82.1 NAME 'caseIgnoreOrderingMatch-kl' DESC 'kl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******82.1.6 NAME 'caseIgnoreSubstringMatch-kl' DESC 'kl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******83.1 NAME 'caseIgnoreOrderingMatch-kn' DESC 'kn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******83.1.6 NAME 'caseIgnoreSubstringMatch-kn' DESC 'kn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******84.1 NAME 'caseIgnoreOrderingMatch-kok' DESC 'kok' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******84.1.6 NAME 'caseIgnoreSubstringMatch-kok' DESC 'kok' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******85.1 NAME 'caseIgnoreOrderingMatch-ml' DESC 'ml' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******85.1.6 NAME 'caseIgnoreSubstringMatch-ml' DESC 'ml' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******86.1 NAME 'caseIgnoreOrderingMatch-ms' DESC 'ms' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******86.1.6 NAME 'caseIgnoreSubstringMatch-ms' DESC 'ms' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******87.1 NAME 'caseIgnoreOrderingMatch-ms-BN' DESC 'ms-BN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******87.1.6 NAME 'caseIgnoreSubstringMatch-ms-BN' DESC 'ms-BN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******88.1 NAME 'caseIgnoreOrderingMatch-ms-MY' DESC 'ms-MY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******88.1.6 NAME 'caseIgnoreSubstringMatch-ms-MY' DESC 'ms-MY' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******89.1 NAME 'caseIgnoreOrderingMatch-mt' DESC 'mt' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******89.1.6 NAME 'caseIgnoreSubstringMatch-mt' DESC 'mt' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******90.1 NAME 'caseIgnoreOrderingMatch-nl-NL' DESC 'nl-NL' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******90.1.6 NAME 'caseIgnoreSubstringMatch-nl-NL' DESC 'nl-NL' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******91.1 NAME 'caseIgnoreOrderingMatch-nn' DESC 'nn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******91.1.6 NAME 'caseIgnoreSubstringMatch-nn' DESC 'nn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******92.1 NAME 'caseIgnoreOrderingMatch-om' DESC 'om' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******92.1.6 NAME 'caseIgnoreSubstringMatch-om' DESC 'om' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******93.1 NAME 'caseIgnoreOrderingMatch-om-ET' DESC 'om-ET' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******93.1.6 NAME 'caseIgnoreSubstringMatch-om-ET' DESC 'om-ET' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******94.1 NAME 'caseIgnoreOrderingMatch-om-KE' DESC 'om-KE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******94.1.6 NAME 'caseIgnoreSubstringMatch-om-KE' DESC 'om-KE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******95.1 NAME 'caseIgnoreOrderingMatch-or' DESC 'or' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******95.1.6 NAME 'caseIgnoreSubstringMatch-or' DESC 'or' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******96.1 NAME 'caseIgnoreOrderingMatch-pa' DESC 'pa' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******96.1.6 NAME 'caseIgnoreSubstringMatch-pa' DESC 'pa' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******97.1 NAME 'caseIgnoreOrderingMatch-pa-Arab' DESC 'pa-Arab' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******97.1.6 NAME 'caseIgnoreSubstringMatch-pa-Arab' DESC 'pa-Arab' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******98.1 NAME 'caseIgnoreOrderingMatch-pa-Arab-PK' DESC 'pa-Arab' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******98.1.6 NAME 'caseIgnoreSubstringMatch-pa-Arab-PK' DESC 'pa-Arab' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******99.1 NAME 'caseIgnoreOrderingMatch-pa-Guru' DESC 'pa-Guru' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******99.1.6 NAME 'caseIgnoreSubstringMatch-pa-Guru' DESC 'pa-Guru' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******00.1 NAME 'caseIgnoreOrderingMatch-pa-Guru-IN' DESC 'pa-Guru' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******00.1.6 NAME 'caseIgnoreSubstringMatch-pa-Guru-IN' DESC 'pa-Guru' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******01.1 NAME 'caseIgnoreOrderingMatch-ps' DESC 'ps' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******01.1.6 NAME 'caseIgnoreSubstringMatch-ps' DESC 'ps' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******02.1 NAME 'caseIgnoreOrderingMatch-pt' DESC 'pt' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******02.1.6 NAME 'caseIgnoreSubstringMatch-pt' DESC 'pt' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******03.1 NAME 'caseIgnoreOrderingMatch-pt-BR' DESC 'pt-BR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******03.1.6 NAME 'caseIgnoreSubstringMatch-pt-BR' DESC 'pt-BR' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******04.1 NAME 'caseIgnoreOrderingMatch-pt-PT' DESC 'pt-PT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******04.1.6 NAME 'caseIgnoreSubstringMatch-pt-PT' DESC 'pt-PT' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******05.1 NAME 'caseIgnoreOrderingMatch-ro-MD' DESC 'ro-MD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******05.1.6 NAME 'caseIgnoreSubstringMatch-ro-MD' DESC 'ro-MD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******06.1 NAME 'caseIgnoreOrderingMatch-ro-RO' DESC 'ro-RO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******06.1.6 NAME 'caseIgnoreSubstringMatch-ro-RO' DESC 'ro-RO' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******07.1 NAME 'caseIgnoreOrderingMatch-ru-MD' DESC 'ru-MD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******07.1.6 NAME 'caseIgnoreSubstringMatch-ru-MD' DESC 'ru-MD' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******08.1 NAME 'caseIgnoreOrderingMatch-ru-RU' DESC 'ru-RU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******08.1.6 NAME 'caseIgnoreSubstringMatch-ru-RU' DESC 'ru-RU' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******09.1 NAME 'caseIgnoreOrderingMatch-ru-UA' DESC 'ru-UA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******09.1.6 NAME 'caseIgnoreSubstringMatch-ru-UA' DESC 'ru-UA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******10.1 NAME 'caseIgnoreOrderingMatch-si' DESC 'si' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******10.1.6 NAME 'caseIgnoreSubstringMatch-si' DESC 'si' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******11.1 NAME 'caseIgnoreOrderingMatch-sk' DESC 'sk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******11.1.6 NAME 'caseIgnoreSubstringMatch-sk' DESC 'sk' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******12.1 NAME 'caseIgnoreOrderingMatch-sl' DESC 'sl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******12.1.6 NAME 'caseIgnoreSubstringMatch-sl' DESC 'sl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******13.1 NAME 'caseIgnoreOrderingMatch-sq' DESC 'sq' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******13.1.6 NAME 'caseIgnoreSubstringMatch-sq' DESC 'sq' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******14.1 NAME 'caseIgnoreOrderingMatch-sr-Cyrl' DESC 'sr-Cyrl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******14.1.6 NAME 'caseIgnoreSubstringMatch-sr-Cyrl' DESC 'sr-Cyrl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******15.1 NAME 'caseIgnoreOrderingMatch-sr-Cyrl-BA' DESC 'sr-Cyrl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******15.1.6 NAME 'caseIgnoreSubstringMatch-sr-Cyrl-BA' DESC 'sr-Cyrl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******16.1 NAME 'caseIgnoreOrderingMatch-sr-Cyrl-ME' DESC 'sr-Cyrl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******16.1.6 NAME 'caseIgnoreSubstringMatch-sr-Cyrl-ME' DESC 'sr-Cyrl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******17.1 NAME 'caseIgnoreOrderingMatch-sr-Cyrl-RS' DESC 'sr-Cyrl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******17.1.6 NAME 'caseIgnoreSubstringMatch-sr-Cyrl-RS' DESC 'sr-Cyrl' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******18.1 NAME 'caseIgnoreOrderingMatch-sr-Latn' DESC 'sr-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******18.1.6 NAME 'caseIgnoreSubstringMatch-sr-Latn' DESC 'sr-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******19.1 NAME 'caseIgnoreOrderingMatch-sr-Latn-BA' DESC 'sr-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******19.1.6 NAME 'caseIgnoreSubstringMatch-sr-Latn-BA' DESC 'sr-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******20.1 NAME 'caseIgnoreOrderingMatch-sr-Latn-ME' DESC 'sr-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******20.1.6 NAME 'caseIgnoreSubstringMatch-sr-Latn-ME' DESC 'sr-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******21.1 NAME 'caseIgnoreOrderingMatch-sr-Latn-RS' DESC 'sr-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******21.1.6 NAME 'caseIgnoreSubstringMatch-sr-Latn-RS' DESC 'sr-Latn' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******22.1 NAME 'caseIgnoreOrderingMatch-sv-FI' DESC 'sv-FI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******22.1.6 NAME 'caseIgnoreSubstringMatch-sv-FI' DESC 'sv-FI' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******23.1 NAME 'caseIgnoreOrderingMatch-sv-SE' DESC 'sv-SE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******23.1.6 NAME 'caseIgnoreSubstringMatch-sv-SE' DESC 'sv-SE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******24.1 NAME 'caseIgnoreOrderingMatch-sw' DESC 'sw' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******24.1.6 NAME 'caseIgnoreSubstringMatch-sw' DESC 'sw' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******25.1 NAME 'caseIgnoreOrderingMatch-sw-KE' DESC 'sw-KE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******25.1.6 NAME 'caseIgnoreSubstringMatch-sw-KE' DESC 'sw-KE' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******26.1 NAME 'caseIgnoreOrderingMatch-sw-TZ' DESC 'sw-TZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******26.1.6 NAME 'caseIgnoreSubstringMatch-sw-TZ' DESC 'sw-TZ' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******27.1 NAME 'caseIgnoreOrderingMatch-ta' DESC 'ta' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******27.1.6 NAME 'caseIgnoreSubstringMatch-ta' DESC 'ta' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******28.1 NAME 'caseIgnoreOrderingMatch-ta-IN' DESC 'ta-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******28.1.6 NAME 'caseIgnoreSubstringMatch-ta-IN' DESC 'ta-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******29.1 NAME 'caseIgnoreOrderingMatch-ta-LK' DESC 'ta-LK' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******29.1.6 NAME 'caseIgnoreSubstringMatch-ta-LK' DESC 'ta-LK' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******30.1 NAME 'caseIgnoreOrderingMatch-te' DESC 'te' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******30.1.6 NAME 'caseIgnoreSubstringMatch-te' DESC 'te' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******31.1 NAME 'caseIgnoreOrderingMatch-th' DESC 'th' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******31.1.6 NAME 'caseIgnoreSubstringMatch-th' DESC 'th' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******32.1 NAME 'caseIgnoreOrderingMatch-ur' DESC 'ur' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******32.1.6 NAME 'caseIgnoreSubstringMatch-ur' DESC 'ur' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******33.1 NAME 'caseIgnoreOrderingMatch-ur-IN' DESC 'ur-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******33.1.6 NAME 'caseIgnoreSubstringMatch-ur-IN' DESC 'ur-IN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******34.1 NAME 'caseIgnoreOrderingMatch-ur-PK' DESC 'ur-PK' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******34.1.6 NAME 'caseIgnoreSubstringMatch-ur-PK' DESC 'ur-PK' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******35.1 NAME 'caseIgnoreOrderingMatch-vi' DESC 'vi' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******35.1.6 NAME 'caseIgnoreSubstringMatch-vi' DESC 'vi' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******36.1 NAME 'caseIgnoreOrderingMatch-yo' DESC 'yo' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******36.1.6 NAME 'caseIgnoreSubstringMatch-yo' DESC 'yo' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******37.1 NAME 'caseIgnoreOrderingMatch-zh-Hans' DESC 'zh-Hans' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******37.1.6 NAME 'caseIgnoreSubstringMatch-zh-Hans' DESC 'zh-Hans' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******38.1 NAME 'caseIgnoreOrderingMatch-zh-Hans-CN' DESC 'zh-Hans' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******38.1.6 NAME 'caseIgnoreSubstringMatch-zh-Hans-CN' DESC 'zh-Hans' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******39.1 NAME 'caseIgnoreOrderingMatch-zh-Hans-SG' DESC 'zh-Hans' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******39.1.6 NAME 'caseIgnoreSubstringMatch-zh-Hans-SG' DESC 'zh-Hans' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******40.1 NAME 'caseIgnoreOrderingMatch-zh-Hant-HK' DESC 'zh-Hant' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******40.1.6 NAME 'caseIgnoreSubstringMatch-zh-Hant-HK' DESC 'zh-Hant' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******41.1 NAME 'caseIgnoreOrderingMatch-zh-Hant-MO' DESC 'zh-Hant' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******41.1.6 NAME 'caseIgnoreSubstringMatch-zh-Hant-MO' DESC 'zh-Hant' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******42.1 NAME 'caseIgnoreOrderingMatch-zh-Hant-TW' DESC 'zh-Hant' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******42.1.6 NAME 'caseIgnoreSubstringMatch-zh-Hant-TW' DESC 'zh-Hant' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******43.1 NAME 'caseIgnoreOrderingMatch-zu' DESC 'zu' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******43.1.6 NAME 'caseIgnoreSubstringMatch-zu' DESC 'zu' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******44.1 NAME 'caseIgnoreOrderingMatch-zu-ZA' DESC 'zu-ZA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******44.1.6 NAME 'caseIgnoreSubstringMatch-zu-ZA' DESC 'zu-ZA' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******.3 SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******.3.6 SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******1.3 NAME 'caseExactOrderingMatch-en' DESC 'en' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.*******1.3.6 NAME 'caseExactSubstringMatch-en' DESC 'en' SYNTAX *******.4.1.1466.************ )",
            "( 2.5.13.23 NAME 'uniqueMemberMatch' DESC 'The uniqueMemberMatch rule compares an assertion value of the Name And Optional UID syntax to an attribute value of a syntax (e.g., the Name And Optional UID syntax) whose corresponding ASN.1 type is NameAndOptionalUID.  The rule evaluates to TRUE if and only if the <distinguishedName> components of the assertion value and attribute value match according to the distinguishedNameMatch rule and either, (1) the <BitString> component is absent from both the attribute value and assertion value, or (2) the <BitString> component is present in both the attribute value and the assertion value and the <BitString> component of the assertion value matches the <BitString> component of the attribute value according to the bitStringMatch rule.  Note that this matching rule has been altered from its description in X.520 [X.520] in order to make the matching rule commutative.  Server implementors should consider using the original X.520 semantics (where the matching was less exact) for approximate matching of attributes with uniqueMemberMatch as the equality matching rule.' SYNTAX *******.4.1.14************.34 )",
            "( ******** NAME 'numericStringMatch' DESC 'The rule evaluates to TRUE if and only if the prepared attribute value character string and the prepared assertion value character string have the same number of characters and corresponding characters have the same code point.' SYNTAX *******.4.1.14************.36 )",
            "( ******** NAME 'numericStringOrderingMatch' DESC 'The rule evaluates to TRUE if and only if, in the code point collation order, the prepared attribute value character string appears earlier than the prepared assertion value character string; i.e., the attribute value is less than the assertion value.' SYNTAX *******.4.1.14************.36 )",
            "( ********0 NAME 'numericStringSubstringsMatch' DESC 'The rule evaluates to TRUE if and only if (1) the prepared substrings of the assertion value match disjoint portions of the prepared attribute value, (2) an initial substring, if present, matches the beginning of the prepared attribute value character string, and (3) a final substring, if present, matches the end of the prepared attribute value character string.' SYNTAX *******.4.1.14************.58 )",
            "( 2.5.13.20 NAME 'telephoneNumberMatch' DESC 'The telephoneNumberMatch rule compares an assertion value of the Telephone Number syntax to an attribute value of a syntax (e.g., the Telephone Number syntax) whose corresponding ASN.1 type is a PrintableString representing a telephone number. The rule evaluates to TRUE if and only if the prepared attribute value character string and the prepared assertion value character string have the same number of characters and corresponding characters have the same code point. In preparing the attribute value and assertion value for comparison, characters are case folded in the Map preparation step, and only telephoneNumber Insignificant Character Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.1466.************ )",
            "( 2.5.13.21 NAME 'telephoneNumberSubstringsMatch' DESC 'The telephoneNumberSubstringsMatch rule compares an assertion value of the Substring Assertion syntax to an attribute value of a syntax (e.g., the Telephone Number syntax) whose corresponding ASN.1 type is a PrintableString representing a telephone number. The rule evaluates to TRUE if and only if (1) the prepared substrings of the assertion value match disjoint portions of the prepared attribute value character string in the order of the substrings in the assertion value, (2) an <initial> substring, if present, matches the beginning of the prepared attribute value character string, and (3) a <final> substring, if present, matches the end of the prepared attribute value character string.  A prepared substring matches a portion of the prepared attribute value character string if corresponding characters have the same code point. In preparing the attribute value and assertion value substrings for comparison, characters are case folded in the Map preparation step, and only telephoneNumber Insignificant Character Handling is applied in the Insignificant Character Handling step.' SYNTAX *******.4.1.14************.58 )"
        ],
        "objectClass": [
            "top",
            "ldapSubentry",
            "subschema"
        ],
        "objectClasses": [
            "( 2.5.6.0 NAME 'top' ABSTRACT MUST objectClass X-ORIGIN 'RFC 4512' )",
            "( ******* NAME 'alias' SUP top STRUCTURAL MUST aliasedObjectName X-ORIGIN 'RFC 4512' )",
            "( ******** NAME 'subschema' AUXILIARY MAY ( dITStructureRules $ nameForms $ dITContentRules $ objectClasses $ attributeTypes $ matchingRules $ matchingRuleUse ) X-ORIGIN 'RFC 4512' )",
            "( *******.4.1.1466.101.120.111 NAME 'extensibleObject' SUP top AUXILIARY X-ORIGIN 'RFC 4512' )",
            "( *******1 NAME 'applicationProcess' SUP top STRUCTURAL MUST cn MAY ( seeAlso $ ou $ l $ description ) X-ORIGIN 'RFC 4519' )",
            "( ******* NAME 'country' SUP top STRUCTURAL MUST c MAY ( searchGuide $ description ) X-ORIGIN 'RFC 4519' )",
            "( *******.4.1.1466.344 NAME 'dcObject' SUP top AUXILIARY MUST dc X-ORIGIN 'RFC 4519' )",
            "( *******4 NAME 'device' SUP top STRUCTURAL MUST cn MAY ( serialNumber $ seeAlso $ owner $ ou $ o $ l $ description ) X-ORIGIN 'RFC 4519' )",
            "( ******* NAME 'groupOfNames' SUP top STRUCTURAL MUST cn MAY ( member $ businessCategory $ seeAlso $ owner $ ou $ o $ description ) X-ORIGIN 'RFC 4519' )",
            "( *******7 NAME 'groupOfUniqueNames' SUP top STRUCTURAL MUST cn MAY ( uniqueMember $ businessCategory $ seeAlso $ owner $ ou $ o $ description ) X-ORIGIN 'RFC 4519' )",
            "( ******* NAME 'locality' SUP top STRUCTURAL MAY ( street $ seeAlso $ searchGuide $ st $ l $ description ) X-ORIGIN 'RFC 4519' )",
            "( ******* NAME 'organization' SUP top STRUCTURAL MUST o MAY ( userPassword $ searchGuide $ seeAlso $ businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationalISDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l $ description ) X-ORIGIN 'RFC 4519' )",
            "( ******* NAME 'person' SUP top STRUCTURAL MUST ( sn $ cn ) MAY ( userPassword $ telephoneNumber $ seeAlso $ description ) X-ORIGIN 'RFC 4519' )",
            "( ******* NAME 'organizationalPerson' SUP person STRUCTURAL MAY ( title $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ internationalISDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ ou $ st $ l ) X-ORIGIN 'RFC 4519' )",
            "( ******* NAME 'organizationalRole' SUP top STRUCTURAL MUST cn MAY ( x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationalISDNNumber $ facsimileTelephoneNumber $ seeAlso $ roleOccupant $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ ou $ st $ l $ description ) X-ORIGIN 'RFC 4519' )",
            "( ******* NAME 'organizationalUnit' SUP top STRUCTURAL MUST ou MAY ( businessCategory $ description $ destinationIndicator $ facsimileTelephoneNumber $ internationalISDNNumber $ l $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ preferredDeliveryMethod $ registeredAddress $ searchGuide $ seeAlso $ st $ street $ telephoneNumber $ teletexTerminalIdentifier $ telexNumber $ userPassword $ x121Address ) X-ORIGIN 'RFC 4519' )",
            "( *******0 NAME 'residentialPerson' SUP person STRUCTURAL MUST l MAY ( businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ internationalISDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l ) X-ORIGIN 'RFC 4519' )",
            "( *******.1.3.1 NAME 'uidObject' SUP top AUXILIARY MUST uid X-ORIGIN 'RFC 4519' )",
            "( 2.16.840.1.113719.*********.1 NAME 'ldapSubEntry' DESC 'LDAP Subentry class, version 1' SUP top STRUCTURAL MAY cn X-ORIGIN 'LDAP Subentry Internet Draft' )",
            "( 2.16.840.1.113730.3.2.40 NAME 'directoryServerFeature' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY ( oid $ cn $ multiLineDescription ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.41 NAME 'nsslapdPlugin' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ nsslapd-pluginPath $ nsslapd-pluginInitfunc $ nsslapd-pluginType $ nsslapd-pluginId $ nsslapd-pluginVersion $ nsslapd-pluginVendor $ nsslapd-pluginDescription $ nsslapd-pluginEnabled ) MAY ( nsslapd-pluginConfigArea $ nsslapd-plugin-depends-on-type ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.44 NAME 'nsIndex' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ nsSystemIndex ) MAY ( description $ nsIndexType $ nsMatchingRule $ nsIndexIDListScanLimit ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.109 NAME 'nsBackendInstance' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.110 NAME 'nsMappingTree' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.104 NAME 'nsContainer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.108 NAME 'nsDS5Replica' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( nsDS5ReplicaRoot $ nsDS5ReplicaId ) MAY ( cn $ nsds5ReplicaPreciseTombstonePurging $ nsds5ReplicaCleanRUV $ nsds5ReplicaAbortCleanRUV $ nsDS5ReplicaType $ nsDS5ReplicaBindDN $ nsState $ nsDS5ReplicaName $ nsDS5Flags $ nsDS5Task $ nsDS5ReplicaReferral $ nsDS5ReplicaAutoReferral $ nsds5ReplicaPurgeDelay $ nsds5ReplicaTombstonePurgeInterval $ nsds5ReplicaChangeCount $ nsds5ReplicaLegacyConsumer $ nsds5ReplicaProtocolTimeout $ nsds5ReplicaBackoffMin $ nsds5ReplicaBackoffMax ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.113 NAME 'nsTombstone' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY ( nstombstonecsn $ nsParentUniqueId $ nscpEntryDN ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.103 NAME 'nsDS5ReplicationAgreement' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsds5ReplicaCleanRUVNotified $ nsDS5ReplicaHost $ nsDS5ReplicaPort $ nsDS5ReplicaTransportInfo $ nsDS5ReplicaBindDN $ nsDS5ReplicaCredentials $ nsDS5ReplicaBindMethod $ nsDS5ReplicaRoot $ nsDS5ReplicatedAttributeList $ nsDS5ReplicatedAttributeListTotal $ nsDS5ReplicaUpdateSchedule $ nsds5BeginReplicaRefresh $ description $ nsds50ruv $ nsruvReplicaLastModified $ nsds5replicaTimeout $ nsds5replicaChangesSentSinceStartup $ nsds5replicaLastUpdateEnd $ nsds5replicaLastUpdateStart $ nsds5replicaLastUpdateStatus $ nsds5replicaUpdateInProgress $ nsds5replicaLastInitEnd $ nsds5ReplicaEnabled $ nsds5replicaLastInitStart $ nsds5replicaLastInitStatus $ nsds5debugreplicatimeout $ nsds5replicaBusyWaitTime $ nsds5ReplicaStripAttrs $ nsds5replicaSessionPauseTime $ nsds5ReplicaProtocolTimeout ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.39 NAME 'nsslapdConfig' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY cn X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.317 NAME 'nsSaslMapping' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ nsSaslMapRegexString $ nsSaslMapBaseDNTemplate $ nsSaslMapFilterTemplate ) MAY nsSaslMapPriority X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.43 NAME 'nsSNMP' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ nsSNMPEnabled ) MAY ( nsSNMPOrganization $ nsSNMPLocation $ nsSNMPContact $ nsSNMPDescription $ nsSNMPName $ nsSNMPMasterHost $ nsSNMPMasterPort ) X-ORIGIN 'Netscape Directory Server' )",
            "( nsEncryptionConfig-oid NAME 'nsEncryptionConfig' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsCertfile $ nsKeyfile $ nsSSL2 $ nsSSL3 $ nsTLS1 $ sslVersionMin $ sslVersionMax $ nsSSLSessionTimeout $ nsSSL3SessionTimeout $ nsSSLClientAuth $ nsSSL2Ciphers $ nsSSL3Ciphers $ nsSSLSupportedCiphers ) X-ORIGIN 'Netscape' )",
            "( nsEncryptionModule-oid NAME 'nsEncryptionModule' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsSSLToken $ nsSSLPersonalitySSL $ nsSSLActivation ) X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.2.327 NAME 'rootDNPluginConfig' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( rootdn-open-time $ rootdn-close-time $ rootdn-days-allowed $ rootdn-allow-host $ rootdn-deny-host $ rootdn-allow-ip $ rootdn-deny-ip ) X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.2.328 NAME 'nsSchemaPolicy' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY ( cn $ schemaUpdateObjectclassAccept $ schemaUpdateObjectclassReject $ schemaUpdateAttributeAccept $ schemaUpdateAttributeReject ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.31 NAME 'groupOfCertificates' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( memberCertificateDescription $ businessCategory $ description $ o $ ou $ owner $ seeAlso ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.33 NAME 'groupOfURLs' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( memberURL $ businessCategory $ description $ o $ ou $ owner $ seeAlso ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.35 NAME 'LDAPServer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( description $ l $ ou $ seeAlso $ generation $ changeLogMaximumAge $ changeLogMaximumSize ) X-ORIGIN 'Netscape Directory Server' )",
            "( *******.*********.18 NAME 'cacheObject' DESC 'object that contains the TTL (time to live) attribute type' SUP top STRUCTURAL MAY ttl X-ORIGIN 'LDAP Caching Internet Draft' )",
            "( 2.16.840.1.113730.3.2.10 NAME 'netscapeServer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( description $ serverRoot $ serverProductName $ serverVersionNumber $ installationTimeStamp $ administratorContactInfo $ userPassword $ adminUrl $ serverHostName ) X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.2.7 NAME 'nsLicenseUser' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY ( nsLicensedFor $ nsLicenseStartTime $ nsLicenseEndTime ) X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.2.1 NAME 'changeLogEntry' DESC 'LDAP changelog objectclass' SUP top STRUCTURAL MUST ( targetDn $ changeTime $ changeNumber $ changeType ) MAY ( changes $ newRdn $ deleteOldRdn $ newSuperior ) X-ORIGIN 'Changelog Internet Draft' )",
            "( 2.16.840.1.113730.3.2.6 NAME 'referral' DESC 'LDAP referrals objectclass' SUP top STRUCTURAL MAY ref X-ORIGIN 'LDAPv3 referrals Internet Draft' )",
            "( 2.16.840.1.113730.3.2.12 NAME 'passwordObject' DESC 'Netscape defined password policy objectclass' SUP top STRUCTURAL MAY ( pwdpolicysubentry $ passwordExpirationTime $ passwordExpWarned $ passwordRetryCount $ retryCountResetTime $ accountUnlockTime $ passwordHistory $ passwordAllowChangeTime $ passwordGraceUserTime ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.13 NAME 'passwordPolicy' DESC 'Netscape defined password policy objectclass' SUP top STRUCTURAL MAY ( passwordMaxAge $ passwordExp $ passwordMinLength $ passwordKeepHistory $ passwordInHistory $ passwordChange $ passwordWarning $ passwordLockout $ passwordMaxFailure $ passwordResetDuration $ passwordUnlock $ passwordLockoutDuration $ passwordCheckSyntax $ passwordMustChange $ passwordStorageScheme $ passwordMinAge $ passwordResetFailureCount $ passwordGraceLimit $ passwordMinDigits $ passwordMinAlphas $ passwordMinUppers $ passwordMinLowers $ passwordMinSpecials $ passwordMin8bit $ passwordMaxRepeats $ passwordMinCategories $ passwordMinTokenLength $ passwordTrackUpdateTime $ passwordAdminDN ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.30 NAME 'glue' DESC 'Netscape defined objectclass' SUP top STRUCTURAL X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.32 NAME 'netscapeMachineData' DESC 'Netscape defined objectclass' SUP top STRUCTURAL X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.38 NAME 'vlvSearch' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ vlvBase $ vlvScope $ vlvFilter ) MAY multiLineDescription X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.42 NAME 'vlvIndex' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ vlvSort ) MAY ( vlvEnabled $ vlvUses ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.84 NAME 'cosDefinition' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY ( costargettree $ costemplatedn $ cosspecifier $ cosAttribute $ aci $ cn $ uid ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.93 NAME 'nsRoleDefinition' DESC 'Netscape defined objectclass' SUP ldapSubEntry STRUCTURAL MAY ( description $ nsRoleScopeDN ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.94 NAME 'nsSimpleRoleDefinition' DESC 'Netscape defined objectclass' SUP nsRoleDefinition STRUCTURAL X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.95 NAME 'nsComplexRoleDefinition' DESC 'Netscape defined objectclass' SUP nsRoleDefinition STRUCTURAL X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.96 NAME 'nsManagedRoleDefinition' DESC 'Netscape defined objectclass' SUP nsSimpleRoleDefinition STRUCTURAL X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.97 NAME 'nsFilteredRoleDefinition' DESC 'Netscape defined objectclass' SUP nsComplexRoleDefinition STRUCTURAL MUST nsRoleFilter X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.98 NAME 'nsNestedRoleDefinition' DESC 'Netscape defined objectclass' SUP nsComplexRoleDefinition STRUCTURAL MUST nsRoleDN X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.99 NAME 'cosSuperDefinition' DESC 'Netscape defined objectclass' SUP ldapSubEntry STRUCTURAL MUST cosAttribute MAY description X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.100 NAME 'cosClassicDefinition' DESC 'Netscape defined objectclass' SUP cosSuperDefinition STRUCTURAL MAY ( costemplatedn $ cosspecifier ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.101 NAME 'cosPointerDefinition' DESC 'Netscape defined objectclass' SUP cosSuperDefinition STRUCTURAL MAY costemplatedn X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.102 NAME 'cosIndirectDefinition' DESC 'Netscape defined objectclass' SUP cosSuperDefinition STRUCTURAL MAY cosIndirectSpecifier X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.503 NAME 'nsDSWindowsReplicationAgreement' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsDS5ReplicaHost $ nsDS5ReplicaPort $ nsDS5ReplicaTransportInfo $ nsDS5ReplicaBindDN $ nsDS5ReplicaCredentials $ nsDS5ReplicaBindMethod $ nsDS5ReplicaRoot $ nsDS5ReplicatedAttributeList $ nsDS5ReplicaUpdateSchedule $ nsds5BeginReplicaRefresh $ description $ nsds50ruv $ nsruvReplicaLastModified $ nsds5replicaTimeout $ nsds5replicaChangesSentSinceStartup $ nsds5replicaLastUpdateEnd $ nsds5replicaLastUpdateStart $ nsds5replicaLastUpdateStatus $ nsds5replicaUpdateInProgress $ nsds5replicaLastInitEnd $ nsds5replicaLastInitStart $ nsds5replicaLastInitStatus $ nsds5debugreplicatimeout $ nsds5replicaBusyWaitTime $ nsds5replicaSessionPauseTime $ nsds7WindowsReplicaSubtree $ nsds7DirectoryReplicaSubtree $ nsds7NewWinUserSyncEnabled $ nsds7NewWinGroupSyncEnabled $ nsds7WindowsDomain $ nsds7DirsyncCookie $ winSyncInterval $ oneWaySync $ winSyncMoveAction $ nsds5ReplicaEnabled $ winSyncDirectoryFilter $ winSyncWindowsFilter $ winSyncSubtreePair ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.128 NAME 'costemplate' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY ( cn $ cosPriority ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.304 NAME 'nsView' DESC 'Netscape defined objectclass' SUP top AUXILIARY MAY ( nsViewFilter $ description ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.316 NAME 'nsAttributeEncryption' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ nsEncryptionAlgorithm ) X-ORIGIN 'Netscape Directory Server' )",
            "( ******** NAME 'pkiUser' DESC 'X.509 PKI User' SUP top AUXILIARY MAY userCertificate X-ORIGIN 'RFC 4523' )",
            "( ******** NAME 'pkiCA' DESC 'X.509 PKI Certificate Authority' SUP top AUXILIARY MAY ( cACertificate $ certificateRevocationList $ authorityRevocationList $ crossCertificatePair ) X-ORIGIN 'RFC 4523' )",
            "( *******9 NAME 'cRLDistributionPoint' DESC 'X.509 CRL distribution point' SUP top STRUCTURAL MUST cn MAY ( certificateRevocationList $ authorityRevocationList $ deltaRevocationList ) X-ORIGIN 'RFC 4523' )",
            "( *******3 NAME 'deltaCRL' DESC 'X.509 delta CRL' SUP top AUXILIARY MAY deltaRevocationList X-ORIGIN 'RFC 4523' )",
            "( *******5 NAME 'strongAuthenticationUser' DESC 'X.521 strong authentication user' SUP top AUXILIARY MUST userCertificate X-ORIGIN 'RFC 4523' )",
            "( *******8 NAME 'userSecurityInformation' DESC 'X.521 user security information' SUP top AUXILIARY MAY supportedAlgorithms X-ORIGIN 'RFC 4523' )",
            "( *******6 NAME 'certificationAuthority' DESC 'X.509 certificate authority' SUP top AUXILIARY MUST ( authorityRevocationList $ certificateRevocationList $ cACertificate ) MAY crossCertificatePair X-ORIGIN 'RFC 4523' )",
            "( *******6.2 NAME 'certificationAuthority-V2' DESC 'X.509 certificate authority, version 2' SUP certificationAuthority AUXILIARY MAY deltaRevocationList X-ORIGIN 'RFC 4523' )",
            "( 0.9.2342.********.100.4.5 NAME 'account' SUP top STRUCTURAL MUST uid MAY ( description $ seeAlso $ l $ o $ ou $ host ) X-ORIGIN 'RFC 4524' )",
            "( 0.9.2342.********.100.4.6 NAME 'document' SUP top STRUCTURAL MUST documentIdentifier MAY ( cn $ description $ seeAlso $ l $ o $ ou $ documentTitle $ documentVersion $ documentAuthor $ documentLocation $ documentPublisher ) X-ORIGIN 'RFC 4524' )",
            "( 0.9.2342.********.100.4.9 NAME 'documentSeries' SUP top STRUCTURAL MUST cn MAY ( description $ l $ o $ ou $ seeAlso $ telephoneNumber ) X-ORIGIN 'RFC 4524' )",
            "( 0.9.2342.********.100.4.13 NAME 'domain' SUP top STRUCTURAL MUST dc MAY ( userPassword $ searchGuide $ seeAlso $ businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationalISDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l $ description $ o $ associatedName ) X-ORIGIN 'RFC 4524' )",
            "( 0.9.2342.********.100.4.17 NAME 'domainRelatedObject' SUP top AUXILIARY MUST associatedDomain X-ORIGIN 'RFC 4524' )",
            "( 0.9.2342.********.100.4.18 NAME 'friendlyCountry' SUP country STRUCTURAL MUST co X-ORIGIN 'RFC 4524' )",
            "( 0.9.2342.********.100.4.14 NAME 'rFC822localPart' SUP domain STRUCTURAL MAY ( cn $ sn ) X-ORIGIN 'RFC 4524' )",
            "( 0.9.2342.********.100.4.7 NAME 'room' SUP top STRUCTURAL MUST cn MAY ( roomNumber $ description $ seeAlso $ telephoneNumber ) X-ORIGIN 'RFC 4524' )",
            "( 0.9.2342.********.100.4.19 NAME 'simpleSecurityObject' SUP top AUXILIARY MUST userPassword X-ORIGIN 'RFC 4524' )",
            "( 2.16.840.1.113730.3.2.2 NAME 'inetOrgPerson' SUP organizationalPerson STRUCTURAL MAY ( audio $ businessCategory $ carLicense $ departmentNumber $ displayName $ employeeNumber $ employeeType $ givenName $ homePhone $ homePostalAddress $ initials $ jpegPhoto $ labeledURI $ mail $ manager $ mobile $ o $ pager $ photo $ roomNumber $ secretary $ uid $ userCertificate $ x500UniqueIdentifier $ preferredLanguage $ userSMIMECertificate $ userPKCS12 ) X-ORIGIN 'RFC 2798' )",
            "( 2.16.840.1.113730.3.2.322 NAME 'autoMemberDefinition' DESC 'Auto Membership Config Definition Entry' SUP top STRUCTURAL MUST ( cn $ autoMemberScope $ autoMemberFilter $ autoMemberGroupingAttr ) MAY ( autoMemberDefaultGroup $ autoMemberDisabled ) X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.2.323 NAME 'autoMemberRegexRule' DESC 'Auto Membership Regex Rule Entry' SUP top STRUCTURAL MUST ( cn $ autoMemberTargetGroup ) MAY ( autoMemberExclusiveRegex $ autoMemberInclusiveRegex $ description ) X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.2.324 NAME 'dnaPluginConfig' DESC 'DNA plugin configuration' SUP top AUXILIARY MAY ( dnaType $ dnaPrefix $ dnaNextValue $ dnaMaxValue $ dnaInterval $ dnaMagicRegen $ dnaFilter $ dnaScope $ dnaSharedCfgDN $ dnaThreshold $ dnaNextRange $ dnaRangeRequestTimeout $ dnaRemoteBindDN $ dnaRemoteBindCred $ cn ) X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.2.325 NAME 'dnaSharedConfig' DESC 'DNA Shared Configuration' SUP top AUXILIARY MAY ( dnaHostname $ dnaPortNum $ dnaSecurePortNum $ dnaRemoteBindMethod $ dnaRemoteConnProtocol $ dnaRemainingValues ) X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.2.319 NAME 'mepManagedEntry' DESC 'Managed Entries Managed Entry' SUP top AUXILIARY MAY mepManagedBy X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.2.320 NAME 'mepOriginEntry' DESC 'Managed Entries Origin Entry' SUP top AUXILIARY MAY mepManagedEntry X-ORIGIN '389 Directory Server' )",
            "( 2.16.840.1.113730.3.2.321 NAME 'mepTemplateEntry' DESC 'Managed Entries Template Entry' SUP top AUXILIARY MAY ( cn $ mepStaticAttr $ mepMappedAttr $ mepRDNAttr ) X-ORIGIN '389 Directory Server' )",
            "( *******.******* NAME 'posixAccount' DESC 'Standard LDAP objectclass' SUP top AUXILIARY MUST ( cn $ uid $ uidNumber $ gidNumber $ homeDirectory ) MAY ( userPassword $ loginShell $ gecos $ description ) X-ORIGIN 'RFC 2307' )",
            "( *******.******* NAME 'shadowAccount' DESC 'Standard LDAP objectclass' SUP top AUXILIARY MUST uid MAY ( userPassword $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ description ) X-ORIGIN 'RFC 2307' )",
            "( *******.******* NAME 'posixGroup' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MUST ( cn $ gidNumber ) MAY ( userPassword $ memberUid $ description ) X-ORIGIN 'RFC 2307' )",
            "( *******.******* NAME 'ipService' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MUST ( cn $ ipServicePort $ ipServiceProtocol ) MAY description X-ORIGIN 'RFC 2307' )",
            "( *******.******* NAME 'ipProtocol' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MUST ( cn $ ipProtocolNumber ) MAY description X-ORIGIN 'RFC 2307' )",
            "( *******.******* NAME 'oncRpc' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MUST ( cn $ oncRpcNumber ) MAY description X-ORIGIN 'RFC 2307' )",
            "( *******.******* NAME 'ipHost' DESC 'Standard LDAP objectclass' SUP top AUXILIARY MUST ( ipHostNumber $ cn ) MAY ( manager $ description $ l $ o $ ou $ owner $ seeAlso $ serialNumber ) X-ORIGIN 'RFC 2307' )",
            "( *******.******* NAME 'ipNetwork' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MUST ( ipNetworkNumber $ cn ) MAY ( ipNetmaskNumber $ manager $ l $ description ) X-ORIGIN 'RFC 2307' )",
            "( *******.******* NAME 'nisNetgroup' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MUST cn MAY ( nisNetgroupTriple $ memberNisNetgroup $ description ) X-ORIGIN 'RFC 2307' )",
            "( *******.*******0 NAME 'nisObject' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MUST ( cn $ nisMapEntry $ nisMapName ) MAY description X-ORIGIN 'RFC 2307' )",
            "( *******.******** NAME 'ieee802Device' DESC 'Standard LDAP objectclass' SUP top AUXILIARY MUST cn MAY ( macAddress $ description $ l $ o $ ou $ owner $ seeAlso $ serialNumber ) X-ORIGIN 'RFC 2307' )",
            "( *******.******** NAME 'bootableDevice' DESC 'Standard LDAP objectclass' SUP top AUXILIARY MUST cn MAY ( bootFile $ bootParameter $ description $ l $ o $ ou $ owner $ seeAlso $ serialNumber ) X-ORIGIN 'RFC 2307' )",
            "( *******.******** NAME 'nisMap' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MUST nisMapName MAY description X-ORIGIN 'RFC 2307' )",
            "( 2.16.840.1.113730.3.2.129 NAME 'inetDomain' DESC 'Auxiliary class for virtual domain nodes' SUP top AUXILIARY MAY ( inetDomainBaseDN $ inetDomainStatus ) X-ORIGIN 'Netscape subscriber interoperability' )",
            "( 2.16.840.1.113730.3.2.130 NAME 'inetUser' DESC 'Auxiliary class which must be present in an entry for delivery of subscriber services' SUP top AUXILIARY MAY ( uid $ inetUserStatus $ inetUserHttpURL $ userPassword $ memberOf ) X-ORIGIN 'Netscape subscriber interoperability' )",
            "( *******.4.1.1466.101.120.141 NAME 'NetscapeLinkedOrganization' AUXILIARY MAY parentOrganization X-ORIGIN 'Netscape' )",
            "( *******.4.1.1466.101.120.142 NAME 'NetscapePreferences' AUXILIARY MAY ( preferredLanguage $ preferredLocale $ preferredTimeZone ) X-ORIGIN 'Netscape' )",
            "( 2.16.840.1.113730.3.2.134 NAME 'inetSubscriber' SUP top AUXILIARY MAY ( inetSubscriberAccountId $ inetSubscriberChallenge $ inetSubscriberResponse ) X-ORIGIN 'Netscape subscriber interoperability' )",
            "( 2.16.840.1.113730.3.2.112 NAME 'inetAdmin' DESC 'Marker for an administrative group or user' SUP top AUXILIARY MAY ( aci $ memberOf $ adminRole ) X-ORIGIN 'Netscape Delegated Administrator' )",
            "( *******.********.******** NAME 'javaContainer' DESC 'Container for a Java object' SUP top STRUCTURAL MUST cn X-ORIGIN 'RFC 2713' )",
            "( *******.********.******** NAME 'javaObject' DESC 'Java object representation' SUP top ABSTRACT MUST javaClassName MAY ( javaClassNames $ javaCodebase $ javaDoc $ description ) X-ORIGIN 'RFC 2713' )",
            "( *******.********.******** NAME 'javaSerializedObject' DESC 'Java serialized object' SUP javaObject AUXILIARY MUST javaSerializedData X-ORIGIN 'RFC 2713' )",
            "( *******.********.******** NAME 'javaNamingReference' DESC 'JNDI reference' SUP javaObject AUXILIARY MAY ( javaReferenceAddress $ javaFactory ) X-ORIGIN 'RFC 2713' )",
            "( *******.********.******** NAME 'javaMarshalledObject' DESC 'Java marshalled object' SUP javaObject AUXILIARY MUST javaSerializedData X-ORIGIN 'RFC 2713' )",
            "( 0.9.2342.********.100.4.3 NAME 'pilotObject' DESC 'Standard LDAP objectclass' SUP top STRUCTURAL MAY ( audio $ ditRedirect $ info $ jpegPhoto $ lastModifiedBy $ lastModifiedTime $ manager $ photo $ uniqueIdentifier ) X-ORIGIN 'RFC 1274' )",
            "( nsAdminDomain-oid NAME 'nsAdminDomain' DESC 'Netscape defined objectclass' SUP organizationalUnit STRUCTURAL MAY nsAdminDomainName X-ORIGIN 'Netscape' )",
            "( nsHost-oid NAME 'nsHost' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( serverHostName $ description $ l $ nsHostLocation $ nsHardwarePlatform $ nsOsVersion ) X-ORIGIN 'Netscape' )",
            "( nsAdminGroup-oid NAME 'nsAdminGroup' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsAdminGroupName $ description $ nsConfigRoot $ nsAdminSIEDN ) X-ORIGIN 'Netscape' )",
            "( nsApplication-oid NAME 'nsApplication' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsVendor $ description $ nsProductName $ nsNickName $ nsProductVersion $ nsBuildNumber $ nsRevisionNumber $ nsSerialNumber $ nsInstalledLocation $ installationTimeStamp $ nsExpirationDate $ nsBuildSecurity $ nsLdapSchemaVersion $ nsServerMigrationClassname $ nsServerCreationClassname ) X-ORIGIN 'Netscape' )",
            "( nsResourceRef-oid NAME 'nsResourceRef' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY seeAlso X-ORIGIN 'Netscape' )",
            "( nsTask-oid NAME 'nsTask' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsTaskLabel $ nsHelpRef $ nsExecRef $ nsLogSuppress ) X-ORIGIN 'Netscape' )",
            "( nsTaskGroup-oid NAME 'nsTaskGroup' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY nsTaskLabel X-ORIGIN 'Netscape' )",
            "( nsAdminObject-oid NAME 'nsAdminObject' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsJarfilename $ nsClassname ) X-ORIGIN 'Netscape' )",
            "( nsConfig-oid NAME 'nsConfig' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( description $ nsServerPort $ nsServerAddress $ nsSuiteSpotUser $ nsErrorLog $ nsPidLog $ nsAccessLog $ nsDefaultAcceptLanguage $ nsServerSecurity ) X-ORIGIN 'Netscape' )",
            "( nsDirectoryInfo-oid NAME 'nsDirectoryInfo' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsBindDN $ nsBindPassword $ nsDirectoryURL $ nsDirectoryFailoverList $ nsDirectoryInfoRef ) X-ORIGIN 'Netscape' )",
            "( nsAdminServer-oid NAME 'nsAdminServer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ nsServerID ) MAY description X-ORIGIN 'Netscape Administration Services' )",
            "( nsAdminConfig-oid NAME 'nsAdminConfig' DESC 'Netscape defined objectclass' SUP nsConfig STRUCTURAL MAY ( nsAdminCgiWaitPid $ nsAdminUsers $ nsAdminAccessHosts $ nsAdminAccessAddresses $ nsAdminOneACLDir $ nsAdminEnableDSGW $ nsAdminEnableEnduser $ nsAdminCacheLifetime ) X-ORIGIN 'Netscape Administration Services' )",
            "( nsAdminResourceEditorExtension-oid NAME 'nsAdminResourceEditorExtension' DESC 'Netscape defined objectclass' SUP nsAdminObject STRUCTURAL MAY ( nsAdminAccountInfo $ nsDeleteclassname ) X-ORIGIN 'Netscape Administration Services' )",
            "( nsAdminGlobalParameters-oid NAME 'nsAdminGlobalParameters' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsAdminEndUserHTMLIndex $ nsNickName ) X-ORIGIN 'Netscape Administration Services' )",
            "( nsGlobalParameters-oid NAME 'nsGlobalParameters' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsUniqueAttribute $ nsUserIDFormat $ nsUserRDNComponent $ nsGroupRDNComponent $ nsWellKnownJarfiles $ nsNYR ) X-ORIGIN 'Netscape Administration Services' )",
            "( nsDefaultObjectClasses-oid NAME 'nsDefaultObjectClasses' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY nsDefaultObjectClass X-ORIGIN 'Netscape Administration Services' )",
            "( nsAdminConsoleUser-oid NAME 'nsAdminConsoleUser' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY nsPreference X-ORIGIN 'Netscape Administration Services' )",
            "( nsCustomView-oid NAME 'nsCustomView' DESC 'Netscape defined objectclass' SUP nsAdminObject STRUCTURAL MAY nsDisplayName X-ORIGIN 'Netscape Administration Services' )",
            "( nsTopologyCustomView-oid NAME 'nsTopologyCustomView' DESC 'Netscape defined objectclass' SUP nsCustomView STRUCTURAL MAY nsViewConfiguration X-ORIGIN 'Netscape Administration Services' )",
            "( nsTopologyPlugin-oid NAME 'nsTopologyPlugin' DESC 'Netscape defined objectclass' SUP nsAdminObject STRUCTURAL X-ORIGIN 'Netscape Administration Services' )",
            "( 2.16.840.1.113730.3.2.18 NAME 'netscapeCertificateServer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL X-ORIGIN 'Netscape Certificate Management System' )",
            "( nsCertificateServer-oid NAME 'nsCertificateServer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST nsServerID MAY ( serverHostName $ nsServerPort $ nsCertConfig ) X-ORIGIN 'Netscape Certificate Management System' )",
            "( 2.16.840.1.113730.3.2.23 NAME 'netscapeDirectoryServer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL X-ORIGIN 'Netscape Directory Server' )",
            "( nsDirectoryServer-oid NAME 'nsDirectoryServer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST nsServerID MAY ( serverHostName $ nsServerPort $ nsSecureServerPort $ nsBindPassword $ nsBindDN $ nsBaseDN ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.8 NAME 'ntUser' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ntUserDomainId MAY ( description $ l $ ou $ seeAlso $ ntUserPriv $ ntUserHomeDir $ ntUserComment $ ntUserFlags $ ntUserScriptPath $ ntUserAuthFlags $ ntUserUsrComment $ ntUserParms $ ntUserWorkstations $ ntUserLastLogon $ ntUserLastLogoff $ ntUserAcctExpires $ ntUserMaxStorage $ ntUserUnitsPerWeek $ ntUserLogonHours $ ntUserBadPwCount $ ntUserNumLogons $ ntUserLogonServer $ ntUserCountryCode $ ntUserCodePage $ ntUserUniqueId $ ntUserPrimaryGroupId $ ntUserProfile $ ntUserHomeDirDrive $ ntUserPasswordExpired $ ntUserCreateNewAccount $ ntUserDeleteAccount $ ntUniqueId ) X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.2.9 NAME 'ntGroup' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ntUserDomainId MAY ( description $ l $ ou $ seeAlso $ ntGroupId $ ntGroupAttributes $ ntGroupCreateNewGroup $ ntGroupDeleteGroup $ ntGroupType $ ntUniqueId $ mail ) X-ORIGIN 'Netscape NT Synchronization' )",
            "( 2.16.840.1.113730.3.2.82 NAME 'nsChangelog4Config' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY cn X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.114 NAME 'nsConsumer4Config' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MAY cn X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.36 NAME 'LDAPReplica' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( description $ l $ ou $ seeAlso $ replicaRoot $ replicaHost $ replicaPort $ replicaBindDn $ replicaCredentials $ replicaBindMethod $ replicaUseSSL $ replicaUpdateSchedule $ replicaUpdateReplayed $ replicaUpdateFailedAt $ replicaBeginOrc $ replicaNickName $ replicaEntryFilter $ replicatedattributelist $ replicaCFUpdated $ replicaAbandonedChanges $ replicaLastRelevantChange ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.11 NAME 'cirReplicaSource' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( cirReplicaRoot $ cirHost $ cirPort $ cirBindDn $ cirUsePersistentSearch $ cirUseSsl $ cirBindCredentials $ cirLastUpdateApplied $ cirUpdateSchedule $ cirSyncInterval $ cirUpdateFailedat $ cirBeginORC $ replicaNickName $ replicaEntryFilter $ replicatedattributelist ) X-ORIGIN 'Netscape Directory Server' )",
            "( 2.16.840.1.113730.3.2.3 NAME 'mailRecipient' DESC 'Netscape Messaging Server 4.x defined objectclass' SUP top AUXILIARY MAY ( cn $ mail $ mailAlternateAddress $ mailHost $ mailRoutingAddress $ mailAccessDomain $ mailAutoReplyMode $ mailAutoReplyText $ mailDeliveryOption $ mailForwardingAddress $ mailMessageStore $ mailProgramDeliveryInfo $ mailQuota $ multiLineDescription $ uid $ userPassword ) X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.113730.3.2.37 NAME 'nsMessagingServerUser' DESC 'Netscape Messaging Server 4.x defined objectclass' SUP top AUXILIARY MAY ( cn $ mailAccessDomain $ mailAutoReplyMode $ mailAutoReplyText $ mailDeliveryOption $ mailForwardingAddress $ mailMessageStore $ mailProgramDeliveryInfo $ mailQuota $ nsmsgDisallowAccess $ nsmsgNumMsgQuota $ nswmExtendedUserPrefs $ vacationstartdate $ vacationenddate ) X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.2.4 NAME 'mailGroup' DESC 'Netscape Messaging Server 4.x defined objectclass' SUP top AUXILIARY MAY ( cn $ mail $ mailAlternateAddress $ mailHost $ mailRoutingAddress $ mgrpAddHeader $ mgrpAllowedBroadcaster $ mgrpAllowedDomain $ mgrpApprovePassword $ mgrpBroadcasterPolicy $ mgrpDeliverTo $ mgrpErrorsTo $ mgrpModerator $ mgrpMsgMaxSize $ mgrpMsgRejectAction $ mgrpMsgRejectText $ mgrpNoDuplicateChecks $ mgrpRemoveHeader $ mgrpRFC822MailMember $ owner ) X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.2.5 NAME 'groupOfMailEnhancedUniqueNames' DESC 'Netscape Messaging Server 4.x defined objectclass' SUP top AUXILIARY MUST cn MAY ( businessCategory $ description $ mailEnhancedUniqueMember $ o $ ou $ owner $ seeAlso ) X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.2.24 NAME 'netscapeMailServer' DESC 'Netscape Messaging Server 4.x defined objectclass' SUP top AUXILIARY X-ORIGIN 'Netscape Messaging Server 4.x' )",
            "( 2.16.840.1.113730.3.2.45 NAME 'nsValueItem' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST cn MAY ( nsValueCIS $ nsValueCES $ nsValueTel $ nsValueInt $ nsValueBin $ nsValueDN $ nsValueType $ nsValueSyntax $ nsValueDescription $ nsValueHelpURL $ nsValueFlags $ nsValueDefault ) X-ORIGIN 'Netscape servers - value item' )",
            "( 2.16.840.1.113730.3.2.29 NAME 'netscapeWebServer' DESC 'Netscape defined objectclass' SUP top STRUCTURAL MUST ( cn $ nsServerID ) MAY ( description $ nsServerPort ) X-ORIGIN 'Netscape Web Server' )",
            "( 2.16.840.1.113730.3.2.154 NAME 'netscapeReversiblePasswordObject' DESC 'object that contains an netscapeReversiblePassword' AUXILIARY MAY netscapeReversiblePassword X-ORIGIN 'Netscape Web Server' )",
            "( *******.********.******* NAME 'accountPolicy' DESC 'Account policy entry' SUP top AUXILIARY MAY accountInactivityLimit X-ORIGIN 'Account Policy Plugin' )",
            "( *******.******** NAME 'automount' DESC 'An entry in an automounter map' SUP top STRUCTURAL MUST ( cn $ automountInformation ) MAY description X-ORIGIN 'draft-howard-rfc2307bis' )",
            "( *******.******** NAME 'automountMap' DESC 'An group of related automount objects' SUP top STRUCTURAL MUST ou X-ORIGIN 'draft-howard-rfc2307bis' )",
            "( *******.4.1.5923.1.1.2 NAME 'eduPerson' AUXILIARY MAY ( eduPersonAffiliation $ eduPersonNickName $ eduPersonOrgDN $ eduPersonOrgUnitDN $ eduPersonPrimaryAffiliation $ eduPersonPrincipalName $ eduPersonEntitlement $ eduPersonPrimaryOrgUnitDN $ eduPersonScopedAffiliation ) X-ORIGIN 'http://middleware.internet2.edu/eduperson/' )",
            "( *******.4.1.13769.9.1 NAME 'mozillaAbPersonAlpha' SUP top AUXILIARY MUST cn MAY ( c $ description $ displayName $ facsimileTelephoneNumber $ givenName $ homePhone $ l $ mail $ mobile $ mozillaCustom1 $ mozillaCustom2 $ mozillaCustom3 $ mozillaCustom4 $ mozillaHomeCountryName $ mozillaHomeLocalityName $ mozillaHomePostalCode $ mozillaHomeState $ mozillaHomeStreet $ mozillaHomeStreet2 $ mozillaHomeUrl $ mozillaNickname $ mozillaSecondEmail $ mozillaUseHtmlMail $ mozillaWorkStreet2 $ mozillaWorkUrl $ nsAIMid $ o $ ou $ pager $ postalCode $ postOfficeBox $ sn $ st $ street $ telephoneNumber $ title ) X-ORIGIN 'Mozilla Address Book' )",
            "( *******.4.1.5322.17.1.1 NAME 'authorizedServiceObject' DESC 'Auxiliary object class for adding authorizedService attribute' SUP top AUXILIARY MAY authorizedService X-ORIGIN 'NSS LDAP schema' )",
            "( *******.4.1.5322.17.1.2 NAME 'hostObject' DESC 'Auxiliary object class for adding host attribute' SUP top AUXILIARY MAY host X-ORIGIN 'NSS LDAP schema' )",
            "( 2.16.840.1.113730.3.2.318 NAME 'pamConfig' DESC 'PAM plugin configuration' SUP top AUXILIARY MAY ( cn $ pamMissingSuffix $ pamExcludeSuffix $ pamIncludeSuffix $ pamIDAttr $ pamIDMapMethod $ pamFallback $ pamSecure $ pamService $ pamFilter ) X-ORIGIN 'Red Hat Directory Server' )",
            "( 2.16.840.1.113730.3.2.326 NAME 'dynamicGroup' DESC 'Group containing internal dynamically-generated members' SUP posixGroup AUXILIARY MAY dsOnlyMemberUid X-ORIGIN 'Red Hat Directory Server' )",
            "( *******.4.1.6981.11.2.3 NAME 'PureFTPdUser' DESC 'PureFTPd user with optional quota, throttling and ratio' STRUCTURAL MAY ( FTPStatus $ FTPQuotaFiles $ FTPQuotaMBytes $ FTPUploadRatio $ FTPDownloadRatio $ FTPUploadBandwidth $ FTPDownloadBandwidth $ FTPuid $ FTPgid ) X-ORIGIN 'Pure-FTPd' )",
            "( 1.2.840.113556.1.5.87 NAME 'calEntry' DESC 'RFC2739: Calendar Entry' SUP top AUXILIARY MAY ( calCalURI $ calFBURL $ calOtherCalURIs $ calOtherFBURLs $ calCAPURI $ calOtherCAPURIs ) X-ORIGIN 'rfc2739' )",
            "( ********.2.6.258 NAME 'printerAbstract' DESC 'Printer related information.' SUP top ABSTRACT MAY ( printer-name $ printer-natural-language-configured $ printer-location $ printer-info $ printer-more-info $ printer-make-and-model $ printer-multiple-document-jobs-supported $ printer-charset-configured $ printer-charset-supported $ printer-generated-natural-language-supported $ printer-document-format-supported $ printer-color-supported $ printer-compression-supported $ printer-pages-per-minute $ printer-pages-per-minute-color $ printer-finishings-supported $ printer-number-up-supported $ printer-sides-supported $ printer-media-supported $ printer-media-local-supported $ printer-resolution-supported $ printer-print-quality-supported $ printer-job-priority-supported $ printer-copies-supported $ printer-job-k-octets-supported $ printer-current-operator $ printer-service-person $ printer-delivery-orientation-supported $ printer-stacking-order-supported $ printer-output-features-supported ) X-ORIGIN 'rfc3712' )",
            "( ********.2.6.255 NAME 'printerService' DESC 'Printer information.' SUP printerAbstract STRUCTURAL MAY ( printer-uri $ printer-xri-supported ) X-ORIGIN 'rfc3712' )",
            "( ********.2.6.257 NAME 'printerServiceAuxClass' DESC 'Printer information.' SUP printerAbstract AUXILIARY MAY ( printer-uri $ printer-xri-supported ) X-ORIGIN 'rfc3712' )",
            "( ********.2.6.256 NAME 'printerIPP' DESC 'Internet Printing Protocol (IPP) information.' SUP top AUXILIARY MAY ( printer-ipp-versions-supported $ printer-multiple-document-jobs-supported ) X-ORIGIN 'rfc3712' )",
            "( ********.2.6.253 NAME 'printerLPR' DESC 'LPR information.' SUP top AUXILIARY MUST printer-name MAY printer-aliases X-ORIGIN 'rfc3712' )",
            "( *******.4.1.2312.******* NAME 'sabayonProfile' DESC 'sabayon profile' SUP top STRUCTURAL MUST cn MAY ( sabayonProfileURL $ description ) X-ORIGIN 'Sabayon' )",
            "( *******.4.1.2312.******* NAME 'sabayonProfileNameObject' DESC 'contains sabayon profile name' SUP top AUXILIARY MUST sabayonProfileName X-ORIGIN 'Sabayon' )",
            "( *******.4.1.2312.******* NAME 'sabayonProfileURLObject' DESC 'contains sabayon profile' SUP top AUXILIARY MUST cn MAY sabayonProfileURL X-ORIGIN 'Sabayon' )",
            "( *******.4.1.15953.9.2.1 NAME 'sudoRole' DESC 'Sudoer Entries' SUP top STRUCTURAL MUST cn MAY ( sudoUser $ sudoHost $ sudoCommand $ sudoRunAs $ sudoRunAsUser $ sudoRunAsGroup $ sudoOption $ sudoNotBefore $ sudoNotAfter $ sudoOrder $ description ) X-ORIGIN 'SUDO' )",
            "( *******.******* NAME 'trustAccount' DESC 'Sets trust accounts information' SUP top AUXILIARY MUST trustModel MAY accessTo X-ORIGIN 'nss_ldap/pam_ldap' )"
        ]
    },
    "schema_entry": "cn=schema",
    "type": "SchemaInfo"
}
"""

ds389_1_3_3_dsa_info = """
{
    "raw": {
        "aci": [
            "(targetattr != \\"aci\\")(version 3.0; aci \\"rootdse anon read access\\"; allow(read,search,compare) userdn=\\"ldap:///anyone\\";)"
        ],
        "dataversion": [
            "***************"
        ],
        "defaultnamingcontext": [
            "dc=labldap06,dc=a3,dc=internal,dc=cloudapp,dc=net"
        ],
        "namingContexts": [
            "dc=labldap06,dc=a3,dc=internal,dc=cloudapp,dc=net"
        ],
        "netscapemdsuffix": [
            "cn=ldap://dc=DS3891,dc=labldap06,dc=a3,dc=internal,dc=cloudapp,dc=net:389"
        ],
        "objectClass": [
            "top"
        ],
        "subschemaSubentry": [
            "cn=schema"
        ],
        "supportedControl": [
            "2.16.840.1.113730.3.4.2",
            "2.16.840.1.113730.3.4.3",
            "2.16.840.1.113730.3.4.4",
            "2.16.840.1.113730.3.4.5",
            "1.2.840.113556.1.4.473",
            "2.16.840.1.113730.3.4.9",
            "2.16.840.1.113730.3.4.16",
            "2.16.840.1.113730.3.4.15",
            "2.16.840.1.113730.3.4.17",
            "2.16.840.1.113730.3.4.19",
            "*******.1.13.1",
            "*******.1.13.2",
            "*******.********.27.8.5.1",
            "*******.********.27.9.5.2",
            "1.2.840.113556.1.4.319",
            "*******.********.27.9.5.8",
            "*******.4.1.4203.666.5.16",
            "2.16.840.1.113730.3.4.14",
            "2.16.840.1.113730.3.4.20",
            "*******.4.1.1466.29539.12",
            "2.16.840.1.113730.3.4.12",
            "2.16.840.1.113730.3.4.18",
            "2.16.840.1.113730.3.4.13"
        ],
        "supportedExtension": [
            "2.16.840.1.113730.3.5.7",
            "2.16.840.1.113730.3.5.8",
            "2.16.840.1.113730.3.5.3",
            "2.16.840.1.113730.3.5.12",
            "2.16.840.1.113730.3.5.5",
            "2.16.840.1.113730.3.5.6",
            "2.16.840.1.113730.3.5.9",
            "2.16.840.1.113730.3.5.4",
            "2.16.840.1.113730.3.6.5",
            "2.16.840.1.113730.3.6.6",
            "2.16.840.1.113730.3.6.7",
            "2.16.840.1.113730.3.6.8",
            "*******.4.1.4203.1.11.3",
            "*******.4.1.4203.1.11.1"
        ],
        "supportedLdapVersion": [
            "2",
            "3"
        ],
        "supportedSASLMechanisms": [
            "EXTERNAL",
            "PLAIN",
            "DIGEST-MD5",
            "ANONYMOUS",
            "GSSAPI",
            "LOGIN"
        ],
        "vendorName": [
            "389 Project"
        ],
        "vendorVersion": [
            "389-Directory/1.3.3.0 B2014.289.2022"
        ]
    },
    "type": "DsaInfo"
}
"""
