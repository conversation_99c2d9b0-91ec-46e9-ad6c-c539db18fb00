"""
"""

# Created on 2019.08.31
#
# Author: <PERSON>
#
# Copyright 2014 - 2020 <PERSON>
#
# This file is part of ldap3.
#
# ldap3 is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published
# by the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# ldap3 is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with ldap3 in the COPYING and COPYING.LESSER files.
# If not, see <http://www.gnu.org/licenses/>.

edir_9_1_4_schema = """
{
    "raw": {
        "attributeTypes": [
            "( 2.5.4.35 NAME 'userPassword' DESC 'Internal NDS policy forces this to be single-valued' SYNTAX *******.4.1.1466.************{128} USAGE directoryOperation )",
            "( 2.5.18.1 NAME 'createTimestamp' DESC 'Operational Attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.18.2 NAME 'modifyTimestamp' DESC 'Operational Attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.18.10 NAME 'subschemaSubentry' DESC 'Operational Attribute' SYNTAX *******.4.1.1466.************ USAGE directoryOperation )",
            "( 2.5.21.9 NAME 'structuralObjectClass' DESC 'Operational Attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113719.********9 NAME 'subordinateCount' DESC 'Operational Attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113719.********8 NAME 'entryFlags' DESC 'Operational Attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113719.********1 NAME 'federationBoundary' DESC 'Operational Attribute' SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.21.5 NAME 'attributeTypes' DESC 'Operational Attribute' SYNTAX *******.4.1.146***********.3 USAGE directoryOperation )",
            "( 2.5.21.6 NAME 'objectClasses' DESC 'Operational Attribute' SYNTAX *******.4.1.146***********.37 USAGE directoryOperation )",
            "( *******.1.20 NAME 'entryDN' DESC 'Operational Attribute' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113719.*******.2 NAME 'ACL' SYNTAX 2.16.840.1.113719.*******.17 X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' )",
            "( 2.5.4.1 NAME 'aliasedObjectName' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Aliased Object Name' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' )",
            "( 2.16.840.1.113719.*******.6 NAME 'backLink' SYNTAX 2.16.840.1.113719.*******.23 NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Back Link' X-NDS_SERVER_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.8 NAME 'binderyProperty' SYNTAX *******.4.1.1466.************{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Bindery Property' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.7 NAME 'binderyObjectRestriction' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Bindery Object Restriction' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.9 NAME 'binderyType' SYNTAX *******.4.1.146***********.36{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Bindery Type' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.11 NAME 'cAPrivateKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'CA Private Key' X-NDS_NONREMOVABLE '1' X-NDS_HIDDEN '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.12 NAME 'cAPublicKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'CA Public Key' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.10 NAME 'Cartridge' SYNTAX *******.4.1.1466.************{64512} X-NDS_NONREMOVABLE '1' )",
            "( 2.5.4.3 NAME ( 'cn' 'commonName' ) SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'CN' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.78 NAME 'printerConfiguration' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'Printer Configuration' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.15 NAME 'Convergence' SYNTAX *******.4.1.1466.************{1} SINGLE-VALUE X-NDS_UPPER_BOUND '1' X-NDS_NONREMOVABLE '1' )",
            "( ******* NAME ( 'c' 'countryName' ) SYNTAX *******.4.1.1466.************{2} SINGLE-VALUE X-NDS_NAME 'C' X-NDS_LOWER_BOUND '2' X-NDS_UPPER_BOUND '2' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.18 NAME 'defaultQueue' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Default Queue' X-NDS_SERVER_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME ( 'description' 'multiLineDescription' ) SYNTAX *******.4.1.1466.************{1024} X-NDS_NAME 'Description' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '1024' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.64 NAME 'partitionCreationTime' SYNTAX 2.16.840.1.113719.*******.19 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Partition Creation Time' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( ******** NAME 'facsimileTelephoneNumber' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'Facsimile Telephone Number' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.117 NAME 'highConvergenceSyncInterval' SYNTAX 2.16.840.1.113719.*******.27 SINGLE-VALUE X-NDS_NAME 'High Convergence Sync Interval' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.25 NAME 'groupMembership' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Group Membership' X-NDS_NAME_VALUE_ACCESS '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.26 NAME 'ndsHomeDirectory' SYNTAX 2.16.840.1.113719.*******.15{255} SINGLE-VALUE X-NDS_NAME 'Home Directory' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '255' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.27 NAME 'hostDevice' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Host Device' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.28 NAME 'hostResourceName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'Host Resource Name' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.29 NAME 'hostServer' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Host Server' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.30 NAME 'inheritedACL' SYNTAX 2.16.840.1.113719.*******.17 NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Inherited ACL' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( ******* NAME ( 'l' 'localityname' ) SYNTAX *******.4.1.1466.************{128} X-NDS_NAME 'L' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '128' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.39 NAME 'loginAllowedTimeMap' SYNTAX *******.4.1.1466.************{42} SINGLE-VALUE X-NDS_NAME 'Login Allowed Time Map' X-NDS_LOWER_BOUND '42' X-NDS_UPPER_BOUND '42' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.40 NAME 'loginDisabled' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Login Disabled' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.41 NAME 'loginExpirationTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Login Expiration Time' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.42 NAME 'loginGraceLimit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Login Grace Limit' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.43 NAME 'loginGraceRemaining' SYNTAX 2.16.840.1.113719.*******.22 SINGLE-VALUE X-NDS_NAME 'Login Grace Remaining' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.44 NAME 'loginIntruderAddress' SYNTAX 2.16.840.1.113719.*******.12 SINGLE-VALUE X-NDS_NAME 'Login Intruder Address' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.45 NAME 'loginIntruderAttempts' SYNTAX 2.16.840.1.113719.*******.22 SINGLE-VALUE X-NDS_NAME 'Login Intruder Attempts' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.46 NAME 'loginIntruderLimit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Login Intruder Limit' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.31 NAME 'intruderAttemptResetInterval' SYNTAX 2.16.840.1.113719.*******.27 SINGLE-VALUE X-NDS_NAME 'Intruder Attempt Reset Interval' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.47 NAME 'loginIntruderResetTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Login Intruder Reset Time' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.48 NAME 'loginMaximumSimultaneous' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Login Maximum Simultaneous' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.49 NAME 'loginScript' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Login Script' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.50 NAME 'loginTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Login Time' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME ( 'member' 'uniqueMember' ) SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Member' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.52 NAME 'Memory' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.22 NAME 'eMailAddress' SYNTAX 2.16.840.1.113719.*******.14{64512} X-NDS_NAME 'EMail Address' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.55 NAME 'networkAddress' SYNTAX 2.16.840.1.113719.*******.12 X-NDS_NAME 'Network Address' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.56 NAME 'networkAddressRestriction' SYNTAX 2.16.840.1.113719.*******.12 X-NDS_NAME 'Network Address Restriction' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.57 NAME 'notify' SYNTAX 2.16.840.1.113719.*******.25 X-NDS_NAME 'Notify' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.114 NAME 'Obituary' SYNTAX *******.4.1.1466.************{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( ******* NAME 'objectClass' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Object Class' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' )",
            "( 2.16.840.1.113719.*******.59 NAME 'operator' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Operator' X-NDS_SERVER_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME ( 'ou' 'organizationalUnitName' ) SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'OU' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME ( 'o' 'organizationname' ) SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'O' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME 'owner' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Owner' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.63 NAME 'pageDescriptionLanguage' SYNTAX *******.4.1.146***********.44{64} X-NDS_NAME 'Page Description Language' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.65 NAME 'passwordsUsed' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_NAME 'Passwords Used' X-NDS_NONREMOVABLE '1' X-NDS_HIDDEN '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.66 NAME 'passwordAllowChange' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Password Allow Change' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.67 NAME 'passwordExpirationInterval' SYNTAX 2.16.840.1.113719.*******.27 SINGLE-VALUE X-NDS_NAME 'Password Expiration Interval' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.68 NAME 'passwordExpirationTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Password Expiration Time' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.69 NAME 'passwordMinimumLength' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Password Minimum Length' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.70 NAME 'passwordRequired' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Password Required' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.71 NAME 'passwordUniqueRequired' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Password Unique Required' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.72 NAME 'path' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'Path' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME 'physicalDeliveryOfficeName' SYNTAX *******.4.1.1466.************{128} X-NDS_NAME 'Physical Delivery Office Name' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '128' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME 'postalAddress' SYNTAX *******.4.1.146***********.41{64512} X-NDS_NAME 'Postal Address' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME 'postalCode' SYNTAX *******.4.1.1466.************{40} X-NDS_NAME 'Postal Code' X-NDS_UPPER_BOUND '40' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME 'postOfficeBox' SYNTAX *******.4.1.1466.************{40} X-NDS_NAME 'Postal Office Box' X-NDS_UPPER_BOUND '40' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.80 NAME 'printJobConfiguration' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Print Job Configuration' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.79 NAME 'printerControl' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Printer Control' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.82 NAME 'privateKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Private Key' X-NDS_NONREMOVABLE '1' X-NDS_HIDDEN '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.83 NAME 'Profile' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.84 NAME 'publicKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Public Key' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_OPERATIONAL '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.85 NAME 'queue' SYNTAX 2.16.840.1.113719.*******.25 X-NDS_NAME 'Queue' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.86 NAME 'queueDirectory' SYNTAX *******.4.1.1466.************{255} SINGLE-VALUE X-NDS_NAME 'Queue Directory' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '255' X-NDS_SERVER_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.115 NAME 'Reference' SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_HIDDEN '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.88 NAME 'Replica' SYNTAX 2.16.840.1.113719.*******.16{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.89 NAME 'Resource' SYNTAX *******.4.1.1466.************ X-NDS_NONREMOVABLE '1' )",
            "( 2.5.4.33 NAME 'roleOccupant' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Role Occupant' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.116 NAME 'higherPrivileges' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Higher Privileges' X-NDS_SERVER_READ '1' X-NDS_NAME_VALUE_ACCESS '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.92 NAME 'securityEquals' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Security Equals' X-NDS_SERVER_READ '1' X-NDS_NAME_VALUE_ACCESS '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' )",
            "( 2.5.4.34 NAME 'seeAlso' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'See Also' X-NDS_NONREMOVABLE '1' )",
            "( 2.5.4.5 NAME 'serialNumber' SYNTAX *******.4.1.146***********.44{64} X-NDS_NAME 'Serial Number' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.95 NAME 'server' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Server' X-NDS_SERVER_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.5.4.8 NAME ( 'st' 'stateOrProvinceName' ) SYNTAX *******.4.1.1466.************{128} X-NDS_NAME 'S' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '128' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.98 NAME 'status' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Status' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_OPERATIONAL '1' )",
            "( 2.5.4.9 NAME 'street' SYNTAX *******.4.1.1466.************{128} X-NDS_NAME 'SA' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '128' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.102 NAME 'supportedTypefaces' SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'Supported Typefaces' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.101 NAME 'supportedServices' SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'Supported Services' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( 2.5.4.4 NAME ( 'sn' 'surname' ) SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'Surname' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.5.4.20 NAME 'telephoneNumber' SYNTAX *******.4.1.1466.***********0{64512} X-NDS_NAME 'Telephone Number' X-NDS_NONREMOVABLE '1' )",
            "( 2.5.4.12 NAME 'title' SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'Title' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.111 NAME 'User' SYNTAX *******.4.1.1466.************ X-NDS_SERVER_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.112 NAME 'Version' SYNTAX *******.4.1.1466.************{64} SINGLE-VALUE X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.1 NAME 'accountBalance' SYNTAX 2.16.840.1.113719.*******.22 SINGLE-VALUE X-NDS_NAME 'Account Balance' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.4 NAME 'allowUnlimitedCredit' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Allow Unlimited Credit' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.118 NAME 'lowConvergenceResetTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-NDS_NAME 'Low Convergence Reset Time' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.54 NAME 'minimumAccountBalance' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Minimum Account Balance' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.104 NAME 'lowConvergenceSyncInterval' SYNTAX 2.16.840.1.113719.*******.27 SINGLE-VALUE X-NDS_NAME 'Low Convergence Sync Interval' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.21 NAME 'Device' SYNTAX *******.4.1.1466.************ X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.53 NAME 'messageServer' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Message Server' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.34 NAME 'Language' SYNTAX 2.16.840.1.113719.*******.6{64512} SINGLE-VALUE X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.100 NAME 'supportedConnections' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Supported Connections' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.107 NAME 'typeCreatorMap' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Type Creator Map' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.108 NAME 'ndsUID' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'UID' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.24 NAME 'groupID' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'GID' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.110 NAME 'unknownBaseClass' SYNTAX *******.4.1.1466.************{32} SINGLE-VALUE USAGE directoryOperation X-NDS_NAME 'Unknown Base Class' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '32' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.87 NAME 'receivedUpTo' SYNTAX 2.16.840.1.113719.*******.19 NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Received Up To' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.33 NAME 'synchronizedUpTo' SYNTAX 2.16.840.1.113719.*******.19 NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Synchronized Up To' X-NDS_PUBLIC_READ '1' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.5 NAME 'authorityRevocation' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Authority Revocation' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.13 NAME 'certificateRevocation' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Certificate Revocation' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.17 NAME 'ndsCrossCertificatePair' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'Cross Certificate Pair' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.37 NAME 'lockedByIntruder' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Locked By Intruder' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.77 NAME 'printer' SYNTAX 2.16.840.1.113719.*******.25 X-NDS_NAME 'Printer' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.20 NAME 'detectIntruder' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Detect Intruder' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.38 NAME 'lockoutAfterDetection' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Lockout After Detection' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.32 NAME 'intruderLockoutResetInterval' SYNTAX 2.16.840.1.113719.*******.27 SINGLE-VALUE X-NDS_NAME 'Intruder Lockout Reset Interval' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.96 NAME 'serverHolds' SYNTAX 2.16.840.1.113719.*******.26 X-NDS_NAME 'Server Holds' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.91 NAME 'sAPName' SYNTAX *******.4.1.1466.************{47} SINGLE-VALUE X-NDS_NAME 'SAP Name' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '47' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.113 NAME 'Volume' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.35 NAME 'lastLoginTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Last Login Time' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.81 NAME 'printServer' SYNTAX 2.16.840.1.113719.*******.25 SINGLE-VALUE X-NDS_NAME 'Print Server' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.119 NAME 'nNSDomain' SYNTAX *******.4.1.1466.************{128} X-NDS_NAME 'NNS Domain' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '128' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.120 NAME 'fullName' SYNTAX *******.4.1.1466.************{127} X-NDS_NAME 'Full Name' X-NDS_UPPER_BOUND '127' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.121 NAME 'partitionControl' SYNTAX 2.16.840.1.113719.*******.25 NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Partition Control' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.122 NAME 'revision' SYNTAX 2.16.840.1.113719.*******.22 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Revision' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_SCHED_SYNC_NEVER '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.123 NAME 'certificateValidityInterval' SYNTAX 2.16.840.1.113719.*******.27{4294967295} SINGLE-VALUE X-NDS_NAME 'Certificate Validity Interval' X-NDS_LOWER_BOUND '60' X-NDS_UPPER_BOUND '-1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.124 NAME 'externalSynchronizer' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'External Synchronizer' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.125 NAME 'messagingDatabaseLocation' SYNTAX 2.16.840.1.113719.*******.15{64512} SINGLE-VALUE X-NDS_NAME 'Messaging Database Location' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.126 NAME 'messageRoutingGroup' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Message Routing Group' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.127 NAME 'messagingServer' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Messaging Server' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.128 NAME 'Postmaster' SYNTAX *******.4.1.1466.************ X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.162 NAME 'mailboxLocation' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Mailbox Location' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.163 NAME 'mailboxID' SYNTAX *******.4.1.1466.************{8} SINGLE-VALUE X-NDS_NAME 'Mailbox ID' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '8' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.164 NAME 'externalName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'External Name' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.165 NAME 'securityFlags' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Security Flags' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.166 NAME 'messagingServerType' SYNTAX *******.4.1.1466.************{32} SINGLE-VALUE X-NDS_NAME 'Messaging Server Type' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '32' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.167 NAME 'lastReferencedTime' SYNTAX 2.16.840.1.113719.*******.19 SINGLE-VALUE USAGE directoryOperation X-NDS_NAME 'Last Referenced Time' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.5.4.42 NAME 'givenName' SYNTAX *******.4.1.1466.************{32} X-NDS_NAME 'Given Name' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '32' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME 'initials' SYNTAX *******.4.1.1466.************{8} X-NDS_NAME 'Initials' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '8' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( ******** NAME 'generationQualifier' SYNTAX *******.4.1.1466.************{8} SINGLE-VALUE X-NDS_NAME 'Generational Qualifier' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '8' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.171 NAME 'profileMembership' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Profile Membership' X-NDS_NAME_VALUE_ACCESS '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.172 NAME 'dsRevision' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'DS Revision' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_OPERATIONAL '1' )",
            "( 2.16.840.1.113719.*******.173 NAME 'supportedGateway' SYNTAX *******.4.1.1466.************{4096} X-NDS_NAME 'Supported Gateway' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '4096' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.174 NAME 'equivalentToMe' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Equivalent To Me' X-NDS_SERVER_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' )",
            "( 2.16.840.1.113719.*******.175 NAME 'replicaUpTo' SYNTAX *******.4.1.1466.************{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Replica Up To' X-NDS_PUBLIC_READ '1' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.176 NAME 'partitionStatus' SYNTAX *******.4.1.1466.************{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Partition Status' X-NDS_PUBLIC_READ '1' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.177 NAME 'permanentConfigParms' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'Permanent Config Parms' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.178 NAME 'Timezone' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.179 NAME 'binderyRestrictionLevel' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-NDS_NAME 'Bindery Restriction Level' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.180 NAME 'transitiveVector' SYNTAX *******.4.1.1466.************{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Transitive Vector' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_SCHED_SYNC_NEVER '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.181 NAME 'T' SYNTAX *******.4.1.1466.************{32} X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '32' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.183 NAME 'purgeVector' SYNTAX 2.16.840.1.113719.*******.19 NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Purge Vector' X-NDS_PUBLIC_READ '1' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_SCHED_SYNC_NEVER '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.184 NAME 'synchronizationTolerance' SYNTAX 2.16.840.1.113719.*******.19 USAGE directoryOperation X-NDS_NAME 'Synchronization Tolerance' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.185 NAME 'passwordManagement' SYNTAX 2.16.840.1.113719.*******.0 SINGLE-VALUE USAGE directoryOperation X-NDS_NAME 'Password Management' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.186 NAME 'usedBy' SYNTAX 2.16.840.1.113719.*******.15{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Used By' X-NDS_SERVER_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.187 NAME 'Uses' SYNTAX 2.16.840.1.113719.*******.15{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_SERVER_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.500 NAME 'obituaryNotify' SYNTAX *******.4.1.1466.************{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Obituary Notify' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.501 NAME 'GUID' SYNTAX *******.4.1.1466.************{16} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_LOWER_BOUND '16' X-NDS_UPPER_BOUND '16' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.502 NAME 'otherGUID' SYNTAX *******.4.1.1466.************{16} USAGE directoryOperation X-NDS_NAME 'Other GUID' X-NDS_LOWER_BOUND '16' X-NDS_UPPER_BOUND '16' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.503 NAME 'auxiliaryClassFlag' SYNTAX 2.16.840.1.113719.*******.0 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Auxiliary Class Flag' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.504 NAME 'unknownAuxiliaryClass' SYNTAX *******.4.1.1466.************{32} USAGE directoryOperation X-NDS_NAME 'Unknown Auxiliary Class' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '32' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 0.9.2342.19200300.100.1.1 NAME ( 'uid' 'userId' ) SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'uniqueID' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 0.9.2342.19200300.100.1.25 NAME 'dc' SYNTAX *******.4.1.1466.************{64} X-NDS_NAME 'dc' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '64' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.507 NAME 'auxClassObjectClassBackup' SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'AuxClass Object Class Backup' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.508 NAME 'localReceivedUpTo' SYNTAX *******.4.1.1466.************{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NAME 'Local Received Up To' X-NDS_PUBLIC_READ '1' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.1.141.4.4 NAME 'federationControl' SYNTAX 2.16.840.1.113719.*******.15{64512} USAGE directoryOperation X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.1.141.4.2 NAME 'federationSearchPath' SYNTAX 2.16.840.1.113719.*******.6{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.1.141.4.3 NAME 'federationDNSName' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.1.141.4.1 NAME 'federationBoundaryType' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.1.14.4.1.4 NAME 'DirXML-Associations' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' )",
            "( 2.5.18.3 NAME 'creatorsName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.5.18.4 NAME 'modifiersName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NONREMOVABLE '1' X-NDS_FILTERED_REQUIRED '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.300 NAME 'languageId' SYNTAX *******.4.1.1466.************{64512} X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.********5 NAME 'ndsPredicate' SYNTAX 2.16.840.1.113719.*******.12 X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.********6 NAME 'ndsPredicateState' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.********7 NAME 'ndsPredicateFlush' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.********8 NAME 'ndsPredicateTimeout' SYNTAX *******.4.1.1466.************{2147483647} SINGLE-VALUE X-NDS_UPPER_BOUND '2147483647' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.********0 NAME 'ndsPredicateStatsDN' SYNTAX *******.4.1.1466.************ X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.********9 NAME 'ndsPredicateUseValues' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.601 NAME 'syncPanePoint' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_PUBLIC_READ '1' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.600 NAME 'syncWindowVector' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_PUBLIC_READ '1' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.602 NAME 'objectVersion' SYNTAX 2.16.840.1.113719.*******.19 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.********2 NAME 'memberQueryURL' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'memberQuery' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.302 NAME 'excludedMember' SYNTAX *******.4.1.1466.************ X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.525 NAME 'auxClassCompatibility' SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE directoryOperation X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.518 NAME 'ndsAgentPassword' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_HIDDEN '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.519 NAME 'ndsOperationCheckpoint' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.520 NAME 'localReferral' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.521 NAME 'treeReferral' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.522 NAME 'schemaResetLock' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.523 NAME 'modifiedACLEntry' SYNTAX *******.4.1.1466.************ USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.524 NAME 'monitoredConnection' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.526 NAME 'localFederationBoundary' SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.527 NAME 'replicationFilter' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.1.1.4.721 NAME 'ServerEBAEnabled' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.1.1.4.716 NAME 'EBATreeConfiguration' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_PUBLIC_READ '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.1.1.4.722 NAME 'EBAPartitionConfiguration' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.1.1.4.723 NAME 'EBAServerConfiguration' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.*******.296 NAME 'loginActivationTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.1.1.4.687 NAME 'UpdateInProgress' SYNTAX *******.4.1.1466.************ X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.1.1.4.720 NAME 'dsContainerReadyAttrs' SYNTAX *******.4.1.1466.************{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.1.1.4.400.1 NAME 'edirSchemaFlagVersion' SYNTAX 2.16.840.1.113719.*******.0 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NONREMOVABLE '1' X-NDS_HIDDEN '1' X-NDS_READ_FILTERED '1' )",
            "( 2.16.840.1.113719.*******.512 NAME 'indexDefinition' SYNTAX 2.16.840.1.113719.*******.6{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.513 NAME 'ndsStatusRepair' SYNTAX *******.4.1.1466.************{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.514 NAME 'ndsStatusExternalReference' SYNTAX *******.4.1.1466.************{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.515 NAME 'ndsStatusObituary' SYNTAX *******.4.1.1466.************{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.516 NAME 'ndsStatusSchema' SYNTAX *******.4.1.1466.************{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.517 NAME 'ndsStatusLimber' SYNTAX *******.4.1.1466.************{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.511 NAME 'authoritative' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113730.3.1.34 NAME 'ref' SYNTAX *******.4.1.1466.************{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.546 NAME 'CachedAttrsOnExtRefs' SYNTAX *******.4.1.1466.************{64512} X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.*******.547 NAME 'ExtRefLastUpdatedTime' SYNTAX 2.16.840.1.113719.*******.19 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation X-NDS_PUBLIC_READ '1' X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.1.4.688 NAME 'NCPKeyMaterialName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.1.4.713 NAME 'UTF8LoginScript' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.1.4.714 NAME 'loginScriptCharset' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.1.4.721 NAME 'NDSRightsToMonitor' SYNTAX *******.4.1.1466.************{64512} X-NDS_NEVER_SYNC '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.1.192 NAME 'lDAPLogLevel' SYNTAX *******.4.1.1466.************{32768} SINGLE-VALUE X-NDS_NAME 'LDAP Log Level' X-NDS_UPPER_BOUND '32768' )",
            "( 2.16.840.1.113719.********* NAME 'lDAPUDPPort' SYNTAX *******.4.1.1466.************{65535} SINGLE-VALUE X-NDS_NAME 'LDAP UDP Port' X-NDS_UPPER_BOUND '65535' )",
            "( 2.16.840.1.113719.*******.204 NAME 'lDAPLogFilename' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'LDAP Log Filename' )",
            "( 2.16.840.1.113719.*******.205 NAME 'lDAPBackupLogFilename' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'LDAP Backup Log Filename' )",
            "( 2.16.840.1.113719.*******.206 NAME 'lDAPLogSizeLimit' SYNTAX *******.4.1.1466.************{4294967295} SINGLE-VALUE X-NDS_NAME 'LDAP Log Size Limit' X-NDS_LOWER_BOUND '2048' X-NDS_UPPER_BOUND '-1' )",
            "( 2.16.840.1.113719.*******.194 NAME 'lDAPSearchSizeLimit' SYNTAX *******.4.1.1466.************{2147483647} SINGLE-VALUE X-NDS_NAME 'LDAP Search Size Limit' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '2147483647' )",
            "( 2.16.840.1.113719.*******.195 NAME 'lDAPSearchTimeLimit' SYNTAX *******.4.1.1466.************{2147483647} SINGLE-VALUE X-NDS_NAME 'LDAP Search Time Limit' X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '2147483647' )",
            "( 2.16.840.1.113719.*******.207 NAME 'lDAPSuffix' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'LDAP Suffix' )",
            "( 2.16.840.1.113719.********0 NAME 'ldapConfigVersion' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********4 NAME 'ldapReferral' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'LDAP Referral' )",
            "( 2.16.840.1.113719.********* NAME 'ldapDefaultReferralBehavior' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********3 NAME 'ldapSearchReferralUsage' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'LDAP:searchReferralUsage' )",
            "( 2.16.840.1.113719.********4 NAME 'lDAPOtherReferralUsage' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'LDAP:otherReferralUsage' )",
            "( 2.16.840.1.113719.******** NAME 'ldapHostServer' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'LDAP Host Server' )",
            "( 2.16.840.1.113719.******** NAME 'ldapGroupDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'LDAP Group' )",
            "( 2.16.840.1.113719.******** NAME 'ldapTraceLevel' SYNTAX *******.4.1.1466.************{32768} SINGLE-VALUE X-NDS_NAME 'LDAP Screen Level' X-NDS_UPPER_BOUND '32768' )",
            "( 2.16.840.1.113719.******** NAME 'searchSizeLimit' SYNTAX *******.4.1.1466.************{2147483647} SINGLE-VALUE X-NDS_UPPER_BOUND '2147483647' )",
            "( 2.16.840.1.113719.******** NAME 'searchTimeLimit' SYNTAX *******.4.1.1466.************{2147483647} SINGLE-VALUE X-NDS_UPPER_BOUND '2147483647' )",
            "( 2.16.840.1.113719.******** NAME 'ldapServerBindLimit' SYNTAX *******.4.1.1466.************{4294967295} SINGLE-VALUE X-NDS_NAME 'LDAP Server Bind Limit' X-NDS_UPPER_BOUND '-1' )",
            "( 2.16.840.1.113719.******** NAME 'ldapServerIdleTimeout' SYNTAX *******.4.1.1466.************{4294967295} SINGLE-VALUE X-NDS_NAME 'LDAP Server Idle Timeout' X-NDS_UPPER_BOUND '-1' )",
            "( 2.16.840.1.113719.1.27.4.8 NAME 'ldapEnableTCP' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'LDAP Enable TCP' )",
            "( 2.16.840.1.113719.********0 NAME 'ldapEnableSSL' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'LDAP Enable SSL' )",
            "( 2.16.840.1.113719.********1 NAME 'ldapTCPPort' SYNTAX *******.4.1.1466.************{65535} SINGLE-VALUE X-NDS_NAME 'LDAP TCP Port' X-NDS_UPPER_BOUND '65535' )",
            "( 2.16.840.1.113719.********3 NAME 'ldapSSLPort' SYNTAX *******.4.1.1466.************{65535} SINGLE-VALUE X-NDS_NAME 'LDAP SSL Port' X-NDS_UPPER_BOUND '65535' )",
            "( 2.16.840.1.113719.********1 NAME 'filteredReplicaUsage' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********2 NAME 'ldapKeyMaterialName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'LDAP:keyMaterialName' )",
            "( 2.16.840.1.113719.********2 NAME 'extensionInfo' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********5 NAME 'nonStdClientSchemaCompatMode' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********6 NAME 'sslEnableMutualAuthentication' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********2 NAME 'ldapEnablePSearch' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********3 NAME 'ldapMaximumPSearchOperations' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********4 NAME 'ldapIgnorePSearchLimitsForEvents' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********5 NAME 'ldapTLSTrustedRootContainer' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.********6 NAME 'ldapEnableMonitorEvents' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********7 NAME 'ldapMaximumMonitorEventsLoad' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********8 NAME 'ldapTLSRequired' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********9 NAME 'ldapTLSVerifyClientCertificate' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********1 NAME 'ldapDerefAlias' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********2 NAME 'ldapNonStdAllUserAttrsMode' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********5 NAME 'ldapBindRestrictions' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********9 NAME 'ldapInterfaces' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.27.4.80 NAME 'ldapChainSecureRequired' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.27.4.82 NAME 'ldapStdCompliance' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.27.4.83 NAME 'ldapDerefAliasOnAuth' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.27.4.84 NAME 'ldapGeneralizedTime' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.27.4.85 NAME 'ldapPermissiveModify' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.27.4.86 NAME 'ldapSSLConfig' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********5 NAME 'ldapServerList' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'LDAP Server List' )",
            "( 2.16.840.1.113719.********6 NAME 'ldapAttributeMap' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'LDAP Attribute Map v11' )",
            "( 2.16.840.1.113719.********7 NAME 'ldapClassMap' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'LDAP Class Map v11' )",
            "( 2.16.840.1.113719.********8 NAME 'ldapAllowClearTextPassword' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'LDAP Allow Clear Text Password' )",
            "( 2.16.840.1.113719.********9 NAME 'ldapAnonymousIdentity' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'LDAP Anonymous Identity' )",
            "( 2.16.840.1.113719.********2 NAME 'ldapAttributeList' SYNTAX 2.16.840.1.113719.*******.6{64512} )",
            "( 2.16.840.1.113719.********3 NAME 'ldapClassList' SYNTAX 2.16.840.1.113719.*******.6{64512} )",
            "( 2.16.840.1.113719.********6 NAME 'transitionGroupDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********* NAME 'ldapTransitionBackLink' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********* NAME 'ldapLBURPNumWriterThreads' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********0 NAME 'ldapServerDN' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'LDAP Server' )",
            "( 0.9.2342.19200300.100.1.3 NAME 'mail' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'Internet EMail Address' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113730.3.1.3 NAME 'employeeNumber' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'NSCP:employeeNumber' )",
            "( 2.16.840.1.113719.********* NAME 'referralExcludeFilter' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.********7 NAME 'referralIncludeFilter' SYNTAX *******.4.1.1466.************{64512} )",
            "( ******** NAME 'userCertificate' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'userCertificate' X-NDS_PUBLIC_READ '1' )",
            "( ******** NAME 'cACertificate' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'cACertificate' X-NDS_PUBLIC_READ '1' )",
            "( ******** NAME 'crossCertificatePair' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'crossCertificatePair' X-NDS_PUBLIC_READ '1' )",
            "( ******** NAME 'attributeCertificate' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.5.4.2 NAME 'knowledgeInformation' SYNTAX *******.4.1.1466.************{32768} X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '32768' )",
            "( 2.5.4.14 NAME 'searchGuide' SYNTAX *******.4.1.146***********.25{64512} X-NDS_NAME 'searchGuide' )",
            "( ******** NAME 'businessCategory' SYNTAX *******.4.1.1466.************{128} X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '128' )",
            "( ******** NAME 'telexNumber' SYNTAX *******.4.1.1466.***********2{64512} X-NDS_NAME 'telexNumber' )",
            "( ******** NAME 'teletexTerminalIdentifier' SYNTAX *******.4.1.1466.***********1{64512} X-NDS_NAME 'teletexTerminalIdentifier' )",
            "( ******** NAME 'x121Address' SYNTAX *******.4.1.146***********.36{15} X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '15' )",
            "( ******** NAME 'internationaliSDNNumber' SYNTAX *******.4.1.146***********.36{16} X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '16' )",
            "( ******** NAME 'registeredAddress' SYNTAX *******.4.1.146***********.41{64512} X-NDS_NAME 'registeredAddress' )",
            "( ******** NAME 'destinationIndicator' SYNTAX *******.4.1.146***********.44{128} X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '128' )",
            "( ******** NAME 'preferredDeliveryMethod' SYNTAX *******.4.1.146***********.14{64512} SINGLE-VALUE X-NDS_NAME 'preferredDeliveryMethod' )",
            "( ******** NAME 'presentationAddress' SYNTAX *******.4.1.146***********.43{64512} SINGLE-VALUE X-NDS_NAME 'presentationAddress' )",
            "( ******** NAME 'supportedApplicationContext' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'supportedApplicationContext' )",
            "( ******** NAME 'x500UniqueIdentifier' SYNTAX *******.4.1.146***********.6{64512} X-NDS_NAME 'x500UniqueIdentifier' )",
            "( ******** NAME 'dnQualifier' SYNTAX *******.4.1.146***********.44{64512} )",
            "( ******** NAME 'enhancedSearchGuide' SYNTAX *******.4.1.146***********.21{64512} X-NDS_NAME 'enhancedSearchGuide' )",
            "( ******** NAME 'protocolInformation' SYNTAX *******.4.1.146***********.42{64512} X-NDS_NAME 'protocolInformation' )",
            "( ******** NAME 'houseIdentifier' SYNTAX *******.4.1.1466.************{32768} X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '32768' )",
            "( ******** NAME 'supportedAlgorithms' SYNTAX *******.4.1.146***********.49{64512} X-NDS_NAME 'supportedAlgorithms' )",
            "( ******** NAME 'dmdName' SYNTAX *******.4.1.1466.************{32768} X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '32768' )",
            "( 0.9.2342.19200300.100.1.6 NAME 'roomNumber' SYNTAX *******.4.1.1466.************{64512} )",
            "( 0.9.2342.19200300.100.1.38 NAME 'associatedName' SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'dn' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'httpServerDN' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.******* NAME 'httpHostServerDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'httpThreadsPerCPU' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'httpIOBufferSize' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'httpRequestTimeout' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'httpKeepAliveRequestTimeout' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'httpSessionTimeout' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'httpKeyMaterialObject' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'httpTraceLevel' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******** NAME 'httpAuthRequiresTLS' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******1 NAME 'httpDefaultClearPort' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******2 NAME 'httpDefaultTLSPort' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******3 NAME 'httpBindRestrictions' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******.295 NAME 'emboxConfig' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.54.4.1.1 NAME 'trusteesOfNewObject' SYNTAX 2.16.840.1.113719.*******.17 X-NDS_NAME 'Trustees Of New Object' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'newObjectSDSRights' SYNTAX 2.16.840.1.113719.*******.17 X-NDS_NAME 'New Object's DS Rights' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'newObjectSFSRights' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'New Object's FS Rights' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'setupScript' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Setup Script' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'runSetupScript' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Run Setup Script' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'membersOfTemplate' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Members Of Template' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'volumeSpaceRestrictions' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'Volume Space Restrictions' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'setPasswordAfterCreate' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'Set Password After Create' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'homeDirectoryRights' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Home Directory Rights' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'newObjectSSelfRights' SYNTAX 2.16.840.1.113719.*******.17 X-NDS_NAME 'New Object's Self Rights' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.8.4.1 NAME 'digitalMeID' SYNTAX 2.16.840.1.113719.*******.15{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.8.4.2 NAME 'assistant' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.1.8.4.3 NAME 'assistantPhone' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 2.16.840.1.113719.1.8.4.4 NAME 'city' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.8.4.5 NAME 'company' SYNTAX *******.4.1.1466.************{64512} )",
            "( 0.9.2342.19200300.100.1.43 NAME 'co' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.8.4.6 NAME 'directReports' SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.19200300.100.1.10 NAME 'manager' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.1.8.4.7 NAME 'mailstop' SYNTAX *******.4.1.1466.************{64512} )",
            "( 0.9.2342.19200300.100.1.41 NAME 'mobile' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 0.9.2342.19200300.100.1.40 NAME 'personalTitle' SYNTAX *******.4.1.1466.************{64512} )",
            "( 0.9.2342.19200300.100.1.42 NAME 'pager' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 2.16.840.1.113719.1.8.4.8 NAME 'workforceID' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.8.4.9 NAME 'instantMessagingID' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.8.4.10 NAME 'preferredName' SYNTAX *******.4.1.1466.************{64512} )",
            "( 0.9.2342.19200300.100.1.7 NAME 'photo' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.8.4.11 NAME 'jobCode' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'siteLocation' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'employeeStatus' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113730.3.1.4 NAME 'employeeType' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'costCenter' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'costCenterDescription' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'tollFreePhoneNumber' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 2.16.840.1.113719.******** NAME 'otherPhoneNumber' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 2.16.840.1.113719.******** NAME 'managerWorkforceID' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'jackNumber' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113730.3.1.2 NAME 'departmentNumber' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'vehicleInformation' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'accessCardNumber' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'isManager' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.******** NAME 'homeCity' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'homeEmailAddress' SYNTAX *******.4.1.1466.************{64512} )",
            "( *******.4.1.1466.101.120.31 NAME 'homeFax' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 0.9.2342.19200300.100.1.20 NAME 'homePhone' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 2.16.840.1.113719.******** NAME 'homeState' SYNTAX *******.4.1.1466.************{64512} )",
            "( 0.9.2342.19200300.100.1.39 NAME 'homePostalAddress' SYNTAX *******.4.1.146***********.41{64512} )",
            "( 2.16.840.1.113719.******** NAME 'homeZipCode' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'personalMobile' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 2.16.840.1.113719.******** NAME 'children' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'spouse' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'vendorName' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'vendorAddress' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.******** NAME 'vendorPhoneNumber' SYNTAX *******.4.1.1466.***********0{64512} )",
            "( 2.16.840.1.113719.*******.303 NAME 'dgIdentity' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME_VALUE_ACCESS '1' )",
            "( 2.16.840.1.113719.*******.304 NAME 'dgTimeOut' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******.305 NAME 'dgAllowUnknown' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******.306 NAME 'dgAllowDuplicates' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******.546 NAME 'allowAliasToAncestor' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.********.1 NAME 'sASSecurityDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'SAS:Security DN' X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.********.2 NAME 'sASServiceDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'SAS:Service DN' X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.********.3 NAME 'sASSecretStore' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'SAS:SecretStore' )",
            "( 2.16.840.1.113719.********.4 NAME 'sASSecretStoreKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_NAME 'SAS:SecretStore:Key' X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.********.5 NAME 'sASSecretStoreData' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_NAME 'SAS:SecretStore:Data' X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.********.6 NAME 'sASPKIStoreKeys' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_NAME 'SAS:PKIStore:Keys' X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'nDSPKIPublicKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Public Key' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.2 NAME 'nDSPKIPrivateKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Private Key' )",
            "( 2.16.840.1.113719.********.3 NAME 'nDSPKIPublicKeyCertificate' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Public Key Certificate' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.4 NAME 'nDSPKICertificateChain' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'NDSPKI:Certificate Chain' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.16 NAME 'nDSPKIPublicKeyEC' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Public Key EC' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.17 NAME 'nDSPKIPrivateKeyEC' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Private Key EC' )",
            "( 2.16.840.1.113719.********.18 NAME 'nDSPKIPublicKeyCertificateEC' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Public Key Certificate EC' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.19 NAME 'crossCertificatePairEC' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'Cross Certificate Pair EC' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.20 NAME 'nDSPKICertificateChainEC' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'NDSPKI:Certificate Chain EC' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.5 NAME 'nDSPKIParentCA' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Parent CA' )",
            "( 2.16.840.1.113719.********.6 NAME 'nDSPKIParentCADN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'NDSPKI:Parent CA DN' )",
            "( 2.16.840.1.113719.********.20 NAME 'nDSPKISuiteBMode' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'NDSPKI:SuiteBMode' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.7 NAME 'nDSPKIKeyFile' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Key File' )",
            "( 2.16.840.1.113719.********.8 NAME 'nDSPKISubjectName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Subject Name' )",
            "( 2.16.840.1.113719.********.11 NAME 'nDSPKIGivenName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Given Name' )",
            "( 2.16.840.1.113719.********.9 NAME 'nDSPKIKeyMaterialDN' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'NDSPKI:Key Material DN' )",
            "( 2.16.840.1.113719.********.10 NAME 'nDSPKITreeCADN' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'NDSPKI:Tree CA DN' )",
            "( 2.5.4.59 NAME 'cAECCertificate' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.12 NAME 'nDSPKIUserCertificateInfo' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'NDSPKI:userCertificateInfo' )",
            "( 2.16.840.1.113719.********.13 NAME 'nDSPKITrustedRootCertificate' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Trusted Root Certificate' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.14 NAME 'nDSPKINotBefore' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Not Before' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.15 NAME 'nDSPKINotAfter' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:Not After' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.101 NAME 'nDSPKISDKeyServerDN' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'NDSPKI:SD Key Server DN' X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.********.102 NAME 'nDSPKISDKeyStruct' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'NDSPKI:SD Key Struct' )",
            "( 2.16.840.1.113719.********.103 NAME 'nDSPKISDKeyCert' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:SD Key Cert' )",
            "( 2.16.840.1.113719.********.104 NAME 'nDSPKISDKeyID' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'NDSPKI:SD Key ID' )",
            "( 2.16.840.1.113719.********.105 NAME 'nDSPKIKeystore' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_NAME 'NDSPKI:Keystore' X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.********.106 NAME 'ndspkiAdditionalRoots' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.2.3 NAME 'masvLabel' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.2.4 NAME 'masvProposedLabel' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.2.5 NAME 'masvDefaultRange' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.2.6 NAME 'masvAuthorizedRange' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.2.7 NAME 'masvDomainPolicy' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.1.8 NAME 'masvClearanceNames' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.1.9 NAME 'masvLabelNames' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.1.10 NAME 'masvLabelSecrecyLevelNames' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.1.11 NAME 'masvLabelSecrecyCategoryNames' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.1.12 NAME 'masvLabelIntegrityLevelNames' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.1.13 NAME 'masvLabelIntegrityCategoryNames' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.1.14 NAME 'masvPolicyUpdate' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.31.4.1.16 NAME 'masvNDSAttributeLabels' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.31.4.1.15 NAME 'masvPolicyDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.2 NAME 'sASLoginSequence' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'SAS:Login Sequence' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.8 NAME 'sASLoginPolicyUpdate' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'SAS:Login Policy Update' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.38 NAME 'sasNMASProductOptions' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.74 NAME 'sasAuditConfiguration' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.14 NAME 'sASNDSPasswordWindow' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'SAS:NDS Password Window' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.15 NAME 'sASPolicyCredentials' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'SAS:Policy Credentials' X-NDS_SERVER_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.16 NAME 'sASPolicyMethods' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'SAS:Policy Methods' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.17 NAME 'sASPolicyObjectVersion' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'SAS:Policy Object Version' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.18 NAME 'sASPolicyServiceSubtypes' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'SAS:Policy Service Subtypes' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.19 NAME 'sASPolicyServices' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'SAS:Policy Services' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.20 NAME 'sASPolicyUsers' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'SAS:Policy Users' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.21 NAME 'sASAllowNDSPasswordWindow' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'SAS:Allow NDS Password Window' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.9 NAME 'sASMethodIdentifier' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'SAS:Method Identifier' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.10 NAME 'sASMethodVendor' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'SAS:Method Vendor' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.11 NAME 'sASAdvisoryMethodGrade' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'SAS:Advisory Method Grade' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.12 NAME 'sASVendorSupport' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'SAS:Vendor Support' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.13 NAME 'sasCertificateSearchContainers' SYNTAX *******.4.1.1466.************ X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.70 NAME 'sasNMASMethodConfigData' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.22 NAME 'sASLoginClientMethodNetWare' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'SAS:Login Client Method NetWare' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.23 NAME 'sASLoginServerMethodNetWare' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'SAS:Login Server Method NetWare' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.24 NAME 'sASLoginClientMethodWINNT' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'SAS:Login Client Method WINNT' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.25 NAME 'sASLoginServerMethodWINNT' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'SAS:Login Server Method WINNT' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.26 NAME 'sasLoginClientMethodSolaris' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.27 NAME 'sasLoginServerMethodSolaris' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.28 NAME 'sasLoginClientMethodLinux' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.29 NAME 'sasLoginServerMethodLinux' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.30 NAME 'sasLoginClientMethodTru64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.31 NAME 'sasLoginServerMethodTru64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.32 NAME 'sasLoginClientMethodAIX' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.33 NAME 'sasLoginServerMethodAIX' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.34 NAME 'sasLoginClientMethodHPUX' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.35 NAME 'sasLoginServerMethodHPUX' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1000 NAME 'sasLoginClientMethods390' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1001 NAME 'sasLoginServerMethods390' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1002 NAME 'sasLoginClientMethodLinuxX64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1003 NAME 'sasLoginServerMethodLinuxX64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1004 NAME 'sasLoginClientMethodWinX64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1005 NAME 'sasLoginServerMethodWinX64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1006 NAME 'sasLoginClientMethodSolaris64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1007 NAME 'sasLoginServerMethodSolaris64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1008 NAME 'sasLoginClientMethodAIX64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1009 NAME 'sasLoginServerMethodAIX64' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1011 NAME 'sasLoginServerMethodSolarisi386' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1012 NAME 'sasLoginClientMethodSolarisi386' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.78 NAME 'sasUnsignedMethodModules' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.79 NAME 'sasServerModuleName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.80 NAME 'sasServerModuleEntryPointName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.81 NAME 'sasSASLMechanismName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.82 NAME 'sasSASLMechanismEntryPointName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.83 NAME 'sasClientModuleName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.84 NAME 'sasClientModuleEntryPointName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.36 NAME 'sASLoginMethodContainerDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'SAS:Login Method Container DN' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.37 NAME 'sASLoginPolicyDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'SAS:Login Policy DN' X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.63 NAME 'sasPostLoginMethodContainerDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.38 NAME 'rADIUSActiveConnections' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'RADIUS:Active Connections' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.39 NAME 'rADIUSAgedInterval' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:Aged Interval' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.40 NAME 'rADIUSAttributeList' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'RADIUS:Attribute List' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.41 NAME 'rADIUSAttributeLists' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'RADIUS:Attribute Lists' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.42 NAME 'rADIUSClient' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'RADIUS:Client' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.43 NAME 'rADIUSCommonNameResolution' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:Common Name Resolution' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.44 NAME 'rADIUSConcurrentLimit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:Concurrent Limit' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.45 NAME 'rADIUSConnectionHistory' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'RADIUS:Connection History' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.46 NAME 'rADIUSDASVersion' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:DAS Version' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.47 NAME 'rADIUSDefaultProfile' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'RADIUS:Default Profile' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.48 NAME 'rADIUSDialAccessGroup' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:Dial Access Group' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.49 NAME 'rADIUSEnableCommonNameLogin' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'RADIUS:Enable Common Name Login' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.50 NAME 'rADIUSEnableDialAccess' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NAME 'RADIUS:Enable Dial Access' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.51 NAME 'rADIUSInterimAcctingTimeout' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:Interim Accting Timeout' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.52 NAME 'rADIUSLookupContexts' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'RADIUS:Lookup Contexts' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.53 NAME 'rADIUSMaxDASHistoryRecord' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:Max DAS History Record' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.54 NAME 'rADIUSMaximumHistoryRecord' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:Maximum History Record' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.55 NAME 'rADIUSPassword' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'RADIUS:Password' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.56 NAME 'rADIUSPasswordPolicy' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'RADIUS:Password Policy' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.57 NAME 'rADIUSPrivateKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'RADIUS:Private Key' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.58 NAME 'rADIUSProxyContext' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'RADIUS:Proxy Context' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.59 NAME 'rADIUSProxyDomain' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'RADIUS:Proxy Domain' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.60 NAME 'rADIUSProxyTarget' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'RADIUS:Proxy Target' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.61 NAME 'rADIUSPublicKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'RADIUS:Public Key' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.62 NAME 'rADIUSServiceList' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_NAME 'RADIUS:Service List' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.3 NAME 'sASLoginSecret' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'SAS:Login Secret' X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.4 NAME 'sASLoginSecretKey' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'SAS:Login Secret Key' X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.5 NAME 'sASEncryptionType' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'SAS:Encryption Type' X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.6 NAME 'sASLoginConfiguration' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'SAS:Login Configuration' X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.7 NAME 'sASLoginConfigurationKey' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'SAS:Login Configuration Key' X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.73 NAME 'sasDefaultLoginSequence' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.64 NAME 'sasAuthorizedLoginSequences' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.69 NAME 'sasAllowableSubjectNames' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.71 NAME 'sasLoginFailureDelay' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.72 NAME 'sasMethodVersion' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1010 NAME 'sasUpdateLoginInfo' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1011 NAME 'sasOTPEnabled' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1012 NAME 'sasOTPCounter' SYNTAX 2.16.840.1.113719.*******.22 SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1013 NAME 'sasOTPLookAheadWindow' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1014 NAME 'sasOTPDigits' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1015 NAME 'sasOTPReSync' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.1.39.42.1.0.1016 NAME 'sasUpdateLoginTimeInterval' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.******* NAME 'snmpGroupDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'snmpServerList' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.******* NAME 'snmpTrapConfig' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'snmpTrapDescription' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'snmpTrapInterval' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.******* NAME 'snmpTrapDisable' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******.528 NAME 'ndapPartitionPasswordMgmt' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.529 NAME 'ndapClassPasswordMgmt' SYNTAX 2.16.840.1.113719.*******.0 X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.530 NAME 'ndapPasswordMgmt' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.537 NAME 'ndapPartitionLoginMgmt' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.538 NAME 'ndapClassLoginMgmt' SYNTAX 2.16.840.1.113719.*******.0 X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.539 NAME 'ndapLoginMgmt' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.1 NAME 'nspmPasswordKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.1.39.43.4.2 NAME 'nspmPassword' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.1.39.43.4.3 NAME 'nspmDistributionPassword' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.1.39.43.4.4 NAME 'nspmPasswordHistory' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.1.39.43.4.5 NAME 'nspmAdministratorChangeCount' SYNTAX 2.16.840.1.113719.*******.22 SINGLE-VALUE USAGE directoryOperation X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.1.39.43.4.6 NAME 'nspmPasswordPolicyDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.7 NAME 'nspmPreviousDistributionPassword' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.1.39.43.4.8 NAME 'nspmDoNotExpirePassword' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.********.27.8.1.16 NAME 'pwdChangedTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.********.27.8.1.17 NAME 'pwdAccountLockedTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.********.27.8.1.19 NAME 'pwdFailureTime' SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.16.840.1.113719.1.39.43.4.100 NAME 'nspmConfigurationOptions' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.102 NAME 'nspmChangePasswordMessage' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.103 NAME 'nspmPasswordHistoryLimit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.104 NAME 'nspmPasswordHistoryExpiration' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( *******.********.27.8.1.4 NAME 'pwdInHistory' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.105 NAME 'nspmMinPasswordLifetime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.106 NAME 'nspmAdminsDoNotExpirePassword' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.107 NAME 'nspmPasswordACL' SYNTAX 2.16.840.1.113719.*******.17 )",
            "( 2.16.840.1.113719.1.39.43.4.200 NAME 'nspmMaximumLength' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.201 NAME 'nspmMinUpperCaseCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.202 NAME 'nspmMaxUpperCaseCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.203 NAME 'nspmMinLowerCaseCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.204 NAME 'nspmMaxLowerCaseCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.205 NAME 'nspmNumericCharactersAllowed' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.206 NAME 'nspmNumericAsFirstCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.207 NAME 'nspmNumericAsLastCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.208 NAME 'nspmMinNumericCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.209 NAME 'nspmMaxNumericCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.210 NAME 'nspmSpecialCharactersAllowed' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.211 NAME 'nspmSpecialAsFirstCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.212 NAME 'nspmSpecialAsLastCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.213 NAME 'nspmMinSpecialCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.214 NAME 'nspmMaxSpecialCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.215 NAME 'nspmMaxRepeatedCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.216 NAME 'nspmMaxConsecutiveCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.217 NAME 'nspmMinUniqueCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.218 NAME 'nspmDisallowedAttributeValues' SYNTAX *******.4.1.1466.************{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.219 NAME 'nspmExcludeList' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.220 NAME 'nspmCaseSensitive' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.221 NAME 'nspmPolicyPrecedence' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.222 NAME 'nspmExtendedCharactersAllowed' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.223 NAME 'nspmExtendedAsFirstCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.224 NAME 'nspmExtendedAsLastCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.225 NAME 'nspmMinExtendedCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.226 NAME 'nspmMaxExtendedCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.227 NAME 'nspmUpperAsFirstCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.228 NAME 'nspmUpperAsLastCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.229 NAME 'nspmLowerAsFirstCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.230 NAME 'nspmLowerAsLastCharacter' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.231 NAME 'nspmComplexityRules' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.233 NAME 'nspmAD2K8Syntax' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.234 NAME 'nspmAD2K8maxViolation' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.235 NAME 'nspmXCharLimit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.236 NAME 'nspmXCharHistoryLimit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.237 NAME 'nspmUnicodeAllowed' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.238 NAME 'nspmNonAlphaCharactersAllowed' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.239 NAME 'nspmMinNonAlphaCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.240 NAME 'nspmMaxNonAlphaCharacters' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.241 NAME 'nspmGraceLoginHistoryLimit' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.300 NAME 'nspmPolicyAgentContainerDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.301 NAME 'nspmPolicyAgentNetWare' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.302 NAME 'nspmPolicyAgentWINNT' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.303 NAME 'nspmPolicyAgentSolaris' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.304 NAME 'nspmPolicyAgentLinux' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.305 NAME 'nspmPolicyAgentAIX' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.39.43.4.306 NAME 'nspmPolicyAgentHPUX' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 0.9.2342.19200300.100.1.55 NAME 'audio' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113730.3.1.1 NAME 'carLicense' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113730.3.1.241 NAME 'displayName' SYNTAX *******.4.1.1466.************{64512} )",
            "( 0.9.2342.19200300.100.1.60 NAME 'jpegPhoto' SYNTAX *******.4.1.1466.************{64512} )",
            "( *******.4.1.250.1.57 NAME 'labeledUri' SYNTAX *******.4.1.1466.************{64512} )",
            "( 0.9.2342.19200300.100.1.7 NAME 'ldapPhoto' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113730.3.1.39 NAME 'preferredLanguage' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 0.9.2342.19200300.100.1.21 NAME 'secretary' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.1.40 NAME 'userSMIMECertificate' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113730.3.1.216 NAME 'userPKCS12' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.12.4.1.0 NAME 'auditAEncryptionKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'Audit:A Encryption Key' )",
            "( 2.16.840.1.113719.1.12.4.2.0 NAME 'auditBEncryptionKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'Audit:B Encryption Key' )",
            "( 2.16.840.1.113719.1.12.4.3.0 NAME 'auditContents' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Audit:Contents' )",
            "( 2.16.840.1.113719.1.12.4.4.0 NAME 'auditType' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Audit:Type' )",
            "( 2.16.840.1.113719.1.12.4.5.0 NAME 'auditCurrentEncryptionKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'Audit:Current Encryption Key' )",
            "( 2.16.840.1.113719.1.12.4.6.0 NAME 'auditFileLink' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'Audit:File Link' )",
            "( 2.16.840.1.113719.1.12.4.7.0 NAME 'auditLinkList' SYNTAX *******.4.1.1466.************ X-NDS_NAME 'Audit:Link List' )",
            "( 2.16.840.1.113719.1.******** NAME 'auditPath' SYNTAX 2.16.840.1.113719.*******.15{64512} SINGLE-VALUE X-NDS_NAME 'Audit:Path' )",
            "( 2.16.840.1.113719.********.0 NAME 'auditPolicy' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NAME 'Audit:Policy' )",
            "( 2.16.840.1.113719.********.1 NAME 'wANMANWANPolicy' SYNTAX 2.16.840.1.113719.*******.13{64512} X-NDS_NAME 'WANMAN:WAN Policy' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.2 NAME 'wANMANLANAreaMembership' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'WANMAN:LAN Area Membership' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.3 NAME 'wANMANCost' SYNTAX *******.4.1.1466.************{64512} X-NDS_NAME 'WANMAN:Cost' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.4 NAME 'wANMANDefaultCost' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'WANMAN:Default Cost' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********** NAME 'rbsAssignedRoles' SYNTAX 2.16.840.1.113719.*******.25 )",
            "( 2.16.840.1.113719.********** NAME 'rbsContent' SYNTAX 2.16.840.1.113719.*******.25 )",
            "( 2.16.840.1.113719.********** NAME 'rbsContentMembership' SYNTAX 2.16.840.1.113719.*******.25 )",
            "( 2.16.840.1.113719.********** NAME 'rbsEntryPoint' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.********** NAME 'rbsMember' SYNTAX 2.16.840.1.113719.*******.25 )",
            "( 2.16.840.1.113719.********** NAME 'rbsOwnedCollections' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.********** NAME 'rbsPath' SYNTAX 2.16.840.1.113719.*******.25 )",
            "( 2.16.840.1.113719.********** NAME 'rbsParameters' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.********** NAME 'rbsTaskRights' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.********** NAME 'rbsTrusteeOf' SYNTAX 2.16.840.1.113719.*******.25 )",
            "( 2.16.840.1.113719.********** NAME 'rbsType' SYNTAX *******.4.1.1466.************{256} SINGLE-VALUE X-NDS_LOWER_BOUND '1' X-NDS_UPPER_BOUND '256' )",
            "( 2.16.840.1.113719.1.135.4.41 NAME 'rbsURL' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.********** NAME 'rbsTaskTemplates' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.********** NAME 'rbsTaskTemplatesURL' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.********** NAME 'rbsGALabel' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.********** NAME 'rbsPageMembership' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.********** NAME 'rbsTargetObjectType' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.********** NAME 'rbsContext' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********** NAME 'rbsXMLInfo' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.********** NAME 'rbsAssignedRoles2' SYNTAX 2.16.840.1.113719.*******.25 )",
            "( 2.16.840.1.113719.********** NAME 'rbsOwnedCollections2' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.*******.540 NAME 'prSyncPolicyDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.*******.541 NAME 'prSyncAttributes' SYNTAX *******.4.1.1466.************{64512} X-NDS_SERVER_READ '1' )",
            "( 2.16.840.1.113719.*******.542 NAME 'dsEncryptedReplicationConfig' SYNTAX 2.16.840.1.113719.*******.19 )",
            "( 2.16.840.1.113719.*******.543 NAME 'encryptionPolicyDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******.544 NAME 'attrEncryptionRequiresSecure' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******.545 NAME 'attrEncryptionDefinition' SYNTAX 2.16.840.1.113719.*******.6{64512} X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.********.16 NAME 'ndspkiCRLFileName' SYNTAX 2.16.840.1.113719.*******.15{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.17 NAME 'ndspkiStatus' SYNTAX *******.4.1.1466.************ X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.18 NAME 'ndspkiIssueTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.19 NAME 'ndspkiNextIssueTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.20 NAME 'ndspkiAttemptTime' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.21 NAME 'ndspkiTimeInterval' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.22 NAME 'ndspkiCRLMaxProcessingInterval' SYNTAX 2.16.840.1.113719.*******.27 SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.23 NAME 'ndspkiCRLNumber' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.24 NAME 'ndspkiDistributionPoints' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.25 NAME 'ndspkiCRLProcessData' SYNTAX *******.4.1.1466.************{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.26 NAME 'ndspkiCRLConfigurationDNList' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.27 NAME 'ndspkiCADN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.28 NAME 'ndspkiCRLContainerDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.29 NAME 'ndspkiIssuedCertContainerDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.30 NAME 'ndspkiDistributionPointDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.31 NAME 'ndspkiCRLConfigurationDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.32 NAME 'ndspkiDirectory' SYNTAX 2.16.840.1.113719.*******.15{64512} )",
            "( 2.5.4.38 NAME 'authorityRevocationList' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'ndspkiAuthorityRevocationList' X-NDS_PUBLIC_READ '1' )",
            "( 2.5.4.39 NAME 'certificateRevocationList' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'ndspkiCertificateRevocationList' X-NDS_PUBLIC_READ '1' )",
            "( 2.5.4.53 NAME 'deltaRevocationList' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NAME 'ndspkiDeltaRevocationList' X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.36 NAME 'ndspkiTrustedRootList' SYNTAX *******.4.1.1466.************ X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.37 NAME 'ndspkiSecurityRightsLevel' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.********.38 NAME 'ndspkiKMOExport' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.********.39 NAME 'ndspkiCRLECConfigurationDNList' SYNTAX 2.16.840.1.113719.*******.15{64512} X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.40 NAME 'ndspkiCRLType' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.41 NAME 'ndspkiCRLExtendValidity' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.42 NAME 'ndspkiDefaultRSAKeySize' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.43 NAME 'ndspkiDefaultECCurve' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.********.44 NAME 'ndspkiDefaultCertificateLife' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.******* NAME 'notfSMTPEmailHost' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.******* NAME 'notfSMTPEmailFrom' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.******* NAME 'notfSMTPEmailUserName' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.******* NAME 'notfMergeTemplateData' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.******* NAME 'notfMergeTemplateSubject' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.1 NAME 'nsimRequiredQuestions' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.2 NAME 'nsimRandomQuestions' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.3 NAME 'nsimNumberRandomQuestions' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.4 NAME 'nsimMinResponseLength' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.5 NAME 'nsimMaxResponseLength' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.6 NAME 'nsimForgottenLoginConfig' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.7 NAME 'nsimForgottenAction' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.8 NAME 'nsimAssignments' SYNTAX *******.4.1.1466.************ X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.9 NAME 'nsimChallengeSetDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.10 NAME 'nsimChallengeSetGUID' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.11 NAME 'nsimPwdRuleEnforcement' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*********.12 NAME 'nsimHint' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.*********.13 NAME 'nsimPasswordReminder' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.266.4.4 NAME 'sssProxyStoreKey' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE USAGE directoryOperation X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.1.266.4.5 NAME 'sssProxyStoreSecrets' SYNTAX *******.4.1.1466.************{64512} USAGE directoryOperation X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.1.266.4.6 NAME 'sssActiveServerList' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.266.4.7 NAME 'sssCacheRefreshInterval' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.266.4.8 NAME 'sssAdminList' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.1.266.4.9 NAME 'sssAdminGALabel' SYNTAX *******.4.1.1466.************{64512} SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.266.4.10 NAME 'sssEnableReadTimestamps' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.266.4.11 NAME 'sssDisableMasterPasswords' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.266.4.12 NAME 'sssEnableAdminAccess' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.266.4.13 NAME 'sssReadSecretPolicies' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.266.4.14 NAME 'sssServerPolicyOverrideDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.*******.531 NAME 'eDirCloneSource' SYNTAX 2.16.840.1.113719.*******.15{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.*******.532 NAME 'eDirCloneKeys' SYNTAX *******.4.1.1466.************{64512} NO-USER-MODIFICATION USAGE directoryOperation X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' X-NDS_HIDDEN '1' )",
            "( 2.16.840.1.113719.*******.533 NAME 'eDirCloneLock' SYNTAX 2.16.840.1.113719.*******.15{64512} SINGLE-VALUE X-NDS_NOT_SCHED_SYNC_IMMEDIATE '1' )",
            "( 2.16.840.1.113719.1.1.4.711 NAME 'groupMember' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113719.1.1.4.712 NAME 'nestedConfig' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113719.1.1.4.717 NAME 'xdasDSConfiguration' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.1.4.718 NAME 'xdasConfiguration' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.1.4.719 NAME 'xdasVersion' SYNTAX *******.4.1.1466.************{32768} SINGLE-VALUE X-NDS_UPPER_BOUND '32768' )",
            "( 2.16.840.1.113719.1.347.4.79 NAME 'NAuditInstrumentation' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.347.4.2 NAME 'NAuditLoggingServer' SYNTAX *******.4.1.1466.************ X-NDS_PUBLIC_READ '1' )",
            "( 2.16.840.1.113719.1.1.4.724 NAME 'cefConfiguration' SYNTAX *******.4.1.1466.************{64512} )",
            "( 2.16.840.1.113719.1.1.4.725 NAME 'cefVersion' SYNTAX *******.4.1.1466.************{32768} SINGLE-VALUE X-NDS_UPPER_BOUND '32768' )"
        ],
        "createTimestamp": [],
        "dITContentRules": [],
        "dITStructureRules": [],
        "ldapSyntaxes": [
            "( *******.4.1.146***********.1 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.2 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.3 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.4 X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.*********** X-NDS_SYNTAX '21' )",
            "( *******.4.1.146***********.6 X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.*********** X-NDS_SYNTAX '7' )",
            "( 2.16.840.1.113719.*******.6 X-NDS_SYNTAX '6' )",
            "( *******.4.1.146***********.8 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.9 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.10 X-NDS_SYNTAX '9' )",
            "( 2.16.840.1.113719.*******.22 X-NDS_SYNTAX '22' )",
            "( *******.4.1.146***********.11 X-NDS_SYNTAX '3' )",
            "( *******.4.1.1466.************ X-NDS_SYNTAX '1' )",
            "( *******.4.1.146***********.13 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.14 X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.************ X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.16 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.17 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.18 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.19 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.20 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.21 X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.************ X-NDS_SYNTAX '11' )",
            "( *******.4.1.146***********.23 X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.************ X-NDS_SYNTAX '24' )",
            "( *******.4.1.146***********.25 X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.************ X-NDS_SYNTAX '2' )",
            "( *******.4.1.1466.************ X-NDS_SYNTAX '8' )",
            "( *******.4.1.146***********.28 X-NDS_SYNTAX '9' )",
            "( 1.2.840.113556.1.4.906 X-NDS_SYNTAX '29' )",
            "( *******.4.1.1466.***********4 X-NDS_SYNTAX '3' )",
            "( *******.4.1.1466.***********6 X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.***********7 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.29 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.30 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.31 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.32 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.33 X-NDS_SYNTAX '3' )",
            "( *******.4.1.1466.***********5 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.34 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.35 X-NDS_SYNTAX '3' )",
            "( 2.16.840.1.113719.*******.19 X-NDS_SYNTAX '19' )",
            "( *******.4.1.146***********.36 X-NDS_SYNTAX '5' )",
            "( 2.16.840.1.113719.*******.17 X-NDS_SYNTAX '17' )",
            "( *******.4.1.146***********.37 X-NDS_SYNTAX '3' )",
            "( 2.16.840.1.113719.*******.13 X-NDS_SYNTAX '13' )",
            "( *******.4.1.1466.************ X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.************ X-NDS_SYNTAX '20' )",
            "( *******.4.1.146***********.39 X-NDS_SYNTAX '3' )",
            "( *******.4.1.146***********.41 X-NDS_SYNTAX '18' )",
            "( *******.4.1.146***********.43 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.44 X-NDS_SYNTAX '4' )",
            "( *******.4.1.146***********.42 X-NDS_SYNTAX '9' )",
            "( 2.16.840.1.113719.*******.16 X-NDS_SYNTAX '16' )",
            "( *******.4.1.1466.***********8 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.45 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.46 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.47 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.48 X-NDS_SYNTAX '9' )",
            "( *******.4.1.146***********.49 X-NDS_SYNTAX '9' )",
            "( 2.16.840.1.113719.*******.12 X-NDS_SYNTAX '12' )",
            "( 2.16.840.1.113719.*******.23 X-NDS_SYNTAX '23' )",
            "( 2.16.840.1.113719.*******.15 X-NDS_SYNTAX '15' )",
            "( 2.16.840.1.113719.*******.14 X-NDS_SYNTAX '14' )",
            "( *******.4.1.1466.***********0 X-NDS_SYNTAX '10' )",
            "( *******.4.1.1466.***********1 X-NDS_SYNTAX '9' )",
            "( *******.4.1.1466.***********2 X-NDS_SYNTAX '9' )",
            "( 2.16.840.1.113719.*******.25 X-NDS_SYNTAX '25' )",
            "( *******.4.1.1466.***********3 X-NDS_SYNTAX '9' )",
            "( 2.16.840.1.113719.*******.26 X-NDS_SYNTAX '26' )",
            "( 2.16.840.1.113719.*******.27 X-NDS_SYNTAX '27' )"
        ],
        "matchingRuleUse": [],
        "matchingRules": [],
        "modifyTimestamp": [
            "20190831135835Z"
        ],
        "nameForms": [],
        "objectClass": [
            "top",
            "subschema"
        ],
        "objectClasses": [
            "( 2.5.6.0 NAME 'Top' STRUCTURAL MUST objectClass MAY ( cAPublicKey $ cAPrivateKey $ certificateValidityInterval $ authorityRevocation $ lastReferencedTime $ equivalentToMe $ ACL $ backLink $ binderyProperty $ Obituary $ Reference $ revision $ ndsCrossCertificatePair $ certificateRevocation $ usedBy $ GUID $ otherGUID $ DirXML-Associations $ creatorsName $ modifiersName $ objectVersion $ auxClassCompatibility $ unknownBaseClass $ unknownAuxiliaryClass $ masvProposedLabel $ masvDefaultRange $ masvAuthorizedRange $ auditFileLink $ rbsAssignedRoles $ rbsOwnedCollections $ rbsAssignedRoles2 $ rbsOwnedCollections2 ) X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES '16#subtree#[Creator]#[Entry Rights]' )",
            "( *******.********.******** NAME 'aliasObject' SUP Top STRUCTURAL MUST aliasedObjectName X-NDS_NAME 'Alias' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( ******* NAME 'Country' SUP Top STRUCTURAL MUST c MAY ( description $ searchGuide $ sssActiveServerList $ sssServerPolicyOverrideDN ) X-NDS_NAMING 'c' X-NDS_CONTAINMENT ( 'Top' 'treeRoot' 'domain' ) X-NDS_NONREMOVABLE '1' )",
            "( ******* NAME 'Locality' SUP Top STRUCTURAL MAY ( description $ l $ seeAlso $ st $ street $ searchGuide $ sssActiveServerList $ sssServerPolicyOverrideDN ) X-NDS_NAMING ( 'l' 'st' ) X-NDS_CONTAINMENT ( 'Country' 'organizationalUnit' 'Locality' 'Organization' 'domain' ) X-NDS_NONREMOVABLE '1' )",
            "( ******* NAME 'Organization' SUP ( ndsLoginProperties $ ndsContainerLoginProperties ) STRUCTURAL MUST o MAY ( description $ facsimileTelephoneNumber $ l $ loginScript $ eMailAddress $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ printJobConfiguration $ printerControl $ seeAlso $ st $ street $ telephoneNumber $ loginIntruderLimit $ intruderAttemptResetInterval $ detectIntruder $ lockoutAfterDetection $ intruderLockoutResetInterval $ nNSDomain $ mailboxLocation $ mailboxID $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ internationaliSDNNumber $ businessCategory $ searchGuide $ rADIUSAttributeLists $ rADIUSDefaultProfile $ rADIUSDialAccessGroup $ rADIUSEnableDialAccess $ rADIUSServiceList $ sssActiveServerList $ sssServerPolicyOverrideDN $ userPassword ) X-NDS_NAMING 'o' X-NDS_CONTAINMENT ( 'Top' 'treeRoot' 'Country' 'Locality' 'domain' ) X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES ( '2#entry#[Self]#loginScript' '2#entry#[Self]#printJobConfiguration') )",
            "( ******* NAME 'organizationalUnit' SUP ( ndsLoginProperties $ ndsContainerLoginProperties ) STRUCTURAL MUST ou MAY ( description $ facsimileTelephoneNumber $ l $ loginScript $ eMailAddress $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ printJobConfiguration $ printerControl $ seeAlso $ st $ street $ telephoneNumber $ loginIntruderLimit $ intruderAttemptResetInterval $ detectIntruder $ lockoutAfterDetection $ intruderLockoutResetInterval $ nNSDomain $ mailboxLocation $ mailboxID $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ internationaliSDNNumber $ businessCategory $ searchGuide $ rADIUSAttributeLists $ rADIUSDefaultProfile $ rADIUSDialAccessGroup $ rADIUSEnableDialAccess $ rADIUSServiceList $ sssActiveServerList $ sssServerPolicyOverrideDN $ userPassword ) X-NDS_NAMING 'ou' X-NDS_CONTAINMENT ( 'Locality' 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NAME 'Organizational Unit' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES ( '2#entry#[Self]#loginScript' '2#entry#[Self]#printJobConfiguration') )",
            "( ******* NAME 'organizationalRole' SUP Top STRUCTURAL MUST cn MAY ( description $ facsimileTelephoneNumber $ l $ eMailAddress $ ou $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ roleOccupant $ seeAlso $ st $ street $ telephoneNumber $ mailboxLocation $ mailboxID $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ internationaliSDNNumber ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NAME 'Organizational Role' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( ******* NAME ( 'groupOfNames' 'group' 'groupOfUniqueNames' ) SUP Top STRUCTURAL MUST cn MAY ( description $ l $ member $ ou $ o $ owner $ seeAlso $ groupID $ fullName $ eMailAddress $ mailboxLocation $ mailboxID $ Profile $ profileMembership $ loginScript $ businessCategory $ nspmPasswordPolicyDN ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NAME 'Group' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( ******* NAME 'Person' SUP ndsLoginProperties STRUCTURAL MUST ( cn $ sn ) MAY ( description $ seeAlso $ telephoneNumber $ fullName $ givenName $ initials $ generationQualifier $ uid $ assistant $ assistantPhone $ city $ st $ company $ co $ directReports $ manager $ mailstop $ mobile $ personalTitle $ pager $ workforceID $ instantMessagingID $ preferredName $ photo $ jobCode $ siteLocation $ employeeStatus $ employeeType $ costCenter $ costCenterDescription $ tollFreePhoneNumber $ otherPhoneNumber $ managerWorkforceID $ roomNumber $ jackNumber $ departmentNumber $ vehicleInformation $ accessCardNumber $ isManager $ userPassword ) X-NDS_NAMING ( 'cn' 'uid' ) X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( ******* NAME 'organizationalPerson' SUP Person STRUCTURAL MAY ( facsimileTelephoneNumber $ l $ eMailAddress $ ou $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ st $ street $ title $ mailboxLocation $ mailboxID $ uid $ mail $ employeeNumber $ destinationIndicator $ internationaliSDNNumber $ preferredDeliveryMethod $ registeredAddress $ teletexTerminalIdentifier $ telexNumber $ x121Address $ businessCategory $ roomNumber $ x500UniqueIdentifier ) X-NDS_NAMING ( 'cn' 'ou' 'uid' ) X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NAME 'Organizational Person' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113730.3.2.2 NAME 'inetOrgPerson' SUP organizationalPerson STRUCTURAL MAY ( groupMembership $ ndsHomeDirectory $ loginAllowedTimeMap $ loginDisabled $ loginExpirationTime $ loginGraceLimit $ loginGraceRemaining $ loginIntruderAddress $ loginIntruderAttempts $ loginIntruderResetTime $ loginMaximumSimultaneous $ loginScript $ loginTime $ networkAddressRestriction $ networkAddress $ passwordsUsed $ passwordAllowChange $ passwordExpirationInterval $ passwordExpirationTime $ passwordMinimumLength $ passwordRequired $ passwordUniqueRequired $ printJobConfiguration $ privateKey $ Profile $ publicKey $ securityEquals $ accountBalance $ allowUnlimitedCredit $ minimumAccountBalance $ messageServer $ Language $ ndsUID $ lockedByIntruder $ serverHolds $ lastLoginTime $ typeCreatorMap $ higherPrivileges $ printerControl $ securityFlags $ profileMembership $ Timezone $ sASServiceDN $ sASSecretStore $ sASSecretStoreKey $ sASSecretStoreData $ sASPKIStoreKeys $ userCertificate $ nDSPKIUserCertificateInfo $ nDSPKIKeystore $ rADIUSActiveConnections $ rADIUSAttributeLists $ rADIUSConcurrentLimit $ rADIUSConnectionHistory $ rADIUSDefaultProfile $ rADIUSDialAccessGroup $ rADIUSEnableDialAccess $ rADIUSPassword $ rADIUSServiceList $ audio $ businessCategory $ carLicense $ departmentNumber $ employeeNumber $ employeeType $ displayName $ givenName $ homePhone $ homePostalAddress $ initials $ jpegPhoto $ labeledUri $ mail $ manager $ mobile $ o $ pager $ ldapPhoto $ preferredLanguage $ roomNumber $ secretary $ uid $ userSMIMECertificate $ x500UniqueIdentifier $ userPKCS12 $ sssProxyStoreKey $ sssProxyStoreSecrets $ sssServerPolicyOverrideDN ) X-NDS_NAME 'User' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES ( '2#subtree#[Self]#[All Attributes Rights]' '6#entry#[Self]#loginScript' '1#subtree#[Root Template]#[Entry Rights]' '2#entry#[Public]#messageServer' '2#entry#[Root Template]#groupMembership' '6#entry#[Self]#printJobConfiguration' '2#entry#[Root Template]#networkAddress') )",
            "( ******** NAME 'Device' SUP Top STRUCTURAL MUST cn MAY ( description $ l $ networkAddress $ ou $ o $ owner $ seeAlso $ serialNumber ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.4 NAME 'Computer' SUP Device STRUCTURAL MAY ( operator $ server $ status ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.17 NAME 'Printer' SUP Device STRUCTURAL MAY ( Cartridge $ printerConfiguration $ defaultQueue $ hostDevice $ printServer $ Memory $ networkAddressRestriction $ notify $ operator $ pageDescriptionLanguage $ queue $ status $ supportedTypefaces ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.21 NAME 'Resource' SUP Top ABSTRACT MUST cn MAY ( description $ hostResourceName $ l $ ou $ o $ seeAlso $ Uses ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.20 NAME 'Queue' SUP Resource STRUCTURAL MUST queueDirectory MAY ( Device $ operator $ server $ User $ networkAddress $ Volume $ hostServer ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES '2#subtree#[Root Template]#[All Attributes Rights]' )",
            "( 2.16.840.1.113719.*******.3 NAME 'binderyQueue' SUP Queue STRUCTURAL MUST binderyType X-NDS_NAMING ( 'cn' 'binderyType' ) X-NDS_NAME 'Bindery Queue' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES '2#subtree#[Root Template]#[All Attributes Rights]' )",
            "( 2.16.840.1.113719.*******.26 NAME 'Volume' SUP Resource STRUCTURAL MUST hostServer MAY status X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES ( '2#entry#[Root Template]#hostResourceName' '2#entry#[Root Template]#hostServer') )",
            "( 2.16.840.1.113719.*******.7 NAME 'directoryMap' SUP Resource STRUCTURAL MUST hostServer MAY path X-NDS_NAME 'Directory Map' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.19 NAME 'Profile' SUP Top STRUCTURAL MUST ( cn $ loginScript ) MAY ( description $ l $ ou $ o $ seeAlso $ fullName ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.22 NAME 'Server' SUP Top ABSTRACT MUST cn MAY ( description $ hostDevice $ l $ ou $ o $ privateKey $ publicKey $ Resource $ seeAlso $ status $ User $ Version $ networkAddress $ accountBalance $ allowUnlimitedCredit $ minimumAccountBalance $ fullName $ securityEquals $ securityFlags $ Timezone $ ndapClassPasswordMgmt $ ndapClassLoginMgmt ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES ( '2#entry#[Public]#networkAddress' '16#subtree#[Self]#[Entry Rights]') )",
            "( 2.16.840.1.113719.*******.10 NAME 'ncpServer' SUP Server STRUCTURAL MAY ( operator $ supportedServices $ messagingServer $ dsRevision $ permanentConfigParms $ ndsPredicateStatsDN $ languageId $ indexDefinition $ CachedAttrsOnExtRefs $ NCPKeyMaterialName $ NDSRightsToMonitor $ ldapServerDN $ httpServerDN $ emboxConfig $ sASServiceDN $ cACertificate $ cAECCertificate $ nDSPKIPublicKey $ nDSPKIPrivateKey $ nDSPKICertificateChain $ nDSPKIParentCADN $ nDSPKISDKeyID $ nDSPKISDKeyStruct $ snmpGroupDN $ wANMANWANPolicy $ wANMANLANAreaMembership $ wANMANCost $ wANMANDefaultCost $ encryptionPolicyDN $ eDirCloneSource $ eDirCloneLock $ xdasDSConfiguration $ xdasConfiguration $ xdasVersion $ NAuditLoggingServer $ NAuditInstrumentation $ cefConfiguration $ cefVersion ) X-NDS_NAME 'NCP Server' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES '2#entry#[Public]#messagingServer' )",
            "( 2.16.840.1.113719.*******.18 NAME 'printServer' SUP Server STRUCTURAL MAY ( operator $ printer $ sAPName ) X-NDS_NAME 'Print Server' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES '2#subtree#[Root Template]#[All Attributes Rights]' )",
            "( 2.16.840.1.113719.*******.31 NAME 'CommExec' SUP Server STRUCTURAL MAY networkAddressRestriction X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.2 NAME 'binderyObject' SUP Top STRUCTURAL MUST ( binderyObjectRestriction $ binderyType $ cn ) X-NDS_NAMING ( 'cn' 'binderyType' ) X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NAME 'Bindery Object' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.15 NAME 'Partition' AUXILIARY MAY ( Convergence $ partitionCreationTime $ Replica $ inheritedACL $ lowConvergenceSyncInterval $ receivedUpTo $ synchronizedUpTo $ authorityRevocation $ certificateRevocation $ cAPrivateKey $ cAPublicKey $ ndsCrossCertificatePair $ lowConvergenceResetTime $ highConvergenceSyncInterval $ partitionControl $ replicaUpTo $ partitionStatus $ transitiveVector $ purgeVector $ synchronizationTolerance $ obituaryNotify $ localReceivedUpTo $ federationControl $ syncPanePoint $ syncWindowVector $ EBAPartitionConfiguration $ authoritative $ allowAliasToAncestor $ sASSecurityDN $ masvLabel $ ndapPartitionPasswordMgmt $ ndapPartitionLoginMgmt $ prSyncPolicyDN $ dsEncryptedReplicationConfig ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.0 NAME 'aFPServer' SUP Server STRUCTURAL MAY ( serialNumber $ supportedConnections ) X-NDS_NAME 'AFP Server' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.27 NAME 'messagingServer' SUP Server STRUCTURAL MAY ( messagingDatabaseLocation $ messageRoutingGroup $ Postmaster $ supportedServices $ messagingServerType $ supportedGateway ) X-NDS_NAME 'Messaging Server' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES ( '1#subtree#[Self]#[Entry Rights]' '2#subtree#[Self]#[All Attributes Rights]' '6#entry#[Self]#status' '2#entry#[Public]#messagingServerType' '2#entry#[Public]#messagingDatabaseLocation') )",
            "( 2.16.840.1.113719.*******.28 NAME 'messageRoutingGroup' SUP groupOfNames STRUCTURAL X-NDS_NAME 'Message Routing Group' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES ( '1#subtree#[Self]#[Entry Rights]' '2#subtree#[Self]#[All Attributes Rights]') )",
            "( 2.16.840.1.113719.*******.29 NAME 'externalEntity' SUP Top STRUCTURAL MUST cn MAY ( description $ seeAlso $ facsimileTelephoneNumber $ l $ eMailAddress $ ou $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ st $ street $ title $ externalName $ mailboxLocation $ mailboxID ) X-NDS_NAMING ( 'cn' 'ou' ) X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NAME 'External Entity' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES '2#entry#[Public]#externalName' )",
            "( 2.16.840.1.113719.*******.30 NAME 'List' SUP Top STRUCTURAL MUST cn MAY ( description $ l $ member $ ou $ o $ eMailAddress $ mailboxLocation $ mailboxID $ owner $ seeAlso $ fullName ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' X-NDS_ACL_TEMPLATES '2#entry#[Root Template]#member' )",
            "( 2.16.840.1.113719.*******.32 NAME 'treeRoot' SUP Top STRUCTURAL MUST T MAY ( EBATreeConfiguration $ sssActiveServerList ) X-NDS_NAMING 'T' X-NDS_NAME 'Tree Root' X-NDS_NONREMOVABLE '1' )",
            "( 0.9.2342.19200300.100.4.13 NAME 'domain' SUP ( Top $ ndsLoginProperties $ ndsContainerLoginProperties ) STRUCTURAL MUST dc MAY ( searchGuide $ o $ seeAlso $ businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ l $ associatedName $ description $ sssActiveServerList $ sssServerPolicyOverrideDN $ userPassword ) X-NDS_NAMING 'dc' X-NDS_CONTAINMENT ( 'Top' 'treeRoot' 'Country' 'Locality' 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NONREMOVABLE '1' )",
            "( *******.4.1.1466.344 NAME 'dcObject' AUXILIARY MUST dc X-NDS_NAMING 'dc' X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.33 NAME 'ndsLoginProperties' SUP Top ABSTRACT MAY ( groupMembership $ loginAllowedTimeMap $ loginDisabled $ loginExpirationTime $ loginGraceLimit $ loginGraceRemaining $ loginIntruderAddress $ loginIntruderAttempts $ loginIntruderResetTime $ loginMaximumSimultaneous $ loginScript $ loginTime $ networkAddressRestriction $ networkAddress $ passwordsUsed $ passwordAllowChange $ passwordExpirationInterval $ passwordExpirationTime $ passwordMinimumLength $ passwordRequired $ passwordUniqueRequired $ privateKey $ Profile $ publicKey $ securityEquals $ accountBalance $ allowUnlimitedCredit $ minimumAccountBalance $ Language $ lockedByIntruder $ serverHolds $ lastLoginTime $ higherPrivileges $ securityFlags $ profileMembership $ Timezone $ loginActivationTime $ UTF8LoginScript $ loginScriptCharset $ sASNDSPasswordWindow $ sASLoginSecret $ sASLoginSecretKey $ sASEncryptionType $ sASLoginConfiguration $ sASLoginConfigurationKey $ sasLoginFailureDelay $ sasDefaultLoginSequence $ sasAuthorizedLoginSequences $ sasAllowableSubjectNames $ sasUpdateLoginInfo $ sasOTPEnabled $ sasOTPCounter $ sasOTPDigits $ sasOTPReSync $ sasUpdateLoginTimeInterval $ ndapPasswordMgmt $ ndapLoginMgmt $ nspmPasswordKey $ nspmPassword $ pwdChangedTime $ pwdAccountLockedTime $ pwdFailureTime $ nspmDoNotExpirePassword $ nspmDistributionPassword $ nspmPreviousDistributionPassword $ nspmPasswordHistory $ nspmAdministratorChangeCount $ nspmPasswordPolicyDN $ nsimHint $ nsimPasswordReminder $ userPassword ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.********* NAME 'federationBoundary' AUXILIARY MUST federationBoundaryType MAY ( federationControl $ federationDNSName $ federationSearchPath ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.34 NAME 'ndsContainerLoginProperties' SUP Top ABSTRACT MAY ( loginIntruderLimit $ intruderAttemptResetInterval $ detectIntruder $ lockoutAfterDetection $ intruderLockoutResetInterval $ sasLoginFailureDelay $ sasDefaultLoginSequence $ sasAuthorizedLoginSequences $ sasUpdateLoginInfo $ sasOTPEnabled $ sasOTPDigits $ sasUpdateLoginTimeInterval $ ndapPasswordMgmt $ ndapLoginMgmt $ nspmPasswordPolicyDN ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.1.27.6.3 NAME 'ndsPredicateStats' SUP Top STRUCTURAL MUST ( cn $ ndsPredicateState $ ndsPredicateFlush ) MAY ( ndsPredicate $ ndsPredicateTimeout $ ndsPredicateUseValues ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.1.1.6.400.1 NAME 'edirSchemaVersion' SUP Top ABSTRACT MAY edirSchemaFlagVersion X-NDS_NOT_CONTAINER '1' X-NDS_NONREMOVABLE '1' )",
            "( 2.16.840.1.113719.*******.47 NAME 'immediateSuperiorReference' AUXILIARY MAY ref X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.******** NAME 'ldapServer' SUP Top STRUCTURAL MUST cn MAY ( ldapHostServer $ ldapGroupDN $ ldapTraceLevel $ ldapServerBindLimit $ ldapServerIdleTimeout $ lDAPUDPPort $ lDAPSearchSizeLimit $ lDAPSearchTimeLimit $ lDAPLogLevel $ lDAPLogFilename $ lDAPBackupLogFilename $ lDAPLogSizeLimit $ Version $ searchSizeLimit $ searchTimeLimit $ ldapEnableTCP $ ldapTCPPort $ ldapEnableSSL $ ldapSSLPort $ ldapKeyMaterialName $ filteredReplicaUsage $ extensionInfo $ nonStdClientSchemaCompatMode $ sslEnableMutualAuthentication $ ldapEnablePSearch $ ldapMaximumPSearchOperations $ ldapIgnorePSearchLimitsForEvents $ ldapTLSTrustedRootContainer $ ldapEnableMonitorEvents $ ldapMaximumMonitorEventsLoad $ ldapTLSRequired $ ldapTLSVerifyClientCertificate $ ldapConfigVersion $ ldapDerefAlias $ ldapNonStdAllUserAttrsMode $ ldapBindRestrictions $ ldapDefaultReferralBehavior $ ldapReferral $ ldapSearchReferralUsage $ lDAPOtherReferralUsage $ ldapLBURPNumWriterThreads $ ldapInterfaces $ ldapChainSecureRequired $ ldapStdCompliance $ ldapDerefAliasOnAuth $ ldapGeneralizedTime $ ldapPermissiveModify $ ldapSSLConfig ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' 'domain' ) X-NDS_NAME 'LDAP Server' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.1.27.6.2 NAME 'ldapGroup' SUP Top STRUCTURAL MUST cn MAY ( ldapReferral $ ldapServerList $ ldapAllowClearTextPassword $ ldapAnonymousIdentity $ lDAPSuffix $ ldapAttributeMap $ ldapClassMap $ ldapSearchReferralUsage $ lDAPOtherReferralUsage $ transitionGroupDN $ ldapAttributeList $ ldapClassList $ ldapConfigVersion $ Version $ ldapDefaultReferralBehavior $ ldapTransitionBackLink $ ldapSSLConfig $ referralIncludeFilter $ referralExcludeFilter ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' 'domain' ) X-NDS_NAME 'LDAP Group' X-NDS_NOT_CONTAINER '1' )",
            "( *******2 NAME 'pkiCA' AUXILIARY MAY ( cACertificate $ certificateRevocationList $ authorityRevocationList $ crossCertificatePair $ attributeCertificate $ publicKey $ privateKey $ networkAddress $ loginTime $ lastLoginTime $ cAECCertificate $ crossCertificatePairEC ) X-NDS_NOT_CONTAINER '1' )",
            "( *******1 NAME 'pkiUser' AUXILIARY MAY userCertificate X-NDS_NOT_CONTAINER '1' )",
            "( ******** NAME 'strongAuthenticationUser' AUXILIARY MAY userCertificate X-NDS_NOT_CONTAINER '1' )",
            "( ******** NAME 'applicationProcess' SUP Top STRUCTURAL MUST cn MAY ( seeAlso $ ou $ l $ description ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' 'domain' ) )",
            "( ******** NAME 'applicationEntity' SUP Top STRUCTURAL MUST ( presentationAddress $ cn ) MAY ( supportedApplicationContext $ seeAlso $ ou $ o $ l $ description ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' 'domain' ) )",
            "( ******** NAME 'dSA' SUP applicationEntity STRUCTURAL MAY knowledgeInformation X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' 'domain' ) )",
            "( ******** NAME 'certificationAuthority' AUXILIARY MUST ( authorityRevocationList $ certificateRevocationList $ cACertificate ) MAY crossCertificatePair X-NDS_NOT_CONTAINER '1' )",
            "( ******** NAME 'userSecurityInformation' AUXILIARY MAY supportedAlgorithms X-NDS_NOT_CONTAINER '1' )",
            "( *******0 NAME 'dmd' SUP ndsLoginProperties AUXILIARY MUST dmdName MAY ( searchGuide $ seeAlso $ businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ l $ description $ userPassword ) X-NDS_NOT_CONTAINER '1' )",
            "( ********.2 NAME 'certificationAuthority-V2' AUXILIARY MUST ( authorityRevocationList $ certificateRevocationList $ cACertificate ) MAY ( crossCertificatePair $ deltaRevocationList ) X-NDS_NAME 'certificationAuthorityVer2' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.******* NAME 'httpServer' SUP Top STRUCTURAL MUST cn MAY ( httpHostServerDN $ httpThreadsPerCPU $ httpIOBufferSize $ httpRequestTimeout $ httpKeepAliveRequestTimeout $ httpSessionTimeout $ httpKeyMaterialObject $ httpTraceLevel $ httpAuthRequiresTLS $ httpDefaultClearPort $ httpDefaultTLSPort $ httpBindRestrictions ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'domain' 'Country' 'Locality' 'organizationalUnit' 'Organization' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'Template' SUP Top STRUCTURAL MUST cn MAY ( trusteesOfNewObject $ newObjectSDSRights $ newObjectSFSRights $ setupScript $ runSetupScript $ membersOfTemplate $ volumeSpaceRestrictions $ setPasswordAfterCreate $ homeDirectoryRights $ accountBalance $ allowUnlimitedCredit $ description $ eMailAddress $ facsimileTelephoneNumber $ groupMembership $ higherPrivileges $ ndsHomeDirectory $ l $ Language $ loginAllowedTimeMap $ loginDisabled $ loginExpirationTime $ loginGraceLimit $ loginMaximumSimultaneous $ loginScript $ mailboxID $ mailboxLocation $ member $ messageServer $ minimumAccountBalance $ networkAddressRestriction $ newObjectSSelfRights $ ou $ passwordAllowChange $ passwordExpirationInterval $ passwordExpirationTime $ passwordMinimumLength $ passwordRequired $ passwordUniqueRequired $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ Profile $ st $ street $ securityEquals $ securityFlags $ seeAlso $ telephoneNumber $ title $ assistant $ assistantPhone $ city $ company $ co $ manager $ managerWorkforceID $ mailstop $ siteLocation $ employeeType $ costCenter $ costCenterDescription $ tollFreePhoneNumber $ departmentNumber ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'organizationalUnit' 'Organization' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.******* NAME 'homeInfo' AUXILIARY MAY ( homeCity $ homeEmailAddress $ homeFax $ homePhone $ homeState $ homePostalAddress $ homeZipCode $ personalMobile $ spouse $ children ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.******* NAME 'contingentWorker' AUXILIARY MAY ( vendorName $ vendorAddress $ vendorPhoneNumber ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*******.45 NAME 'dynamicGroup' SUP ( groupOfNames $ ndsLoginProperties ) STRUCTURAL MAY ( memberQueryURL $ excludedMember $ dgIdentity $ dgAllowUnknown $ dgTimeOut $ dgAllowDuplicates $ userPassword ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*******.46 NAME 'dynamicGroupAux' SUP ( groupOfNames $ ndsLoginProperties ) AUXILIARY MAY ( memberQueryURL $ excludedMember $ dgIdentity $ dgAllowUnknown $ dgTimeOut $ dgAllowDuplicates $ userPassword ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'sASSecurity' SUP Top STRUCTURAL MUST cn MAY ( nDSPKITreeCADN $ masvPolicyDN $ sASLoginPolicyDN $ sASLoginMethodContainerDN $ sasPostLoginMethodContainerDN $ nspmPolicyAgentContainerDN ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Top' 'treeRoot' 'Country' 'Organization' 'domain' ) X-NDS_NAME 'SAS:Security' )",
            "( 2.16.840.1.113719.********.2 NAME 'sASService' SUP Resource STRUCTURAL MAY ( hostServer $ privateKey $ publicKey $ allowUnlimitedCredit $ fullName $ lastLoginTime $ lockedByIntruder $ loginAllowedTimeMap $ loginDisabled $ loginExpirationTime $ loginIntruderAddress $ loginIntruderAttempts $ loginIntruderResetTime $ loginMaximumSimultaneous $ loginTime $ networkAddress $ networkAddressRestriction $ notify $ operator $ owner $ path $ securityEquals $ securityFlags $ status $ Version $ nDSPKIKeyMaterialDN $ ndspkiKMOExport ) X-NDS_NAMING 'cn' X-NDS_NAME 'SAS:Service' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.1 NAME 'nDSPKICertificateAuthority' SUP Top STRUCTURAL MUST cn MAY ( hostServer $ nDSPKIPublicKey $ nDSPKIPrivateKey $ nDSPKIPublicKeyCertificate $ nDSPKICertificateChain $ nDSPKICertificateChainEC $ nDSPKIParentCA $ nDSPKIParentCADN $ nDSPKISubjectName $ nDSPKIPublicKeyEC $ nDSPKIPrivateKeyEC $ nDSPKIPublicKeyCertificateEC $ crossCertificatePairEC $ nDSPKISuiteBMode $ cACertificate $ cAECCertificate $ ndspkiCRLContainerDN $ ndspkiIssuedCertContainerDN $ ndspkiCRLConfigurationDNList $ ndspkiCRLECConfigurationDNList $ ndspkiSecurityRightsLevel $ ndspkiDefaultRSAKeySize $ ndspkiDefaultECCurve $ ndspkiDefaultCertificateLife ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sASSecurity' X-NDS_NAME 'NDSPKI:Certificate Authority' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.2 NAME 'nDSPKIKeyMaterial' SUP Top STRUCTURAL MUST cn MAY ( hostServer $ nDSPKIKeyFile $ nDSPKIPrivateKey $ nDSPKIPublicKey $ nDSPKIPublicKeyCertificate $ nDSPKICertificateChain $ nDSPKISubjectName $ nDSPKIGivenName $ ndspkiAdditionalRoots $ nDSPKINotBefore $ nDSPKINotAfter ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'sASSecurity' 'Organization' 'organizationalUnit' 'domain' ) X-NDS_NAME 'NDSPKI:Key Material' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.3 NAME 'nDSPKITrustedRoot' SUP Top STRUCTURAL MUST cn MAY ndspkiTrustedRootList X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'sASSecurity' 'Organization' 'organizationalUnit' 'Country' 'Locality' 'domain' ) X-NDS_NAME 'NDSPKI:Trusted Root' )",
            "( 2.16.840.1.113719.********.4 NAME 'nDSPKITrustedRootObject' SUP Top STRUCTURAL MUST ( cn $ nDSPKITrustedRootCertificate ) MAY ( nDSPKISubjectName $ nDSPKINotBefore $ nDSPKINotAfter $ externalName $ givenName $ sn ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'nDSPKITrustedRoot' X-NDS_NAME 'NDSPKI:Trusted Root Object' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.101 NAME 'nDSPKISDKeyAccessPartition' SUP Top STRUCTURAL MUST cn X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sASSecurity' X-NDS_NAME 'NDSPKI:SD Key Access Partition' )",
            "( 2.16.840.1.113719.********.102 NAME 'nDSPKISDKeyList' SUP Top STRUCTURAL MUST cn MAY ( nDSPKISDKeyServerDN $ nDSPKISDKeyStruct $ nDSPKISDKeyCert ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'nDSPKISDKeyAccessPartition' X-NDS_NAME 'NDSPKI:SD Key List' )",
            "( 2.16.840.1.113719.********.1 NAME 'mASVSecurityPolicy' SUP Top STRUCTURAL MUST cn MAY ( description $ masvDomainPolicy $ masvPolicyUpdate $ masvClearanceNames $ masvLabelNames $ masvLabelSecrecyLevelNames $ masvLabelSecrecyCategoryNames $ masvLabelIntegrityLevelNames $ masvLabelIntegrityCategoryNames $ masvNDSAttributeLabels ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sASSecurity' X-NDS_NAME 'MASV:Security Policy' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*********.0.1 NAME 'sASLoginMethodContainer' SUP Top STRUCTURAL MUST cn MAY description X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'sASSecurity' 'Country' 'Locality' 'organizationalUnit' 'Organization' ) X-NDS_NAME 'SAS:Login Method Container' )",
            "( 2.16.840.1.113719.*********.0.4 NAME 'sASLoginPolicy' SUP Top STRUCTURAL MUST cn MAY ( description $ privateKey $ publicKey $ sASAllowNDSPasswordWindow $ sASPolicyCredentials $ sASPolicyMethods $ sASPolicyObjectVersion $ sASPolicyServiceSubtypes $ sASPolicyServices $ sASPolicyUsers $ sASLoginSequence $ sASLoginPolicyUpdate $ sasNMASProductOptions $ sasPolicyMethods $ sasPolicyServices $ sasPolicyUsers $ sasAllowNDSPasswordWindow $ sasLoginFailureDelay $ sasDefaultLoginSequence $ sasAuthorizedLoginSequences $ sasAuditConfiguration $ sasUpdateLoginInfo $ sasOTPEnabled $ sasOTPLookAheadWindow $ sasOTPDigits $ sasUpdateLoginTimeInterval $ nspmPasswordPolicyDN ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sASSecurity' X-NDS_NAME 'SAS:Login Policy' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*********.0.7 NAME 'sASNMASBaseLoginMethod' SUP Top ABSTRACT MUST cn MAY ( description $ sASLoginSecret $ sASLoginSecretKey $ sASEncryptionType $ sASLoginConfiguration $ sASLoginConfigurationKey $ sASMethodIdentifier $ sASMethodVendor $ sASVendorSupport $ sASAdvisoryMethodGrade $ sASLoginClientMethodNetWare $ sASLoginServerMethodNetWare $ sASLoginClientMethodWINNT $ sASLoginServerMethodWINNT $ sasCertificateSearchContainers $ sasNMASMethodConfigData $ sasMethodVersion $ sASLoginPolicyUpdate $ sasUnsignedMethodModules $ sasServerModuleName $ sasServerModuleEntryPointName $ sasSASLMechanismName $ sasSASLMechanismEntryPointName $ sasClientModuleName $ sasClientModuleEntryPointName $ sasLoginClientMethodSolaris $ sasLoginServerMethodSolaris $ sasLoginClientMethodLinux $ sasLoginServerMethodLinux $ sasLoginClientMethodTru64 $ sasLoginServerMethodTru64 $ sasLoginClientMethodAIX $ sasLoginServerMethodAIX $ sasLoginClientMethodHPUX $ sasLoginServerMethodHPUX $ sasLoginClientMethods390 $ sasLoginServerMethods390 $ sasLoginClientMethodLinuxX64 $ sasLoginServerMethodLinuxX64 $ sasLoginClientMethodWinX64 $ sasLoginServerMethodWinX64 $ sasLoginClientMethodSolaris64 $ sasLoginServerMethodSolaris64 $ sasLoginClientMethodSolarisi386 $ sasLoginServerMethodSolarisi386 $ sasLoginClientMethodAIX64 $ sasLoginServerMethodAIX64 ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sASLoginMethodContainer' X-NDS_NAME 'SAS:NMAS Base Login Method' )",
            "( 2.16.840.1.113719.*********.0.8 NAME 'sASNMASLoginMethod' SUP sASNMASBaseLoginMethod STRUCTURAL X-NDS_NAME 'SAS:NMAS Login Method' )",
            "( 2.16.840.1.113719.*********.0.9 NAME 'rADIUSDialAccessSystem' SUP Top STRUCTURAL MUST cn MAY ( publicKey $ privateKey $ rADIUSAgedInterval $ rADIUSClient $ rADIUSCommonNameResolution $ rADIUSConcurrentLimit $ rADIUSDASVersion $ rADIUSEnableCommonNameLogin $ rADIUSEnableDialAccess $ rADIUSInterimAcctingTimeout $ rADIUSLookupContexts $ rADIUSMaxDASHistoryRecord $ rADIUSMaximumHistoryRecord $ rADIUSPasswordPolicy $ rADIUSPrivateKey $ rADIUSProxyContext $ rADIUSProxyDomain $ rADIUSProxyTarget $ rADIUSPublicKey $ sASLoginConfiguration $ sASLoginConfigurationKey ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' ) X-NDS_NAME 'RADIUS:Dial Access System' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*********.0.10 NAME 'rADIUSProfile' SUP Top STRUCTURAL MUST cn MAY rADIUSAttributeList X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' ) X-NDS_NAME 'RADIUS:Profile' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*********.0.11 NAME 'sasPostLoginMethodContainer' SUP Top STRUCTURAL MUST cn MAY description X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sASSecurity' )",
            "( 2.16.840.1.113719.*********.0.12 NAME 'sasPostLoginMethod' SUP Top STRUCTURAL MUST cn MAY ( description $ sASLoginSecret $ sASLoginSecretKey $ sASEncryptionType $ sASLoginConfiguration $ sASLoginConfigurationKey $ sASMethodIdentifier $ sASMethodVendor $ sASVendorSupport $ sASAdvisoryMethodGrade $ sASLoginClientMethodNetWare $ sASLoginServerMethodNetWare $ sASLoginClientMethodWINNT $ sASLoginServerMethodWINNT $ sasMethodVersion $ sASLoginPolicyUpdate $ sasUnsignedMethodModules $ sasServerModuleName $ sasServerModuleEntryPointName $ sasSASLMechanismName $ sasSASLMechanismEntryPointName $ sasClientModuleName $ sasClientModuleEntryPointName $ sasLoginClientMethodSolaris $ sasLoginServerMethodSolaris $ sasLoginClientMethodLinux $ sasLoginServerMethodLinux $ sasLoginClientMethodTru64 $ sasLoginServerMethodTru64 $ sasLoginClientMethodAIX $ sasLoginServerMethodAIX $ sasLoginClientMethodHPUX $ sasLoginServerMethodHPUX $ sasLoginClientMethods390 $ sasLoginServerMethods390 $ sasLoginClientMethodLinuxX64 $ sasLoginServerMethodLinuxX64 $ sasLoginClientMethodWinX64 $ sasLoginServerMethodWinX64 $ sasLoginClientMethodSolaris64 $ sasLoginServerMethodSolaris64 $ sasLoginClientMethodSolarisi386 $ sasLoginServerMethodSolarisi386 $ sasLoginClientMethodAIX64 $ sasLoginServerMethodAIX64 ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sasPostLoginMethodContainer' )",
            "( 2.16.840.1.113719.******* NAME 'snmpGroup' SUP Top STRUCTURAL MUST cn MAY ( Version $ snmpServerList $ snmpTrapDisable $ snmpTrapInterval $ snmpTrapDescription $ snmpTrapConfig ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'domain' 'organizationalUnit' 'Organization' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*********.2 NAME 'nspmPasswordPolicyContainer' SUP Top STRUCTURAL MUST cn MAY description X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'sASSecurity' 'Country' 'domain' 'Locality' 'Organization' 'organizationalUnit' ) )",
            "( 2.16.840.1.113719.*********.3 NAME 'nspmPolicyAgent' SUP Top STRUCTURAL MUST cn MAY ( description $ nspmPolicyAgentNetWare $ nspmPolicyAgentWINNT $ nspmPolicyAgentSolaris $ nspmPolicyAgentLinux $ nspmPolicyAgentAIX $ nspmPolicyAgentHPUX ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'nspmPasswordPolicyContainer' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*********.1 NAME 'nspmPasswordPolicy' SUP Top STRUCTURAL MUST cn MAY ( description $ nspmPolicyPrecedence $ nspmConfigurationOptions $ nspmChangePasswordMessage $ passwordExpirationInterval $ loginGraceLimit $ nspmMinPasswordLifetime $ passwordUniqueRequired $ nspmPasswordHistoryLimit $ nspmPasswordHistoryExpiration $ passwordAllowChange $ passwordRequired $ passwordMinimumLength $ nspmMaximumLength $ nspmCaseSensitive $ nspmMinUpperCaseCharacters $ nspmMaxUpperCaseCharacters $ nspmMinLowerCaseCharacters $ nspmMaxLowerCaseCharacters $ nspmNumericCharactersAllowed $ nspmNumericAsFirstCharacter $ nspmNumericAsLastCharacter $ nspmMinNumericCharacters $ nspmMaxNumericCharacters $ nspmSpecialCharactersAllowed $ nspmSpecialAsFirstCharacter $ nspmSpecialAsLastCharacter $ nspmMinSpecialCharacters $ nspmMaxSpecialCharacters $ nspmMaxRepeatedCharacters $ nspmMaxConsecutiveCharacters $ nspmMinUniqueCharacters $ nspmDisallowedAttributeValues $ nspmExcludeList $ nspmExtendedCharactersAllowed $ nspmExtendedAsFirstCharacter $ nspmExtendedAsLastCharacter $ nspmMinExtendedCharacters $ nspmMaxExtendedCharacters $ nspmUpperAsFirstCharacter $ nspmUpperAsLastCharacter $ nspmLowerAsFirstCharacter $ nspmLowerAsLastCharacter $ nspmComplexityRules $ nspmAD2K8Syntax $ nspmAD2K8maxViolation $ nspmXCharLimit $ nspmXCharHistoryLimit $ nspmUnicodeAllowed $ nspmNonAlphaCharactersAllowed $ nspmMinNonAlphaCharacters $ nspmMaxNonAlphaCharacters $ pwdInHistory $ nspmAdminsDoNotExpirePassword $ nspmPasswordACL $ nsimChallengeSetDN $ nsimForgottenAction $ nsimForgottenLoginConfig $ nsimAssignments $ nsimChallengeSetGUID $ nsimPwdRuleEnforcement ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'nspmPasswordPolicyContainer' 'domain' 'Locality' 'Organization' 'organizationalUnit' 'Country' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*********.4 NAME 'nspmPasswordAux' AUXILIARY MAY ( publicKey $ privateKey $ loginGraceLimit $ loginGraceRemaining $ passwordExpirationTime $ passwordRequired $ nspmPasswordKey $ nspmPassword $ nspmDistributionPassword $ nspmPreviousDistributionPassword $ nspmPasswordHistory $ nspmAdministratorChangeCount $ nspmPasswordPolicyDN $ pwdChangedTime $ pwdAccountLockedTime $ pwdFailureTime $ nspmDoNotExpirePassword ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.0 NAME 'auditFileObject' SUP Top STRUCTURAL MUST ( cn $ auditPolicy $ auditContents ) MAY ( description $ auditPath $ auditLinkList $ auditType $ auditCurrentEncryptionKey $ auditAEncryptionKey $ auditBEncryptionKey ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Top' 'Country' 'Locality' 'Organization' 'organizationalUnit' 'treeRoot' 'domain' ) X-NDS_NAME 'Audit:File Object' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.4 NAME 'wANMANLANArea' SUP Top STRUCTURAL MUST cn MAY ( description $ l $ member $ o $ ou $ owner $ seeAlso $ wANMANWANPolicy $ wANMANCost $ wANMANDefaultCost ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'Organization' 'organizationalUnit' ) X-NDS_NAME 'WANMAN:LAN Area' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsCollection' SUP Top STRUCTURAL MUST cn MAY ( owner $ description $ rbsXMLInfo ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' 'domain' ) )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsExternalScope' SUP Top ABSTRACT MUST cn MAY ( rbsURL $ description $ rbsXMLInfo ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'rbsCollection' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsModule' SUP Top STRUCTURAL MUST cn MAY ( rbsURL $ rbsPath $ rbsType $ description $ rbsXMLInfo ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'rbsCollection' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsRole' SUP Top STRUCTURAL MUST cn MAY ( rbsContent $ rbsMember $ rbsTrusteeOf $ rbsGALabel $ rbsParameters $ description $ rbsXMLInfo ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'rbsCollection' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsTask' SUP Top STRUCTURAL MUST cn MAY ( rbsContentMembership $ rbsType $ rbsTaskRights $ rbsEntryPoint $ rbsParameters $ rbsTaskTemplates $ rbsTaskTemplatesURL $ description $ rbsXMLInfo ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'rbsModule' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsBook' SUP rbsTask STRUCTURAL MAY ( rbsTargetObjectType $ rbsPageMembership ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsScope' SUP groupOfNames STRUCTURAL MAY ( rbsContext $ rbsXMLInfo ) X-NDS_CONTAINMENT 'rbsRole' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsCollection2' SUP Top STRUCTURAL MUST cn MAY ( rbsXMLInfo $ rbsParameters $ owner $ description ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' 'domain' ) )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsExternalScope2' SUP Top ABSTRACT MUST cn MAY ( rbsXMLInfo $ description ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'rbsCollection2' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsModule2' SUP Top STRUCTURAL MUST cn MAY ( rbsXMLInfo $ rbsPath $ rbsType $ description ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'rbsCollection2' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsRole2' SUP Top STRUCTURAL MUST cn MAY ( rbsXMLInfo $ rbsContent $ rbsMember $ rbsTrusteeOf $ rbsParameters $ description ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'rbsCollection2' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsTask2' SUP Top STRUCTURAL MUST cn MAY ( rbsXMLInfo $ rbsContentMembership $ rbsType $ rbsTaskRights $ rbsEntryPoint $ rbsParameters $ description ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'rbsModule2' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsBook2' SUP rbsTask2 STRUCTURAL MAY ( rbsTargetObjectType $ rbsPageMembership ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.**********.1 NAME 'rbsScope2' SUP groupOfNames STRUCTURAL MAY ( rbsContext $ rbsXMLInfo ) X-NDS_CONTAINMENT 'rbsRole2' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*******.49 NAME 'prSyncPolicy' SUP Top STRUCTURAL MUST cn MAY prSyncAttributes X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'domain' 'Country' 'Locality' 'organizationalUnit' 'Organization' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*******.50 NAME 'encryptionPolicy' SUP Top STRUCTURAL MUST cn MAY ( attrEncryptionDefinition $ attrEncryptionRequiresSecure ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'domain' 'organizationalUnit' 'Organization' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.5 NAME 'ndspkiContainer' SUP Top STRUCTURAL MUST cn X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'ndspkiContainer' 'sASSecurity' 'Organization' 'organizationalUnit' 'Country' 'Locality' 'nDSPKITrustedRoot' ) )",
            "( 2.16.840.1.113719.********.6 NAME 'ndspkiCertificate' SUP Top STRUCTURAL MUST ( cn $ userCertificate ) MAY ( nDSPKISubjectName $ nDSPKINotBefore $ nDSPKINotAfter $ externalName $ givenName $ sn ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'sASSecurity' 'Organization' 'organizationalUnit' 'Country' 'Locality' 'ndspkiContainer' 'nDSPKITrustedRoot' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.********.7 NAME 'ndspkiCRLConfiguration' SUP Top STRUCTURAL MUST cn MAY ( ndspkiCRLFileName $ ndspkiDirectory $ ndspkiStatus $ ndspkiIssueTime $ ndspkiNextIssueTime $ ndspkiAttemptTime $ ndspkiTimeInterval $ ndspkiCRLMaxProcessingInterval $ ndspkiCRLNumber $ ndspkiDistributionPoints $ ndspkiDistributionPointDN $ ndspkiCADN $ ndspkiCRLProcessData $ nDSPKIPublicKey $ nDSPKIPrivateKey $ nDSPKIPublicKeyCertificate $ nDSPKICertificateChain $ nDSPKIParentCA $ nDSPKIParentCADN $ nDSPKISubjectName $ cACertificate $ hostServer $ ndspkiCRLType $ ndspkiCRLExtendValidity ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'ndspkiContainer' )",
            "( ******** NAME 'cRLDistributionPoint' SUP Top STRUCTURAL MUST cn MAY ( authorityRevocationList $ authorityRevocationList $ cACertificate $ certificateRevocationList $ certificateRevocationList $ crossCertificatePair $ deltaRevocationList $ deltaRevocationList $ ndspkiCRLConfigurationDN ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'Country' 'Locality' 'organizationalUnit' 'Organization' 'sASSecurity' 'domain' 'ndspkiCRLConfiguration' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.******* NAME 'notfTemplateCollection' SUP Top STRUCTURAL MUST cn MAY ( notfSMTPEmailHost $ notfSMTPEmailFrom $ notfSMTPEmailUserName $ sASSecretStore ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sASSecurity' )",
            "( 2.16.840.1.113719.******* NAME 'notfMergeTemplate' SUP Top STRUCTURAL MUST cn MAY ( notfMergeTemplateData $ notfMergeTemplateSubject ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'notfTemplateCollection' X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*********.1 NAME 'nsimChallengeSet' SUP Top STRUCTURAL MUST cn MAY ( description $ nsimRequiredQuestions $ nsimRandomQuestions $ nsimNumberRandomQuestions $ nsimMinResponseLength $ nsimMaxResponseLength ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'nspmPasswordPolicyContainer' 'Country' 'domain' 'Locality' 'Organization' 'organizationalUnit' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.1.266.6.1 NAME 'sssServerPolicies' SUP Top STRUCTURAL MUST cn MAY ( sssCacheRefreshInterval $ sssEnableReadTimestamps $ sssDisableMasterPasswords $ sssEnableAdminAccess $ sssAdminList $ sssAdminGALabel $ sssReadSecretPolicies ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT 'sASSecurity' )",
            "( 2.16.840.1.113719.1.266.6.2 NAME 'sssServerPolicyOverride' SUP Top STRUCTURAL MUST cn MAY ( sssCacheRefreshInterval $ sssEnableReadTimestamps $ sssDisableMasterPasswords $ sssEnableAdminAccess $ sssAdminList $ sssAdminGALabel $ sssReadSecretPolicies ) X-NDS_NAMING 'cn' X-NDS_CONTAINMENT ( 'sssServerPolicies' 'Organization' 'organizationalUnit' 'Country' 'Locality' 'domain' ) X-NDS_NOT_CONTAINER '1' )",
            "( 2.16.840.1.113719.*******.91 NAME 'nestedGroupAux' AUXILIARY MAY ( groupMember $ excludedMember $ nestedConfig $ groupMembership ) X-NDS_NOT_CONTAINER '1' )"
        ]
    },
    "schema_entry": "cn=schema",
    "type": "SchemaInfo"
}
"""

edir_9_1_4_dsa_info = """
{
    "raw": {
        "abandonOps": [
            "0"
        ],
        "addEntryOps": [
            "0"
        ],
        "altServer": [],
        "bindSecurityErrors": [
            "0"
        ],
        "chainings": [
            "0"
        ],
        "compareOps": [
            "0"
        ],
        "directoryTreeName": [
            "TEST_TREE"
        ],
        "dsaName": [
            "cn=MYSERVER,o=resources"
        ],
        "errors": [
            "0"
        ],
        "extendedOps": [
            "0"
        ],
        "inBytes": [
            "293"
        ],
        "inOps": [
            "3"
        ],
        "listOps": [
            "0"
        ],
        "modifyEntryOps": [
            "0"
        ],
        "modifyRDNOps": [
            "0"
        ],
        "namingContexts": [
            ""
        ],
        "oneLevelSearchOps": [
            "0"
        ],
        "outBytes": [
            "14"
        ],
        "readOps": [
            "1"
        ],
        "referralsReturned": [
            "0"
        ],
        "removeEntryOps": [
            "0"
        ],
        "repUpdatesIn": [
            "0"
        ],
        "repUpdatesOut": [
            "0"
        ],
        "searchOps": [
            "1"
        ],
        "securityErrors": [
            "0"
        ],
        "simpleAuthBinds": [
            "1"
        ],
        "strongAuthBinds": [
            "0"
        ],
        "subschemaSubentry": [
            "cn=schema"
        ],
        "supportedCapabilities": [],
        "supportedControl": [
            "2.16.840.1.113719.1.27.101.6",
            "2.16.840.1.113719.1.27.101.5",
            "1.2.840.113556.1.4.319",
            "2.16.840.1.113730.3.4.3",
            "2.16.840.1.113730.3.4.2",
            "2.16.840.1.113719.1.27.101.57",
            "2.16.840.1.113719.1.27.103.7",
            "2.16.840.1.113719.1.27.101.40",
            "2.16.840.1.113719.1.27.101.41",
            "1.2.840.113556.1.4.1413",
            "1.2.840.113556.1.4.805",
            "2.16.840.1.113730.3.4.18",
            "1.2.840.113556.1.4.529"
        ],
        "supportedExtension": [
            "2.16.840.1.113719.1.148.100.1",
            "2.16.840.1.113719.1.148.100.3",
            "2.16.840.1.113719.1.148.100.5",
            "2.16.840.1.113719.1.148.100.7",
            "2.16.840.1.113719.1.148.100.9",
            "2.16.840.1.113719.1.148.100.11",
            "2.16.840.1.113719.1.148.100.13",
            "2.16.840.1.113719.1.148.100.15",
            "2.16.840.1.113719.1.148.100.17",
            "2.16.840.1.113719.1.39.42.100.1",
            "2.16.840.1.113719.1.39.42.100.3",
            "2.16.840.1.113719.1.39.42.100.5",
            "2.16.840.1.113719.1.39.42.100.7",
            "2.16.840.1.113719.1.39.42.100.9",
            "2.16.840.1.113719.1.39.42.100.11",
            "2.16.840.1.113719.1.39.42.100.13",
            "2.16.840.1.113719.1.39.42.100.15",
            "2.16.840.1.113719.1.39.42.100.17",
            "2.16.840.1.113719.1.39.42.100.19",
            "2.16.840.1.113719.1.39.42.100.21",
            "2.16.840.1.113719.1.39.42.100.23",
            "2.16.840.1.113719.1.39.42.100.25",
            "2.16.840.1.113719.1.39.42.100.27",
            "2.16.840.1.113719.1.39.42.100.29",
            "*******.4.1.4203.1.11.1",
            "2.16.840.1.113719.1.27.100.1",
            "2.16.840.1.113719.1.27.100.3",
            "2.16.840.1.113719.1.27.100.5",
            "2.16.840.1.113719.1.27.100.7",
            "2.16.840.1.113719.1.27.100.11",
            "2.16.840.1.113719.1.27.100.13",
            "2.16.840.1.113719.1.27.100.15",
            "2.16.840.1.113719.1.27.100.17",
            "2.16.840.1.113719.1.27.100.19",
            "2.16.840.1.113719.1.27.100.21",
            "2.16.840.1.113719.1.27.100.23",
            "2.16.840.1.113719.1.27.100.25",
            "2.16.840.1.113719.1.27.100.27",
            "2.16.840.1.113719.1.27.100.29",
            "2.16.840.1.113719.1.27.100.31",
            "2.16.840.1.113719.1.27.100.33",
            "2.16.840.1.113719.1.27.100.35",
            "2.16.840.1.113719.1.27.100.37",
            "2.16.840.1.113719.1.27.100.39",
            "2.16.840.1.113719.1.27.100.41",
            "2.16.840.1.113719.1.27.100.96",
            "2.16.840.1.113719.1.27.100.98",
            "2.16.840.1.113719.1.27.100.101",
            "2.16.840.1.113719.1.27.100.103",
            "2.16.840.1.113719.1.142.100.1",
            "2.16.840.1.113719.1.142.100.4",
            "2.16.840.1.113719.1.142.100.6",
            "2.16.840.1.113719.1.27.100.9",
            "2.16.840.1.113719.1.27.100.43",
            "2.16.840.1.113719.1.27.100.45",
            "2.16.840.1.113719.1.27.100.47",
            "2.16.840.1.113719.1.27.100.49",
            "2.16.840.1.113719.1.27.100.51",
            "2.16.840.1.113719.1.27.100.53",
            "2.16.840.1.113719.1.27.100.55",
            "*******.4.1.1466.20037",
            "2.16.840.1.113719.1.27.100.79",
            "2.16.840.1.113719.1.27.100.84",
            "2.16.840.1.113719.1.27.103.1",
            "2.16.840.1.113719.1.27.103.2"
        ],
        "supportedFeatures": [
            "*******.4.1.4203.1.5.1",
            "2.16.840.1.113719.1.27.99.1"
        ],
        "supportedGroupingTypes": [
            "2.16.840.1.113719.1.27.103.8"
        ],
        "supportedLDAPVersion": [
            "2",
            "3"
        ],
        "supportedSASLMechanisms": [
            "NMAS_LOGIN"
        ],
        "unAuthBinds": [
            "0"
        ],
        "vendorName": [
            "NetIQ Corporation"
        ],
        "vendorVersion": [
            "LDAP Agent for NetIQ eDirectory 9.1.4 (40105.09)"
        ],
        "wholeSubtreeSearchOps": [
            "0"
        ]
    },
    "type": "DsaInfo"
}
"""