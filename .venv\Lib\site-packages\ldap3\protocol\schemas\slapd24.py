"""
"""

# Created on 2014.10.21
#
# Author: <PERSON>
#
# Copyright 2014 - 2020 <PERSON>
#
# This file is part of ldap3.
#
# ldap3 is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published
# by the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# ldap3 is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with ldap3 in the COPYING and COPYING.LESSER files.
# If not, see <http://www.gnu.org/licenses/>.

slapd_2_4_schema = """
{
    "raw": {
        "attributeTypes": [
            "( 2.5.4.0 NAME 'objectClass' DESC 'RFC4512: object classes of the entity' EQUALITY objectIdentifierMatch SYNTAX *******.4.1.1466.************ )",
            "( 2.5.21.9 NAME 'structuralObjectClass' DESC 'RFC4512: structural object class of entry' EQUALITY objectIdentifierMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.18.1 NAME 'createTimestamp' DESC 'RFC4512: time which object was created' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.1466.115.121.1.24 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.18.2 NAME 'modifyTimestamp' DESC 'RFC4512: time which object was last modified' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.1466.115.121.1.24 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.18.3 NAME 'creatorsName' DESC 'RFC4512: name of creator' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.18.4 NAME 'modifiersName' DESC 'RFC4512: name of last modifier' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.18.9 NAME 'hasSubordinates' DESC 'X.501: entry has children' EQUALITY booleanMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( 2.5.18.10 NAME 'subschemaSubentry' DESC 'RFC4512: name of controlling subschema entry' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.1.20 NAME 'entryDN' DESC 'DN of the entry' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.1.16.4 NAME 'entryUUID' DESC 'UUID of the entry' EQUALITY UUIDMatch ORDERING UUIDOrderingMatch SYNTAX *******.1.16.1 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.4.1.1466.101.120.6 NAME 'altServer' DESC 'RFC4512: alternative servers' SYNTAX *******.4.1.1466.************ USAGE dSAOperation )",
            "( *******.4.1.1466.101.120.5 NAME 'namingContexts' DESC 'RFC4512: naming contexts' SYNTAX *******.4.1.1466.************ USAGE dSAOperation )",
            "( *******.4.1.1466.101.120.13 NAME 'supportedControl' DESC 'RFC4512: supported controls' SYNTAX *******.4.1.1466.************ USAGE dSAOperation )",
            "( *******.4.1.1466.101.120.7 NAME 'supportedExtension' DESC 'RFC4512: supported extended operations' SYNTAX *******.4.1.1466.************ USAGE dSAOperation )",
            "( *******.4.1.1466.101.120.15 NAME 'supportedLDAPVersion' DESC 'RFC4512: supported LDAP versions' SYNTAX *******.4.1.1466.************ USAGE dSAOperation )",
            "( *******.4.1.1466.101.120.14 NAME 'supportedSASLMechanisms' DESC 'RFC4512: supported SASL mechanisms' SYNTAX *******.4.1.1466.11********** USAGE dSAOperation )",
            "( *******.4.1.4203.1.3.5 NAME 'supportedFeatures' DESC 'RFC4512: features supported by the server' EQUALITY objectIdentifierMatch SYNTAX *******.4.1.1466.************ USAGE dSAOperation )",
            "( *******.1.4 NAME 'vendorName' DESC 'RFC3045: name of implementation vendor' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE NO-USER-MODIFICATION USAGE dSAOperation )",
            "( *******.1.5 NAME 'vendorVersion' DESC 'RFC3045: version of implementation' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE NO-USER-MODIFICATION USAGE dSAOperation )",
            "( 2.5.21.4 NAME 'matchingRules' DESC 'RFC4512: matching rules' EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.115.121.1.30 USAGE directoryOperation )",
            "( 2.5.21.5 NAME 'attributeTypes' DESC 'RFC4512: attribute types' EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.115.121.1.3 USAGE directoryOperation )",
            "( 2.5.21.6 NAME 'objectClasses' DESC 'RFC4512: object classes' EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.115.121.1.37 USAGE directoryOperation )",
            "( 2.5.21.8 NAME 'matchingRuleUse' DESC 'RFC4512: matching rule uses' EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.115.121.1.31 USAGE directoryOperation )",
            "( *******.4.1.1466.101.120.16 NAME 'ldapSyntaxes' DESC 'RFC4512: LDAP syntaxes' EQUALITY objectIdentifierFirstComponentMatch SYNTAX *******.4.1.1466.115.121.1.54 USAGE directoryOperation )",
            "( 2.5.4.1 NAME ( 'aliasedObjectName' 'aliasedEntryName' ) DESC 'RFC4512: name of aliased object' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 2.16.840.1.113730.3.1.34 NAME 'ref' DESC 'RFC3296: subordinate referral URL' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.11********** USAGE distributedOperation )",
            "( *******.4.1.1466.101.119.3 NAME 'entryTtl' DESC 'RFC2589: entry time-to-live' SYNTAX *******.4.1.1466.************ SINGLE-VALUE NO-USER-MODIFICATION USAGE dSAOperation )",
            "( *******.4.1.1466.101.119.4 NAME 'dynamicSubtrees' DESC 'RFC2589: dynamic subtrees' SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE dSAOperation )",
            "( 2.5.4.49 NAME 'distinguishedName' DESC 'RFC4519: common supertype of DN attributes' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )",
            "( 2.5.4.41 NAME 'name' DESC 'RFC4519: common supertype of name attributes' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{32768} )",
            "( 2.5.4.3 NAME ( 'cn' 'commonName' ) DESC 'RFC4519: common name(s) for which the entity is known by' SUP name )",
            "( 0.9.2342.********.100.1.1 NAME ( 'uid' 'userid' ) DESC 'RFC4519: user identifier' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( *******.1.1.1.0 NAME 'uidNumber' DESC 'RFC2307: An integer uniquely identifying a user in an administrative domain' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.1 NAME 'gidNumber' DESC 'RFC2307: An integer uniquely identifying a group in an administrative domain' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( ******** NAME 'userPassword' DESC 'RFC4519/2307: password of user' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************{128} )",
            "( *******.*********.57 NAME 'labeledURI' DESC 'RFC2079: Uniform Resource Identifier with optional label' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.11********** )",
            "( ******** NAME 'description' DESC 'RFC4519: descriptive information' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{1024} )",
            "( ******** NAME 'seeAlso' DESC 'RFC4519: DN of related object' SUP distinguishedName )",
            "( *******.4.1.4203.********.0.78 NAME 'olcConfigFile' DESC 'File for slapd configuration directives' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.79 NAME 'olcConfigDir' DESC 'Directory for slapd configuration backend' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.1 NAME 'olcAccess' DESC 'Access Control List' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.86 NAME 'olcAddContentAcl' DESC 'Check ACLs against content of Add ops' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.2 NAME 'olcAllows' DESC 'Allowed set of deprecated features' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.3 NAME 'olcArgsFile' DESC 'File for slapd command line options' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.5 NAME 'olcAttributeOptions' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.4 NAME 'olcAttributeTypes' DESC 'OpenLDAP attributeTypes' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.6 NAME 'olcAuthIDRewrite' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.7 NAME 'olcAuthzPolicy' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.8 NAME 'olcAuthzRegexp' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.9 NAME 'olcBackend' DESC 'A type of backend' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE X-ORDERED 'SIBLINGS' )",
            "( *******.4.1.4203.********.0.10 NAME 'olcConcurrency' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.11 NAME 'olcConnMaxPending' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.12 NAME 'olcConnMaxPendingAuth' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.13 NAME 'olcDatabase' DESC 'The backend type for a database instance' SUP olcBackend SINGLE-VALUE X-ORDERED 'SIBLINGS' )",
            "( *******.4.1.4203.********.0.14 NAME 'olcDefaultSearchBase' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.15 NAME 'olcDisallows' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.16 NAME 'olcDitContentRules' DESC 'OpenLDAP DIT content rules' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.2.0.20 NAME 'olcExtraAttrs' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.17 NAME 'olcGentleHUP' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.17 NAME 'olcHidden' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.18 NAME 'olcIdleTimeout' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.19 NAME 'olcInclude' SUP labeledURI )",
            "( *******.4.1.4203.********.0.20 NAME 'olcIndexSubstrIfMinLen' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.21 NAME 'olcIndexSubstrIfMaxLen' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.22 NAME 'olcIndexSubstrAnyLen' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.23 NAME 'olcIndexSubstrAnyStep' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.84 NAME 'olcIndexIntLen' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.4 NAME 'olcLastMod' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.85 NAME 'olcLdapSyntaxes' DESC 'OpenLDAP ldapSyntax' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.2.0.5 NAME 'olcLimits' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.93 NAME 'olcListenerThreads' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.26 NAME 'olcLocalSSF' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.27 NAME 'olcLogFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.28 NAME 'olcLogLevel' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.2.0.6 NAME 'olcMaxDerefDepth' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.16 NAME 'olcMirrorMode' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.30 NAME 'olcModuleLoad' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.31 NAME 'olcModulePath' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.18 NAME 'olcMonitoring' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.32 NAME 'olcObjectClasses' DESC 'OpenLDAP object classes' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.33 NAME 'olcObjectIdentifier' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.34 NAME 'olcOverlay' SUP olcDatabase SINGLE-VALUE X-ORDERED 'SIBLINGS' )",
            "( *******.4.1.4203.********.0.35 NAME 'olcPasswordCryptSaltFormat' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.36 NAME 'olcPasswordHash' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.37 NAME 'olcPidFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.38 NAME 'olcPlugin' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.39 NAME 'olcPluginLogFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.40 NAME 'olcReadOnly' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.41 NAME 'olcReferral' SUP labeledURI SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.7 NAME 'olcReplica' SUP labeledURI EQUALITY caseIgnoreMatch X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.43 NAME 'olcReplicaArgsFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.44 NAME 'olcReplicaPidFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.45 NAME 'olcReplicationInterval' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.46 NAME 'olcReplogFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.47 NAME 'olcRequires' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.48 NAME 'olcRestrict' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.49 NAME 'olcReverseLookup' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.8 NAME 'olcRootDN' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.51 NAME 'olcRootDSE' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.2.0.9 NAME 'olcRootPW' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.89 NAME 'olcSaslAuxprops' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.53 NAME 'olcSaslHost' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.54 NAME 'olcSaslRealm' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.56 NAME 'olcSaslSecProps' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.58 NAME 'olcSchemaDN' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.59 NAME 'olcSecurity' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.81 NAME 'olcServerID' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.60 NAME 'olcSizeLimit' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.61 NAME 'olcSockbufMaxIncoming' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.62 NAME 'olcSockbufMaxIncomingAuth' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.83 NAME 'olcSortVals' DESC 'Attributes whose values will always be sorted' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.2.0.15 NAME 'olcSubordinate' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.10 NAME 'olcSuffix' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.4203.********.2.0.19 NAME 'olcSyncUseSubentry' DESC 'Store sync context in a subentry' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.11 NAME 'olcSyncrepl' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.0.90 NAME 'olcTCPBuffer' DESC 'Custom TCP buffer size' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.66 NAME 'olcThreads' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.67 NAME 'olcTimeLimit' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.0.68 NAME 'olcTLSCACertificateFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.69 NAME 'olcTLSCACertificatePath' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.70 NAME 'olcTLSCertificateFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.71 NAME 'olcTLSCertificateKeyFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.72 NAME 'olcTLSCipherSuite' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.73 NAME 'olcTLSCRLCheck' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.82 NAME 'olcTLSCRLFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.74 NAME 'olcTLSRandFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.75 NAME 'olcTLSVerifyClient' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.77 NAME 'olcTLSDHParamFile' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.87 NAME 'olcTLSProtocolMin' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.0.80 NAME 'olcToolThreads' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.12 NAME 'olcUpdateDN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.13 NAME 'olcUpdateRef' SUP labeledURI EQUALITY caseIgnoreMatch )",
            "( *******.4.1.4203.********.0.88 NAME 'olcWriteTimeout' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.1 NAME 'olcDbDirectory' DESC 'Directory for database content' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.11 NAME 'olcDbCacheFree' DESC 'Number of extra entries to free when max is reached' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.1 NAME 'olcDbCacheSize' DESC 'Entry cache size in entries' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.2 NAME 'olcDbCheckpoint' DESC 'Database checkpoint interval in kbytes and minutes' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.16 NAME 'olcDbChecksum' DESC 'Enable database checksum validation' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.13 NAME 'olcDbCryptFile' DESC 'Pathname of file containing the DB encryption key' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.14 NAME 'olcDbCryptKey' DESC 'DB encryption key' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.3 NAME 'olcDbConfig' DESC 'BerkeleyDB DB_CONFIG configuration directives' SYNTAX *******.4.1.1466.************ X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.2.1.4 NAME 'olcDbNoSync' DESC 'Disable synchronous database writes' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.15 NAME 'olcDbPageSize' DESC 'Page size of specified DB, in Kbytes' EQUALITY caseExactMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.2.1.5 NAME 'olcDbDirtyRead' DESC 'Allow reads of uncommitted data' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.12 NAME 'olcDbDNcacheSize' DESC 'DN cache size' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.6 NAME 'olcDbIDLcacheSize' DESC 'IDL cache size in IDLs' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.2 NAME 'olcDbIndex' DESC 'Attribute index parameters' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.2.1.7 NAME 'olcDbLinearIndex' DESC 'Index attributes one at a time' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.8 NAME 'olcDbLockDetect' DESC 'Deadlock detection algorithm' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.3 NAME 'olcDbMode' DESC 'Unix permissions of database files' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.9 NAME 'olcDbSearchStack' DESC 'Depth of search stack in IDLs' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.1.10 NAME 'olcDbShmKey' DESC 'Key for shared memory region' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.0.14 NAME 'olcDbURI' DESC 'URI (list) for remote DSA' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.1 NAME 'olcDbStartTLS' DESC 'StartTLS' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.2 NAME 'olcDbACLAuthcDn' DESC 'Remote ACL administrative identity' OBSOLETE SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.3 NAME 'olcDbACLPasswd' DESC 'Remote ACL administrative identity credentials' OBSOLETE SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.4 NAME 'olcDbACLBind' DESC 'Remote ACL administrative identity auth bind configuration' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.5 NAME 'olcDbIDAssertAuthcDn' DESC 'Remote Identity Assertion administrative identity' OBSOLETE SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.6 NAME 'olcDbIDAssertPasswd' DESC 'Remote Identity Assertion administrative identity credentials' OBSOLETE SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.7 NAME 'olcDbIDAssertBind' DESC 'Remote Identity Assertion administrative identity auth bind configuration' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.8 NAME 'olcDbIDAssertMode' DESC 'Remote Identity Assertion mode' OBSOLETE SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.9 NAME 'olcDbIDAssertAuthzFrom' DESC 'Remote Identity Assertion authz rules' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.2.3.10 NAME 'olcDbRebindAsUser' DESC 'Rebind as user' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.11 NAME 'olcDbChaseReferrals' DESC 'Chase referrals' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.12 NAME 'olcDbTFSupport' DESC 'Absolute filters support' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.13 NAME 'olcDbProxyWhoAmI' DESC 'Proxy whoAmI exop' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.14 NAME 'olcDbTimeout' DESC 'Per-operation timeouts' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.15 NAME 'olcDbIdleTimeout' DESC 'connection idle timeout' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.16 NAME 'olcDbConnTtl' DESC 'connection ttl' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.17 NAME 'olcDbNetworkTimeout' DESC 'connection network timeout' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.18 NAME 'olcDbProtocolVersion' DESC 'protocol version' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.19 NAME 'olcDbSingleConn' DESC 'cache a single connection per identity' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.20 NAME 'olcDbCancel' DESC 'abandon/ignore/exop operations when appropriate' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.21 NAME 'olcDbQuarantine' DESC 'Quarantine database if connection fails and retry according to rule' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.22 NAME 'olcDbUseTemporaryConn' DESC 'Use temporary connections if the cached one is busy' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.23 NAME 'olcDbConnectionPoolMax' DESC 'Max size of privileged connections pool' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.25 NAME 'olcDbNoRefs' DESC 'Do not return search reference responses' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.26 NAME 'olcDbNoUndefFilter' DESC 'Do not propagate undefined search filters' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.3.27 NAME 'olcDbIDAssertPassThru' DESC 'Remote Identity Assertion passthru rules' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.3.3.1 NAME 'olcChainingBehavior' DESC 'Chaining behavior control parameters (draft-sermersheim-ldap-chaining)' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.3.2 NAME 'olcChainCacheURI' DESC 'Enables caching of URIs not present in configuration' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.3.3 NAME 'olcChainMaxReferralDepth' DESC 'max referral depth' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.3.4 NAME 'olcChainReturnError' DESC 'Errors are returned instead of the original referral' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.2.5.1 NAME 'olcRelay' DESC 'Relay DN' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.4.1 NAME 'olcAccessLogDB' DESC 'Suffix of database for log content' SUP distinguishedName SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.4.2 NAME 'olcAccessLogOps' DESC 'Operation types to log' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.4.3 NAME 'olcAccessLogPurge' DESC 'Log cleanup parameters' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.4.4 NAME 'olcAccessLogSuccess' DESC 'Log successful ops only' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.4.5 NAME 'olcAccessLogOld' DESC 'Log old values when modifying entries matching the filter' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.4.6 NAME 'olcAccessLogOldAttr' DESC 'Log old values of these attributes even if unmodified' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.4.7 NAME 'olcAccessLogBase' DESC 'Operation types to log under a specific branch' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.15.1 NAME 'olcAuditlogFile' DESC 'Filename for auditlogging' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.19.1 NAME 'olcCollectInfo' DESC 'DN of entry and attribute to distribute' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.13.1 NAME 'olcConstraintAttribute' DESC 'constraint for list of attributes' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.9.1 NAME 'olcDDSstate' DESC 'RFC2589 Dynamic directory services state' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.9.2 NAME 'olcDDSmaxTtl' DESC 'RFC2589 Dynamic directory services max TTL' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.9.3 NAME 'olcDDSminTtl' DESC 'RFC2589 Dynamic directory services min TTL' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.9.4 NAME 'olcDDSdefaultTtl' DESC 'RFC2589 Dynamic directory services default TTL' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.9.5 NAME 'olcDDSinterval' DESC 'RFC2589 Dynamic directory services expiration task run interval' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.9.6 NAME 'olcDDStolerance' DESC 'RFC2589 Dynamic directory services additional TTL in expiration scheduling' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.9.7 NAME 'olcDDSmaxDynamicObjects' DESC 'RFC2589 Dynamic directory services max number of dynamic objects' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.17.1 NAME 'olcDGAttrPair' DESC 'Member and MemberURL attribute pair' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.8.1 NAME 'olcDlAttrSet' DESC 'Dynamic list: <group objectClass>, <URL attributeDescription>, <member attributeDescription>' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( 1.2.840.113556.1.2.102 NAME 'memberOf' DESC 'Group that the entry belongs to' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ USAGE dSAOperation X-ORIGIN 'iPlanet Delegated Administrator' )",
            "( *******.4.1.4203.********.3.18.0 NAME 'olcMemberOfDN' DESC 'DN to be used as modifiersName' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.18.1 NAME 'olcMemberOfDangling' DESC 'Behavior with respect to dangling members, constrained to ignore, drop, error' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.18.2 NAME 'olcMemberOfRefInt' DESC 'Take care of referential integrity' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.18.3 NAME 'olcMemberOfGroupOC' DESC 'Group objectClass' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.18.4 NAME 'olcMemberOfMemberAD' DESC 'member attribute' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.18.5 NAME 'olcMemberOfMemberOfAD' DESC 'memberOf attribute' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.18.7 NAME 'olcMemberOfDanglingError' DESC 'Error code returned in case of dangling back reference' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.42.2.27.8.1.16 NAME 'pwdChangedTime' DESC 'The time the password was last changed' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.1466.115.121.1.24 SINGLE-VALUE NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.4.1.42.2.27.8.1.17 NAME 'pwdAccountLockedTime' DESC 'The time an user account was locked' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.1466.115.121.1.24 SINGLE-VALUE USAGE directoryOperation )",
            "( *******.4.1.42.2.27.8.1.19 NAME 'pwdFailureTime' DESC 'The timestamps of the last consecutive authentication failures' EQUALITY generalizedTimeMatch ORDERING generalizedTimeOrderingMatch SYNTAX *******.4.1.1466.115.121.1.24 NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.4.1.42.2.27.8.1.20 NAME 'pwdHistory' DESC 'The history of users passwords' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.4.1.42.2.27.8.1.21 NAME 'pwdGraceUseTime' DESC 'The timestamps of the grace login once the password has expired' EQUALITY generalizedTimeMatch SYNTAX *******.4.1.1466.115.121.1.24 NO-USER-MODIFICATION USAGE directoryOperation )",
            "( *******.4.1.42.2.27.8.1.22 NAME 'pwdReset' DESC 'The indication that the password has been reset' EQUALITY booleanMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE USAGE directoryOperation )",
            "( *******.4.1.42.2.27.8.1.23 NAME 'pwdPolicySubentry' DESC 'The pwdPolicy subentry in effect for this object' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE USAGE directoryOperation )",
            "( *******.4.1.4203.********.3.12.1 NAME 'olcPPolicyDefault' DESC 'DN of a pwdPolicy object for uncustomized objects' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.12.2 NAME 'olcPPolicyHashCleartext' DESC 'Hash passwords on add or modify' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.12.4 NAME 'olcPPolicyForwardUpdates' DESC 'Allow policy state updates to be forwarded via updateref' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.12.3 NAME 'olcPPolicyUseLockout' DESC 'Warn clients with AccountLocked' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.2.1 NAME ( 'olcPcache' 'olcProxyCache' ) DESC 'Proxy Cache basic parameters' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.2.2 NAME ( 'olcPcacheAttrset' 'olcProxyAttrset' ) DESC 'A set of attributes to cache' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.2.3 NAME ( 'olcPcacheTemplate' 'olcProxyCacheTemplate' ) DESC 'Filter template, attrset, cache TTL, optional negative TTL, optional sizelimit TTL, optional TTR' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.2.4 NAME 'olcPcachePosition' DESC 'Response callback position in overlay stack' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.2.5 NAME ( 'olcPcacheMaxQueries' 'olcProxyCacheQueries' ) DESC 'Maximum number of queries to cache' SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.4203.********.3.2.6 NAME ( 'olcPcachePersist' 'olcProxySaveQueries' ) DESC 'Save cached queries for hot restart' SYNTAX *******.4.1.1466.*********** )",
            "( *******.4.1.4203.********.3.2.7 NAME ( 'olcPcacheValidate' 'olcProxyCheckCacheability' ) DESC 'Check whether the results of a query are cacheable, e.g. for schema issues' SYNTAX *******.4.1.1466.*********** )",
            "( *******.4.1.4203.********.3.2.8 NAME 'olcPcacheOffline' DESC 'Set cache to offline mode and disable expiration' SYNTAX *******.4.1.1466.*********** )",
            "( *******.4.1.4203.********.3.2.9 NAME 'olcPcacheBind' DESC 'Parameters for caching Binds' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.11.1 NAME 'olcRefintAttribute' DESC 'Attributes for referential integrity' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.11.2 NAME 'olcRefintNothing' DESC 'Replacement DN to supply when needed' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.11.3 NAME 'olcRefintModifiersName' DESC 'The DN to use as modifiersName' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.20.1 NAME 'olcRetcodeParent' DESC '' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.20.2 NAME 'olcRetcodeItem' DESC '' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.3.20.3 NAME 'olcRetcodeInDir' DESC '' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.20.4 NAME 'olcRetcodeSleep' DESC '' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.16.1 NAME 'olcRwmRewrite' DESC 'Rewrites strings' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.3.16.2 NAME 'olcRwmTFSupport' DESC 'Absolute filters support' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.16.3 NAME 'olcRwmMap' DESC 'maps attributes/objectClasses' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** X-ORDERED 'VALUES' )",
            "( *******.4.1.4203.********.3.16.4 NAME 'olcRwmNormalizeMapped' DESC 'Normalize mapped attributes/objectClasses' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.16.5 NAME 'olcRwmDropUnrequested' DESC 'Drop unrequested attributes' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.21.1 NAME 'olcSssVlvMax' DESC 'Maximum number of concurrent Sort requests' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.21.2 NAME 'olcSssVlvMaxKeys' DESC 'Maximum number of Keys in a Sort request' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.21.3 NAME 'olcSssVlvMaxPerConn' DESC 'Maximum number of concurrent paged search requests per connection' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.1.1 NAME 'olcSpCheckpoint' DESC 'ContextCSN checkpoint interval in ops and minutes' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.1.2 NAME 'olcSpSessionlog' DESC 'Session log size in ops' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.1.3 NAME 'olcSpNoPresent' DESC 'Omit Present phase processing' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.1.4 NAME 'olcSpReloadHint' DESC 'Observe Reload Hint in Request control' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.14.1 NAME 'olcTranslucentStrict' DESC 'Reveal attribute deletion constraint violations' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.14.2 NAME 'olcTranslucentNoGlue' DESC 'Disable automatic glue records for ADD and MODRDN' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.14.3 NAME 'olcTranslucentLocal' DESC 'Attributes to use in local search filter' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.14.4 NAME 'olcTranslucentRemote' DESC 'Attributes to use in remote search filter' SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.14.5 NAME 'olcTranslucentBindLocal' DESC 'Enable local bind' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.14.6 NAME 'olcTranslucentPwModLocal' DESC 'Enable local RFC 3062 Password Modify extended operation' SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.10.1 NAME 'olcUniqueBase' DESC 'Subtree for uniqueness searches' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.10.2 NAME 'olcUniqueIgnore' DESC 'Attributes for which uniqueness shall not be enforced' EQUALITY caseIgnoreMatch ORDERING caseIgnoreOrderingMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.10.3 NAME 'olcUniqueAttribute' DESC 'Attributes for which uniqueness shall be enforced' EQUALITY caseIgnoreMatch ORDERING caseIgnoreOrderingMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.10.4 NAME 'olcUniqueStrict' DESC 'Enforce uniqueness of null values' EQUALITY booleanMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )",
            "( *******.4.1.4203.********.3.10.5 NAME 'olcUniqueURI' DESC 'List of keywords and LDAP URIs for a uniqueness domain' EQUALITY caseExactMatch ORDERING caseExactOrderingMatch SUBSTR caseExactSubstringsMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.4203.********.3.5.1 NAME 'olcValSortAttr' DESC 'Sorting rule for attribute under given DN' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( 2.5.4.2 NAME 'knowledgeInformation' DESC 'RFC2256: knowledge information' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11**********{32768} )",
            "( 2.5.4.4 NAME ( 'sn' 'surname' ) DESC 'RFC2256: last (family) name(s) for which the entity is known by' SUP name )",
            "( 2.5.4.5 NAME 'serialNumber' DESC 'RFC2256: serial number of the entity' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************{64} )",
            "( ******* NAME ( 'c' 'countryName' ) DESC 'RFC4519: two-letter ISO-3166 country code' SUP name SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( ******* NAME ( 'l' 'localityName' ) DESC 'RFC2256: locality which this object resides in' SUP name )",
            "( ******* NAME ( 'st' 'stateOrProvinceName' ) DESC 'RFC2256: state or province which this object resides in' SUP name )",
            "( ******* NAME ( 'street' 'streetAddress' ) DESC 'RFC2256: street address of this object' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{128} )",
            "( ******** NAME ( 'o' 'organizationName' ) DESC 'RFC2256: organization this object belongs to' SUP name )",
            "( ******** NAME ( 'ou' 'organizationalUnitName' ) DESC 'RFC2256: organizational unit this object belongs to' SUP name )",
            "( ******** NAME 'title' DESC 'RFC2256: title associated with the entity' SUP name )",
            "( ******** NAME 'searchGuide' DESC 'RFC2256: search guide, deprecated by enhancedSearchGuide' SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'businessCategory' DESC 'RFC2256: business category' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{128} )",
            "( ******** NAME 'postalAddress' DESC 'RFC2256: postal address' EQUALITY caseIgnoreListMatch SUBSTR caseIgnoreListSubstringsMatch SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'postalCode' DESC 'RFC2256: postal code' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{40} )",
            "( ******** NAME 'postOfficeBox' DESC 'RFC2256: Post Office Box' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{40} )",
            "( ******** NAME 'physicalDeliveryOfficeName' DESC 'RFC2256: Physical Delivery Office Name' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{128} )",
            "( ******** NAME 'telephoneNumber' DESC 'RFC2256: Telephone Number' EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************{32} )",
            "( ******** NAME 'telexNumber' DESC 'RFC2256: Telex Number' SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'teletexTerminalIdentifier' DESC 'RFC2256: Teletex Terminal Identifier' SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME ( 'facsimileTelephoneNumber' 'fax' ) DESC 'RFC2256: Facsimile (Fax) Telephone Number' SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'x121Address' DESC 'RFC2256: X.121 Address' EQUALITY numericStringMatch SUBSTR numericStringSubstringsMatch SYNTAX *******.4.1.1466.************{15} )",
            "( ******** NAME 'internationaliSDNNumber' DESC 'RFC2256: international ISDN number' EQUALITY numericStringMatch SUBSTR numericStringSubstringsMatch SYNTAX *******.4.1.1466.************{16} )",
            "( ******** NAME 'registeredAddress' DESC 'RFC2256: registered postal address' SUP postalAddress SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'destinationIndicator' DESC 'RFC2256: destination indicator' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************{128} )",
            "( ******** NAME 'preferredDeliveryMethod' DESC 'RFC2256: preferred delivery method' SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( ******** NAME 'presentationAddress' DESC 'RFC2256: presentation address' EQUALITY presentationAddressMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( ******** NAME 'supportedApplicationContext' DESC 'RFC2256: supported application context' EQUALITY objectIdentifierMatch SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'member' DESC 'RFC2256: member of a group' SUP distinguishedName )",
            "( ******** NAME 'owner' DESC 'RFC2256: owner (of the object)' SUP distinguishedName )",
            "( ******** NAME 'roleOccupant' DESC 'RFC2256: occupant of role' SUP distinguishedName )",
            "( ******** NAME 'userCertificate' DESC 'RFC2256: X.509 user certificate, use ;binary' EQUALITY certificateExactMatch SYNTAX *******.4.1.1466.*********** )",
            "( ******** NAME 'cACertificate' DESC 'RFC2256: X.509 CA certificate, use ;binary' EQUALITY certificateExactMatch SYNTAX *******.4.1.1466.*********** )",
            "( ******** NAME 'authorityRevocationList' DESC 'RFC2256: X.509 authority revocation list, use ;binary' SYNTAX *******.4.1.1466.115.121.1.9 )",
            "( 2.5.4.39 NAME 'certificateRevocationList' DESC 'RFC2256: X.509 certificate revocation list, use ;binary' SYNTAX *******.4.1.1466.115.121.1.9 )",
            "( 2.5.4.40 NAME 'crossCertificatePair' DESC 'RFC2256: X.509 cross certificate pair, use ;binary' SYNTAX *******.4.1.1466.***********0 )",
            "( 2.5.4.42 NAME ( 'givenName' 'gn' ) DESC 'RFC2256: first name(s) for which the entity is known by' SUP name )",
            "( 2.5.4.43 NAME 'initials' DESC 'RFC2256: initials of some or all of names, but not the surname(s).' SUP name )",
            "( 2.5.4.44 NAME 'generationQualifier' DESC 'RFC2256: name qualifier indicating a generation' SUP name )",
            "( 2.5.4.45 NAME 'x500UniqueIdentifier' DESC 'RFC2256: X.500 unique identifier' EQUALITY bitStringMatch SYNTAX *******.4.1.1466.115.121.1.6 )",
            "( 2.5.4.46 NAME 'dnQualifier' DESC 'RFC2256: DN qualifier' EQUALITY caseIgnoreMatch ORDERING caseIgnoreOrderingMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.************ )",
            "( 2.5.4.47 NAME 'enhancedSearchGuide' DESC 'RFC2256: enhanced search guide' SYNTAX *******.4.1.1466.115.121.1.21 )",
            "( 2.5.4.48 NAME 'protocolInformation' DESC 'RFC2256: protocol information' EQUALITY protocolInformationMatch SYNTAX *******.4.1.1466.115.121.1.42 )",
            "( 2.5.4.50 NAME 'uniqueMember' DESC 'RFC2256: unique member of a group' EQUALITY uniqueMemberMatch SYNTAX *******.4.1.1466.************ )",
            "( 2.5.4.51 NAME 'houseIdentifier' DESC 'RFC2256: house identifier' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{32768} )",
            "( 2.5.4.52 NAME 'supportedAlgorithms' DESC 'RFC2256: supported algorithms' SYNTAX *******.4.1.1466.************ )",
            "( 2.5.4.53 NAME 'deltaRevocationList' DESC 'RFC2256: delta revocation list; use ;binary' SYNTAX *******.4.1.1466.115.121.1.9 )",
            "( 2.5.4.54 NAME 'dmdName' DESC 'RFC2256: name of DMD' SUP name )",
            "( *******5 NAME 'pseudonym' DESC 'X.520(4th): pseudonym for the object' SUP name )",
            "( 0.9.2342.********.100.1.3 NAME ( 'mail' 'rfc822Mailbox' ) DESC 'RFC1274: RFC822 Mailbox' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************{256} )",
            "( 0.9.2342.********.100.1.25 NAME ( 'dc' 'domainComponent' ) DESC 'RFC1274/2247: domain component' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.37 NAME 'associatedDomain' DESC 'RFC1274: domain associated with object' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ )",
            "( 1.2.840.113549.1.9.1 NAME ( 'email' 'emailAddress' 'pkcs9email' ) DESC 'RFC3280: legacy attribute for email addresses in DNs' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************{128} )",
            "( 0.9.2342.********.100.1.2 NAME 'textEncodedORAddress' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.4 NAME 'info' DESC 'RFC1274: general information' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{2048} )",
            "( 0.9.2342.********.100.1.5 NAME ( 'drink' 'favouriteDrink' ) DESC 'RFC1274: favorite drink' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.6 NAME 'roomNumber' DESC 'RFC1274: room number' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.7 NAME 'photo' DESC 'RFC1274: photo (G3 fax)' SYNTAX *******.4.1.1466.************{25000} )",
            "( 0.9.2342.********.100.1.8 NAME 'userClass' DESC 'RFC1274: category of user' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.9 NAME 'host' DESC 'RFC1274: host computer' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.10 NAME 'manager' DESC 'RFC1274: DN of manager' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.11 NAME 'documentIdentifier' DESC 'RFC1274: unique identifier of document' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.12 NAME 'documentTitle' DESC 'RFC1274: title of document' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.13 NAME 'documentVersion' DESC 'RFC1274: version of document' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.14 NAME 'documentAuthor' DESC 'RFC1274: DN of author of document' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.15 NAME 'documentLocation' DESC 'RFC1274: location of document original' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.20 NAME ( 'homePhone' 'homeTelephoneNumber' ) DESC 'RFC1274: home telephone number' EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.21 NAME 'secretary' DESC 'RFC1274: DN of secretary' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.22 NAME 'otherMailbox' SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.26 NAME 'aRecord' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.27 NAME 'mDRecord' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.28 NAME 'mXRecord' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.29 NAME 'nSRecord' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.30 NAME 'sOARecord' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.31 NAME 'cNAMERecord' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.38 NAME 'associatedName' DESC 'RFC1274: DN of entry associated with domain' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.39 NAME 'homePostalAddress' DESC 'RFC1274: home postal address' EQUALITY caseIgnoreListMatch SUBSTR caseIgnoreListSubstringsMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.40 NAME 'personalTitle' DESC 'RFC1274: personal title' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.41 NAME ( 'mobile' 'mobileTelephoneNumber' ) DESC 'RFC1274: mobile telephone number' EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.42 NAME ( 'pager' 'pagerTelephoneNumber' ) DESC 'RFC1274: pager telephone number' EQUALITY telephoneNumberMatch SUBSTR telephoneNumberSubstringsMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.43 NAME ( 'co' 'friendlyCountryName' ) DESC 'RFC1274: friendly country name' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** )",
            "( 0.9.2342.********.100.1.44 NAME 'uniqueIdentifier' DESC 'RFC1274: unique identifer' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.45 NAME 'organizationalStatus' DESC 'RFC1274: organizational status' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.46 NAME 'janetMailbox' DESC 'RFC1274: Janet mailbox' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************{256} )",
            "( 0.9.2342.********.100.1.47 NAME 'mailPreferenceOption' DESC 'RFC1274: mail preference option' SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.48 NAME 'buildingName' DESC 'RFC1274: name of building' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11**********{256} )",
            "( 0.9.2342.********.100.1.49 NAME 'dSAQuality' DESC 'RFC1274: DSA Quality' SYNTAX *******.4.1.1466.***********9 SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.50 NAME 'singleLevelQuality' DESC 'RFC1274: Single Level Quality' SYNTAX *******.4.1.1466.***********3 SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.51 NAME 'subtreeMinimumQuality' DESC 'RFC1274: Subtree Mininum Quality' SYNTAX *******.4.1.1466.***********3 SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.52 NAME 'subtreeMaximumQuality' DESC 'RFC1274: Subtree Maximun Quality' SYNTAX *******.4.1.1466.***********3 SINGLE-VALUE )",
            "( 0.9.2342.********.100.1.53 NAME 'personalSignature' DESC 'RFC1274: Personal Signature (G3 fax)' SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.54 NAME 'dITRedirect' DESC 'RFC1274: DIT Redirect' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )",
            "( 0.9.2342.********.100.1.55 NAME 'audio' DESC 'RFC1274: audio (u-law)' SYNTAX *******.4.1.1466.115.121.1.4{25000} )",
            "( 0.9.2342.********.100.1.56 NAME 'documentPublisher' DESC 'RFC1274: publisher of document' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** )",
            "( 2.16.840.1.113730.3.1.1 NAME 'carLicense' DESC 'RFC2798: vehicle license or registration plate' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** )",
            "( 2.16.840.1.113730.3.1.2 NAME 'departmentNumber' DESC 'RFC2798: identifies a department within an organization' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** )",
            "( 2.16.840.1.113730.3.1.241 NAME 'displayName' DESC 'RFC2798: preferred name to be used when displaying entries' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( 2.16.840.1.113730.3.1.3 NAME 'employeeNumber' DESC 'RFC2798: numerically identifies an employee within an organization' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( 2.16.840.1.113730.3.1.4 NAME 'employeeType' DESC 'RFC2798: type of employment for a person' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** )",
            "( 0.9.2342.********.100.1.60 NAME 'jpegPhoto' DESC 'RFC2798: a JPEG image' SYNTAX *******.4.1.1466.************ )",
            "( 2.16.840.1.113730.3.1.39 NAME 'preferredLanguage' DESC 'RFC2798: preferred written or spoken language for a person' EQUALITY caseIgnoreMatch SUBSTR caseIgnoreSubstringsMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( 2.16.840.1.113730.3.1.40 NAME 'userSMIMECertificate' DESC 'RFC2798: PKCS#7 SignedData used to support S/MIME' SYNTAX *******.4.1.1466.115.121.1.5 )",
            "( 2.16.840.1.113730.3.1.216 NAME 'userPKCS12' DESC 'RFC2798: personal identity information, a PKCS #12 PFX' SYNTAX *******.4.1.1466.115.121.1.5 )",
            "( *******.1.1.1.2 NAME 'gecos' DESC 'The GECOS field; the common name' EQUALITY caseIgnoreIA5Match SUBSTR caseIgnoreIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.3 NAME 'homeDirectory' DESC 'The absolute path to the home directory' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.4 NAME 'loginShell' DESC 'The path to the login shell' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.5 NAME 'shadowLastChange' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.6 NAME 'shadowMin' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.7 NAME 'shadowMax' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.8 NAME 'shadowWarning' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.9 NAME 'shadowInactive' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.10 NAME 'shadowExpire' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.******** NAME 'shadowFlag' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.******** NAME 'memberUid' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ )",
            "( *******.******** NAME 'memberNisNetgroup' EQUALITY caseExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.************ )",
            "( *******.******** NAME 'nisNetgroupTriple' DESC 'Netgroup triple' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( *******.******** NAME 'ipServicePort' DESC 'Service port number' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.******** NAME 'ipServiceProtocol' DESC 'Service protocol name' SUP name )",
            "( *******.******** NAME 'ipProtocolNumber' DESC 'IP protocol number' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.******** NAME 'oncRpcNumber' DESC 'ONC RPC number' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.******** NAME 'ipHostNumber' DESC 'IPv4 addresses as a dotted decimal omitting leading       zeros or IPv6 addresses as defined in RFC2373' SUP name )",
            "( *******.******** NAME 'ipNetworkNumber' DESC 'IP network as a dotted decimal, eg. 192.168,       omitting leading zeros' SUP name SINGLE-VALUE )",
            "( *******.******** NAME 'ipNetmaskNumber' DESC 'IP netmask as a dotted decimal, eg. *************,       omitting leading zeros' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.******** NAME 'macAddress' DESC 'MAC address in maximal, colon separated hex       notation, eg. 00:00:92:90:ee:e2' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( *******.******** NAME 'bootParameter' DESC 'rpc.bootparamd parameter' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ )",
            "( *******.******** NAME 'bootFile' DESC 'Boot image name' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ )",
            "( *******.******** NAME 'nisMapName' DESC 'Name of a A generic NIS map' SUP name )",
            "( *******.******** NAME 'nisMapEntry' DESC 'A generic NIS entry' EQUALITY caseExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.******** NAME 'nisPublicKey' DESC 'NIS public key' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.******** NAME 'nisSecretKey' DESC 'NIS secret key' EQUALITY octetStringMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.30 NAME 'nisDomain' DESC 'NIS domain' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ )",
            "( *******.1.1.1.31 NAME 'automountMapName' DESC 'automount Map Name' EQUALITY caseExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.32 NAME 'automountKey' DESC 'Automount Key value' EQUALITY caseExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.1.1.1.33 NAME 'automountInformation' DESC 'Automount information' EQUALITY caseExactIA5Match SUBSTR caseExactIA5SubstringsMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.2 NAME 'suseDefaultBase' DESC 'Base DN where new Objects should be created by default' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.3 NAME 'suseNextUniqueId' DESC 'Next unused unique ID, can be used to generate directory wide uniqe IDs' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.4 NAME 'suseMinUniqueId' DESC 'lower Border for Unique IDs' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.5 NAME 'suseMaxUniqueId' DESC 'upper Border for Unique IDs' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.6 NAME 'suseDefaultTemplate' DESC 'The DN of a template that should be used by default' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.7 NAME 'suseSearchFilter' DESC 'Search filter to localize Objects' SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.11 NAME 'suseDefaultValue' DESC 'an Attribute-Value-Assertions to define defaults for specific Attributes' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.7057.10.1.2.2.12 NAME 'suseNamingAttribute' DESC 'AttributeType that should be used as the RDN' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.15 NAME 'suseSecondaryGroup' DESC 'seconday group DN' EQUALITY distinguishedNameMatch SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.7057.10.1.2.2.16 NAME 'suseMinPasswordLength' DESC 'minimum Password length for new users' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.17 NAME 'suseMaxPasswordLength' DESC 'maximum Password length for new users' EQUALITY integerMatch ORDERING integerOrderingMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.18 NAME 'susePasswordHash' DESC 'Hash method to use for new users' EQUALITY caseIgnoreIA5Match SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.19 NAME 'suseSkelDir' DESC '' EQUALITY caseExactIA5Match SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.7057.10.1.2.2.20 NAME 'susePlugin' DESC 'plugin to use upon user/ group creation' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.7057.10.1.2.2.21 NAME 'suseMapAttribute' DESC '' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** )",
            "( *******.4.1.7057.10.1.2.2.22 NAME 'suseImapServer' DESC '' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.23 NAME 'suseImapAdmin' DESC '' EQUALITY caseIgnoreMatch SYNTAX *******.4.1.1466.11********** SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.24 NAME 'suseImapDefaultQuota' DESC '' EQUALITY integerMatch SYNTAX *******.4.1.1466.************ SINGLE-VALUE )",
            "( *******.4.1.7057.10.1.2.2.25 NAME 'suseImapUseSsl' DESC '' EQUALITY booleanMatch SYNTAX *******.4.1.1466.*********** SINGLE-VALUE )"
        ],
        "cn": [
            "Subschema"
        ],
        "createTimestamp": [
            "20141024204149Z"
        ],
        "entryDN": [
            "cn=Subschema"
        ],
        "ldapSyntaxes": [
            "( *******.4.1.1466.115.121.1.4 DESC 'Audio' X-NOT-HUMAN-READABLE 'TRUE' )",
            "( *******.4.1.1466.115.121.1.5 DESC 'Binary' X-NOT-HUMAN-READABLE 'TRUE' )",
            "( *******.4.1.1466.115.121.1.6 DESC 'Bit String' )",
            "( *******.4.1.1466.*********** DESC 'Boolean' )",
            "( *******.4.1.1466.*********** DESC 'Certificate' X-BINARY-TRANSFER-REQUIRED 'TRUE' X-NOT-HUMAN-READABLE 'TRUE' )",
            "( *******.4.1.1466.115.121.1.9 DESC 'Certificate List' X-BINARY-TRANSFER-REQUIRED 'TRUE' X-NOT-HUMAN-READABLE 'TRUE' )",
            "( *******.4.1.1466.***********0 DESC 'Certificate Pair' X-BINARY-TRANSFER-REQUIRED 'TRUE' X-NOT-HUMAN-READABLE 'TRUE' )",
            "( *******.4.1.4203.666.11.10.2.1 DESC 'X.509 AttributeCertificate' X-BINARY-TRANSFER-REQUIRED 'TRUE' X-NOT-HUMAN-READABLE 'TRUE' )",
            "( *******.4.1.1466.************ DESC 'Distinguished Name' )",
            "( 1.2.36.79672281.1.5.0 DESC 'RDN' )",
            "( *******.4.1.1466.************ DESC 'Delivery Method' )",
            "( *******.4.1.1466.11********** DESC 'Directory String' )",
            "( *******.4.1.1466.************ DESC 'Facsimile Telephone Number' )",
            "( *******.4.1.1466.115.121.1.24 DESC 'Generalized Time' )",
            "( *******.4.1.1466.************ DESC 'IA5 String' )",
            "( *******.4.1.1466.************ DESC 'Integer' )",
            "( *******.4.1.1466.************ DESC 'JPEG' X-NOT-HUMAN-READABLE 'TRUE' )",
            "( *******.4.1.1466.************ DESC 'Name And Optional UID' )",
            "( *******.4.1.1466.************ DESC 'Numeric String' )",
            "( *******.4.1.1466.************ DESC 'OID' )",
            "( *******.4.1.1466.************ DESC 'Other Mailbox' )",
            "( *******.4.1.1466.************ DESC 'Octet String' )",
            "( *******.4.1.1466.************ DESC 'Postal Address' )",
            "( *******.4.1.1466.************ DESC 'Printable String' )",
            "( *******.4.1.1466.************ DESC 'Country String' )",
            "( *******.4.1.1466.************ DESC 'SubtreeSpecification' )",
            "( *******.4.1.1466.************ DESC 'Supported Algorithm' X-BINARY-TRANSFER-REQUIRED 'TRUE' X-NOT-HUMAN-READABLE 'TRUE' )",
            "( *******.4.1.1466.************ DESC 'Telephone Number' )",
            "( *******.4.1.1466.************ DESC 'Telex Number' )",
            "( *******.******* DESC 'RFC2307 NIS Netgroup Triple' )",
            "( *******.******* DESC 'RFC2307 Boot Parameter' )",
            "( *******.1.16.1 DESC 'UUID' )"
        ],
        "matchingRuleUse": [
            "( 1.2.840.113556.1.4.804 NAME 'integerBitOrMatch' APPLIES ( supportedLDAPVersion $ entryTtl $ uidNumber $ gidNumber $ olcConcurrency $ olcConnMaxPending $ olcConnMaxPendingAuth $ olcIdleTimeout $ olcIndexSubstrIfMinLen $ olcIndexSubstrIfMaxLen $ olcIndexSubstrAnyLen $ olcIndexSubstrAnyStep $ olcIndexIntLen $ olcListenerThreads $ olcLocalSSF $ olcMaxDerefDepth $ olcReplicationInterval $ olcSockbufMaxIncoming $ olcSockbufMaxIncomingAuth $ olcThreads $ olcToolThreads $ olcWriteTimeout $ olcDbCacheFree $ olcDbCacheSize $ olcDbDNcacheSize $ olcDbIDLcacheSize $ olcDbSearchStack $ olcDbShmKey $ olcDbProtocolVersion $ olcDbConnectionPoolMax $ olcChainMaxReferralDepth $ olcDDSmaxDynamicObjects $ olcPcacheMaxQueries $ olcRetcodeSleep $ olcSssVlvMax $ olcSssVlvMaxKeys $ olcSssVlvMaxPerConn $ olcSpSessionlog $ mailPreferenceOption $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ ipServicePort $ ipProtocolNumber $ oncRpcNumber $ suseNextUniqueId $ suseMinUniqueId $ suseMaxUniqueId $ suseMinPasswordLength $ suseMaxPasswordLength $ suseImapDefaultQuota ) )",
            "( 1.2.840.113556.1.4.803 NAME 'integerBitAndMatch' APPLIES ( supportedLDAPVersion $ entryTtl $ uidNumber $ gidNumber $ olcConcurrency $ olcConnMaxPending $ olcConnMaxPendingAuth $ olcIdleTimeout $ olcIndexSubstrIfMinLen $ olcIndexSubstrIfMaxLen $ olcIndexSubstrAnyLen $ olcIndexSubstrAnyStep $ olcIndexIntLen $ olcListenerThreads $ olcLocalSSF $ olcMaxDerefDepth $ olcReplicationInterval $ olcSockbufMaxIncoming $ olcSockbufMaxIncomingAuth $ olcThreads $ olcToolThreads $ olcWriteTimeout $ olcDbCacheFree $ olcDbCacheSize $ olcDbDNcacheSize $ olcDbIDLcacheSize $ olcDbSearchStack $ olcDbShmKey $ olcDbProtocolVersion $ olcDbConnectionPoolMax $ olcChainMaxReferralDepth $ olcDDSmaxDynamicObjects $ olcPcacheMaxQueries $ olcRetcodeSleep $ olcSssVlvMax $ olcSssVlvMaxKeys $ olcSssVlvMaxPerConn $ olcSpSessionlog $ mailPreferenceOption $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ ipServicePort $ ipProtocolNumber $ oncRpcNumber $ suseNextUniqueId $ suseMinUniqueId $ suseMaxUniqueId $ suseMinPasswordLength $ suseMaxPasswordLength $ suseImapDefaultQuota ) )",
            "( *******.4.1.1466.109.114.2 NAME 'caseIgnoreIA5Match' APPLIES ( altServer $ olcDbConfig $ c $ mail $ dc $ associatedDomain $ email $ aRecord $ mDRecord $ mXRecord $ nSRecord $ sOARecord $ cNAMERecord $ janetMailbox $ gecos $ homeDirectory $ loginShell $ memberUid $ memberNisNetgroup $ nisNetgroupTriple $ ipNetmaskNumber $ macAddress $ bootParameter $ bootFile $ nisMapEntry $ nisDomain $ automountMapName $ automountKey $ automountInformation $ suseNamingAttribute $ susePasswordHash $ suseSkelDir ) )",
            "( *******.4.1.1466.109.114.1 NAME 'caseExactIA5Match' APPLIES ( altServer $ olcDbConfig $ c $ mail $ dc $ associatedDomain $ email $ aRecord $ mDRecord $ mXRecord $ nSRecord $ sOARecord $ cNAMERecord $ janetMailbox $ gecos $ homeDirectory $ loginShell $ memberUid $ memberNisNetgroup $ nisNetgroupTriple $ ipNetmaskNumber $ macAddress $ bootParameter $ bootFile $ nisMapEntry $ nisDomain $ automountMapName $ automountKey $ automountInformation $ suseNamingAttribute $ susePasswordHash $ suseSkelDir ) )",
            "( ********8 NAME 'certificateListExactMatch' APPLIES ( authorityRevocationList $ certificateRevocationList $ deltaRevocationList ) )",
            "( ********4 NAME 'certificateExactMatch' APPLIES ( userCertificate $ cACertificate ) )",
            "( ********0 NAME 'objectIdentifierFirstComponentMatch' APPLIES ( supportedControl $ supportedExtension $ supportedFeatures $ ldapSyntaxes $ supportedApplicationContext ) )",
            "( ********9 NAME 'integerFirstComponentMatch' APPLIES ( supportedLDAPVersion $ entryTtl $ uidNumber $ gidNumber $ olcConcurrency $ olcConnMaxPending $ olcConnMaxPendingAuth $ olcIdleTimeout $ olcIndexSubstrIfMinLen $ olcIndexSubstrIfMaxLen $ olcIndexSubstrAnyLen $ olcIndexSubstrAnyStep $ olcIndexIntLen $ olcListenerThreads $ olcLocalSSF $ olcMaxDerefDepth $ olcReplicationInterval $ olcSockbufMaxIncoming $ olcSockbufMaxIncomingAuth $ olcThreads $ olcToolThreads $ olcWriteTimeout $ olcDbCacheFree $ olcDbCacheSize $ olcDbDNcacheSize $ olcDbIDLcacheSize $ olcDbSearchStack $ olcDbShmKey $ olcDbProtocolVersion $ olcDbConnectionPoolMax $ olcChainMaxReferralDepth $ olcDDSmaxDynamicObjects $ olcPcacheMaxQueries $ olcRetcodeSleep $ olcSssVlvMax $ olcSssVlvMaxKeys $ olcSssVlvMaxPerConn $ olcSpSessionlog $ mailPreferenceOption $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ ipServicePort $ ipProtocolNumber $ oncRpcNumber $ suseNextUniqueId $ suseMinUniqueId $ suseMaxUniqueId $ suseMinPasswordLength $ suseMaxPasswordLength $ suseImapDefaultQuota ) )",
            "( ********8 NAME 'generalizedTimeOrderingMatch' APPLIES ( createTimestamp $ modifyTimestamp $ pwdChangedTime $ pwdAccountLockedTime $ pwdFailureTime $ pwdGraceUseTime ) )",
            "( ********7 NAME 'generalizedTimeMatch' APPLIES ( createTimestamp $ modifyTimestamp $ pwdChangedTime $ pwdAccountLockedTime $ pwdFailureTime $ pwdGraceUseTime ) )",
            "( ********4 NAME 'protocolInformationMatch' APPLIES protocolInformation )",
            "( ********3 NAME 'uniqueMemberMatch' APPLIES uniqueMember )",
            "( ********2 NAME 'presentationAddressMatch' APPLIES presentationAddress )",
            "( ********0 NAME 'telephoneNumberMatch' APPLIES ( telephoneNumber $ homePhone $ mobile $ pager ) )",
            "( ********8 NAME 'octetStringOrderingMatch' APPLIES ( userPassword $ olcDbCryptKey $ pwdHistory $ nisPublicKey $ nisSecretKey ) )",
            "( ********7 NAME 'octetStringMatch' APPLIES ( userPassword $ olcDbCryptKey $ pwdHistory $ nisPublicKey $ nisSecretKey ) )",
            "( ********6 NAME 'bitStringMatch' APPLIES x500UniqueIdentifier )",
            "( ********5 NAME 'integerOrderingMatch' APPLIES ( supportedLDAPVersion $ entryTtl $ uidNumber $ gidNumber $ olcConcurrency $ olcConnMaxPending $ olcConnMaxPendingAuth $ olcIdleTimeout $ olcIndexSubstrIfMinLen $ olcIndexSubstrIfMaxLen $ olcIndexSubstrAnyLen $ olcIndexSubstrAnyStep $ olcIndexIntLen $ olcListenerThreads $ olcLocalSSF $ olcMaxDerefDepth $ olcReplicationInterval $ olcSockbufMaxIncoming $ olcSockbufMaxIncomingAuth $ olcThreads $ olcToolThreads $ olcWriteTimeout $ olcDbCacheFree $ olcDbCacheSize $ olcDbDNcacheSize $ olcDbIDLcacheSize $ olcDbSearchStack $ olcDbShmKey $ olcDbProtocolVersion $ olcDbConnectionPoolMax $ olcChainMaxReferralDepth $ olcDDSmaxDynamicObjects $ olcPcacheMaxQueries $ olcRetcodeSleep $ olcSssVlvMax $ olcSssVlvMaxKeys $ olcSssVlvMaxPerConn $ olcSpSessionlog $ mailPreferenceOption $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ ipServicePort $ ipProtocolNumber $ oncRpcNumber $ suseNextUniqueId $ suseMinUniqueId $ suseMaxUniqueId $ suseMinPasswordLength $ suseMaxPasswordLength $ suseImapDefaultQuota ) )",
            "( ********4 NAME 'integerMatch' APPLIES ( supportedLDAPVersion $ entryTtl $ uidNumber $ gidNumber $ olcConcurrency $ olcConnMaxPending $ olcConnMaxPendingAuth $ olcIdleTimeout $ olcIndexSubstrIfMinLen $ olcIndexSubstrIfMaxLen $ olcIndexSubstrAnyLen $ olcIndexSubstrAnyStep $ olcIndexIntLen $ olcListenerThreads $ olcLocalSSF $ olcMaxDerefDepth $ olcReplicationInterval $ olcSockbufMaxIncoming $ olcSockbufMaxIncomingAuth $ olcThreads $ olcToolThreads $ olcWriteTimeout $ olcDbCacheFree $ olcDbCacheSize $ olcDbDNcacheSize $ olcDbIDLcacheSize $ olcDbSearchStack $ olcDbShmKey $ olcDbProtocolVersion $ olcDbConnectionPoolMax $ olcChainMaxReferralDepth $ olcDDSmaxDynamicObjects $ olcPcacheMaxQueries $ olcRetcodeSleep $ olcSssVlvMax $ olcSssVlvMaxKeys $ olcSssVlvMaxPerConn $ olcSpSessionlog $ mailPreferenceOption $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag $ ipServicePort $ ipProtocolNumber $ oncRpcNumber $ suseNextUniqueId $ suseMinUniqueId $ suseMaxUniqueId $ suseMinPasswordLength $ suseMaxPasswordLength $ suseImapDefaultQuota ) )",
            "( ********3 NAME 'booleanMatch' APPLIES ( hasSubordinates $ olcAddContentAcl $ olcGentleHUP $ olcHidden $ olcLastMod $ olcMirrorMode $ olcMonitoring $ olcReadOnly $ olcReverseLookup $ olcSyncUseSubentry $ olcDbChecksum $ olcDbNoSync $ olcDbDirtyRead $ olcDbLinearIndex $ olcDbRebindAsUser $ olcDbChaseReferrals $ olcDbProxyWhoAmI $ olcDbSingleConn $ olcDbUseTemporaryConn $ olcDbNoRefs $ olcDbNoUndefFilter $ olcChainCacheURI $ olcChainReturnError $ olcAccessLogSuccess $ olcDDSstate $ olcMemberOfRefInt $ pwdReset $ olcPPolicyHashCleartext $ olcPPolicyForwardUpdates $ olcPPolicyUseLockout $ olcPcachePersist $ olcPcacheValidate $ olcPcacheOffline $ olcRetcodeInDir $ olcRwmNormalizeMapped $ olcRwmDropUnrequested $ olcSpNoPresent $ olcSpReloadHint $ olcTranslucentStrict $ olcTranslucentNoGlue $ olcTranslucentBindLocal $ olcTranslucentPwModLocal $ olcUniqueStrict $ suseImapUseSsl ) )",
            "( ********* NAME 'caseIgnoreListMatch' APPLIES ( postalAddress $ registeredAddress $ homePostalAddress ) )",
            "( ******** NAME 'numericStringOrderingMatch' APPLIES ( x121Address $ internationaliSDNNumber ) )",
            "( ******** NAME 'numericStringMatch' APPLIES ( x121Address $ internationaliSDNNumber ) )",
            "( ******** NAME 'caseExactSubstringsMatch' APPLIES ( serialNumber $ destinationIndicator $ dnQualifier ) )",
            "( ******** NAME 'caseExactOrderingMatch' APPLIES ( supportedSASLMechanisms $ vendorName $ vendorVersion $ ref $ name $ cn $ uid $ labeledURI $ description $ olcConfigFile $ olcConfigDir $ olcAccess $ olcAllows $ olcArgsFile $ olcAttributeOptions $ olcAttributeTypes $ olcAuthIDRewrite $ olcAuthzPolicy $ olcAuthzRegexp $ olcBackend $ olcDatabase $ olcDisallows $ olcDitContentRules $ olcExtraAttrs $ olcInclude $ olcLdapSyntaxes $ olcLimits $ olcLogFile $ olcLogLevel $ olcModuleLoad $ olcModulePath $ olcObjectClasses $ olcObjectIdentifier $ olcOverlay $ olcPasswordCryptSaltFormat $ olcPasswordHash $ olcPidFile $ olcPlugin $ olcPluginLogFile $ olcReferral $ olcReplica $ olcReplicaArgsFile $ olcReplicaPidFile $ olcReplogFile $ olcRequires $ olcRestrict $ olcRootDSE $ olcRootPW $ olcSaslAuxprops $ olcSaslHost $ olcSaslRealm $ olcSaslSecProps $ olcSecurity $ olcServerID $ olcSizeLimit $ olcSortVals $ olcSubordinate $ olcSyncrepl $ olcTCPBuffer $ olcTimeLimit $ olcTLSCACertificateFile $ olcTLSCACertificatePath $ olcTLSCertificateFile $ olcTLSCertificateKeyFile $ olcTLSCipherSuite $ olcTLSCRLCheck $ olcTLSCRLFile $ olcTLSRandFile $ olcTLSVerifyClient $ olcTLSDHParamFile $ olcTLSProtocolMin $ olcUpdateRef $ olcDbDirectory $ olcDbCheckpoint $ olcDbCryptFile $ olcDbPageSize $ olcDbIndex $ olcDbLockDetect $ olcDbMode $ olcDbURI $ olcDbStartTLS $ olcDbACLPasswd $ olcDbACLBind $ olcDbIDAssertPasswd $ olcDbIDAssertBind $ olcDbIDAssertMode $ olcDbIDAssertAuthzFrom $ olcDbTFSupport $ olcDbTimeout $ olcDbIdleTimeout $ olcDbConnTtl $ olcDbNetworkTimeout $ olcDbCancel $ olcDbQuarantine $ olcDbIDAssertPassThru $ olcChainingBehavior $ olcAccessLogOps $ olcAccessLogPurge $ olcAccessLogOld $ olcAccessLogOldAttr $ olcAccessLogBase $ olcAuditlogFile $ olcCollectInfo $ olcConstraintAttribute $ olcDDSmaxTtl $ olcDDSminTtl $ olcDDSdefaultTtl $ olcDDSinterval $ olcDDStolerance $ olcDGAttrPair $ olcDlAttrSet $ olcMemberOfDangling $ olcMemberOfGroupOC $ olcMemberOfMemberAD $ olcMemberOfMemberOfAD $ olcMemberOfDanglingError $ olcPcache $ olcPcacheAttrset $ olcPcacheTemplate $ olcPcachePosition $ olcPcacheBind $ olcRefintAttribute $ olcRetcodeItem $ olcRwmRewrite $ olcRwmTFSupport $ olcRwmMap $ olcSpCheckpoint $ olcTranslucentLocal $ olcTranslucentRemote $ olcUniqueIgnore $ olcUniqueAttribute $ olcUniqueURI $ olcValSortAttr $ knowledgeInformation $ sn $ serialNumber $ c $ l $ st $ street $ o $ ou $ title $ businessCategory $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ destinationIndicator $ givenName $ initials $ generationQualifier $ dnQualifier $ houseIdentifier $ dmdName $ pseudonym $ textEncodedORAddress $ info $ drink $ roomNumber $ userClass $ host $ documentIdentifier $ documentTitle $ documentVersion $ documentLocation $ personalTitle $ co $ uniqueIdentifier $ organizationalStatus $ buildingName $ documentPublisher $ carLicense $ departmentNumber $ displayName $ employeeNumber $ employeeType $ preferredLanguage $ ipServiceProtocol $ ipHostNumber $ ipNetworkNumber $ nisMapName $ suseSearchFilter $ suseDefaultValue $ susePlugin $ suseMapAttribute $ suseImapServer $ suseImapAdmin ) )",
            "( ******** NAME 'caseExactMatch' APPLIES ( supportedSASLMechanisms $ vendorName $ vendorVersion $ ref $ name $ cn $ uid $ labeledURI $ description $ olcConfigFile $ olcConfigDir $ olcAccess $ olcAllows $ olcArgsFile $ olcAttributeOptions $ olcAttributeTypes $ olcAuthIDRewrite $ olcAuthzPolicy $ olcAuthzRegexp $ olcBackend $ olcDatabase $ olcDisallows $ olcDitContentRules $ olcExtraAttrs $ olcInclude $ olcLdapSyntaxes $ olcLimits $ olcLogFile $ olcLogLevel $ olcModuleLoad $ olcModulePath $ olcObjectClasses $ olcObjectIdentifier $ olcOverlay $ olcPasswordCryptSaltFormat $ olcPasswordHash $ olcPidFile $ olcPlugin $ olcPluginLogFile $ olcReferral $ olcReplica $ olcReplicaArgsFile $ olcReplicaPidFile $ olcReplogFile $ olcRequires $ olcRestrict $ olcRootDSE $ olcRootPW $ olcSaslAuxprops $ olcSaslHost $ olcSaslRealm $ olcSaslSecProps $ olcSecurity $ olcServerID $ olcSizeLimit $ olcSortVals $ olcSubordinate $ olcSyncrepl $ olcTCPBuffer $ olcTimeLimit $ olcTLSCACertificateFile $ olcTLSCACertificatePath $ olcTLSCertificateFile $ olcTLSCertificateKeyFile $ olcTLSCipherSuite $ olcTLSCRLCheck $ olcTLSCRLFile $ olcTLSRandFile $ olcTLSVerifyClient $ olcTLSDHParamFile $ olcTLSProtocolMin $ olcUpdateRef $ olcDbDirectory $ olcDbCheckpoint $ olcDbCryptFile $ olcDbPageSize $ olcDbIndex $ olcDbLockDetect $ olcDbMode $ olcDbURI $ olcDbStartTLS $ olcDbACLPasswd $ olcDbACLBind $ olcDbIDAssertPasswd $ olcDbIDAssertBind $ olcDbIDAssertMode $ olcDbIDAssertAuthzFrom $ olcDbTFSupport $ olcDbTimeout $ olcDbIdleTimeout $ olcDbConnTtl $ olcDbNetworkTimeout $ olcDbCancel $ olcDbQuarantine $ olcDbIDAssertPassThru $ olcChainingBehavior $ olcAccessLogOps $ olcAccessLogPurge $ olcAccessLogOld $ olcAccessLogOldAttr $ olcAccessLogBase $ olcAuditlogFile $ olcCollectInfo $ olcConstraintAttribute $ olcDDSmaxTtl $ olcDDSminTtl $ olcDDSdefaultTtl $ olcDDSinterval $ olcDDStolerance $ olcDGAttrPair $ olcDlAttrSet $ olcMemberOfDangling $ olcMemberOfGroupOC $ olcMemberOfMemberAD $ olcMemberOfMemberOfAD $ olcMemberOfDanglingError $ olcPcache $ olcPcacheAttrset $ olcPcacheTemplate $ olcPcachePosition $ olcPcacheBind $ olcRefintAttribute $ olcRetcodeItem $ olcRwmRewrite $ olcRwmTFSupport $ olcRwmMap $ olcSpCheckpoint $ olcTranslucentLocal $ olcTranslucentRemote $ olcUniqueIgnore $ olcUniqueAttribute $ olcUniqueURI $ olcValSortAttr $ knowledgeInformation $ sn $ serialNumber $ c $ l $ st $ street $ o $ ou $ title $ businessCategory $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ destinationIndicator $ givenName $ initials $ generationQualifier $ dnQualifier $ houseIdentifier $ dmdName $ pseudonym $ textEncodedORAddress $ info $ drink $ roomNumber $ userClass $ host $ documentIdentifier $ documentTitle $ documentVersion $ documentLocation $ personalTitle $ co $ uniqueIdentifier $ organizationalStatus $ buildingName $ documentPublisher $ carLicense $ departmentNumber $ displayName $ employeeNumber $ employeeType $ preferredLanguage $ ipServiceProtocol $ ipHostNumber $ ipNetworkNumber $ nisMapName $ suseSearchFilter $ suseDefaultValue $ susePlugin $ suseMapAttribute $ suseImapServer $ suseImapAdmin ) )",
            "( ******** NAME 'caseIgnoreSubstringsMatch' APPLIES ( serialNumber $ destinationIndicator $ dnQualifier ) )",
            "( ******** NAME 'caseIgnoreOrderingMatch' APPLIES ( supportedSASLMechanisms $ vendorName $ vendorVersion $ ref $ name $ cn $ uid $ labeledURI $ description $ olcConfigFile $ olcConfigDir $ olcAccess $ olcAllows $ olcArgsFile $ olcAttributeOptions $ olcAttributeTypes $ olcAuthIDRewrite $ olcAuthzPolicy $ olcAuthzRegexp $ olcBackend $ olcDatabase $ olcDisallows $ olcDitContentRules $ olcExtraAttrs $ olcInclude $ olcLdapSyntaxes $ olcLimits $ olcLogFile $ olcLogLevel $ olcModuleLoad $ olcModulePath $ olcObjectClasses $ olcObjectIdentifier $ olcOverlay $ olcPasswordCryptSaltFormat $ olcPasswordHash $ olcPidFile $ olcPlugin $ olcPluginLogFile $ olcReferral $ olcReplica $ olcReplicaArgsFile $ olcReplicaPidFile $ olcReplogFile $ olcRequires $ olcRestrict $ olcRootDSE $ olcRootPW $ olcSaslAuxprops $ olcSaslHost $ olcSaslRealm $ olcSaslSecProps $ olcSecurity $ olcServerID $ olcSizeLimit $ olcSortVals $ olcSubordinate $ olcSyncrepl $ olcTCPBuffer $ olcTimeLimit $ olcTLSCACertificateFile $ olcTLSCACertificatePath $ olcTLSCertificateFile $ olcTLSCertificateKeyFile $ olcTLSCipherSuite $ olcTLSCRLCheck $ olcTLSCRLFile $ olcTLSRandFile $ olcTLSVerifyClient $ olcTLSDHParamFile $ olcTLSProtocolMin $ olcUpdateRef $ olcDbDirectory $ olcDbCheckpoint $ olcDbCryptFile $ olcDbPageSize $ olcDbIndex $ olcDbLockDetect $ olcDbMode $ olcDbURI $ olcDbStartTLS $ olcDbACLPasswd $ olcDbACLBind $ olcDbIDAssertPasswd $ olcDbIDAssertBind $ olcDbIDAssertMode $ olcDbIDAssertAuthzFrom $ olcDbTFSupport $ olcDbTimeout $ olcDbIdleTimeout $ olcDbConnTtl $ olcDbNetworkTimeout $ olcDbCancel $ olcDbQuarantine $ olcDbIDAssertPassThru $ olcChainingBehavior $ olcAccessLogOps $ olcAccessLogPurge $ olcAccessLogOld $ olcAccessLogOldAttr $ olcAccessLogBase $ olcAuditlogFile $ olcCollectInfo $ olcConstraintAttribute $ olcDDSmaxTtl $ olcDDSminTtl $ olcDDSdefaultTtl $ olcDDSinterval $ olcDDStolerance $ olcDGAttrPair $ olcDlAttrSet $ olcMemberOfDangling $ olcMemberOfGroupOC $ olcMemberOfMemberAD $ olcMemberOfMemberOfAD $ olcMemberOfDanglingError $ olcPcache $ olcPcacheAttrset $ olcPcacheTemplate $ olcPcachePosition $ olcPcacheBind $ olcRefintAttribute $ olcRetcodeItem $ olcRwmRewrite $ olcRwmTFSupport $ olcRwmMap $ olcSpCheckpoint $ olcTranslucentLocal $ olcTranslucentRemote $ olcUniqueIgnore $ olcUniqueAttribute $ olcUniqueURI $ olcValSortAttr $ knowledgeInformation $ sn $ serialNumber $ c $ l $ st $ street $ o $ ou $ title $ businessCategory $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ destinationIndicator $ givenName $ initials $ generationQualifier $ dnQualifier $ houseIdentifier $ dmdName $ pseudonym $ textEncodedORAddress $ info $ drink $ roomNumber $ userClass $ host $ documentIdentifier $ documentTitle $ documentVersion $ documentLocation $ personalTitle $ co $ uniqueIdentifier $ organizationalStatus $ buildingName $ documentPublisher $ carLicense $ departmentNumber $ displayName $ employeeNumber $ employeeType $ preferredLanguage $ ipServiceProtocol $ ipHostNumber $ ipNetworkNumber $ nisMapName $ suseSearchFilter $ suseDefaultValue $ susePlugin $ suseMapAttribute $ suseImapServer $ suseImapAdmin ) )",
            "( ******** NAME 'caseIgnoreMatch' APPLIES ( supportedSASLMechanisms $ vendorName $ vendorVersion $ ref $ name $ cn $ uid $ labeledURI $ description $ olcConfigFile $ olcConfigDir $ olcAccess $ olcAllows $ olcArgsFile $ olcAttributeOptions $ olcAttributeTypes $ olcAuthIDRewrite $ olcAuthzPolicy $ olcAuthzRegexp $ olcBackend $ olcDatabase $ olcDisallows $ olcDitContentRules $ olcExtraAttrs $ olcInclude $ olcLdapSyntaxes $ olcLimits $ olcLogFile $ olcLogLevel $ olcModuleLoad $ olcModulePath $ olcObjectClasses $ olcObjectIdentifier $ olcOverlay $ olcPasswordCryptSaltFormat $ olcPasswordHash $ olcPidFile $ olcPlugin $ olcPluginLogFile $ olcReferral $ olcReplica $ olcReplicaArgsFile $ olcReplicaPidFile $ olcReplogFile $ olcRequires $ olcRestrict $ olcRootDSE $ olcRootPW $ olcSaslAuxprops $ olcSaslHost $ olcSaslRealm $ olcSaslSecProps $ olcSecurity $ olcServerID $ olcSizeLimit $ olcSortVals $ olcSubordinate $ olcSyncrepl $ olcTCPBuffer $ olcTimeLimit $ olcTLSCACertificateFile $ olcTLSCACertificatePath $ olcTLSCertificateFile $ olcTLSCertificateKeyFile $ olcTLSCipherSuite $ olcTLSCRLCheck $ olcTLSCRLFile $ olcTLSRandFile $ olcTLSVerifyClient $ olcTLSDHParamFile $ olcTLSProtocolMin $ olcUpdateRef $ olcDbDirectory $ olcDbCheckpoint $ olcDbCryptFile $ olcDbPageSize $ olcDbIndex $ olcDbLockDetect $ olcDbMode $ olcDbURI $ olcDbStartTLS $ olcDbACLPasswd $ olcDbACLBind $ olcDbIDAssertPasswd $ olcDbIDAssertBind $ olcDbIDAssertMode $ olcDbIDAssertAuthzFrom $ olcDbTFSupport $ olcDbTimeout $ olcDbIdleTimeout $ olcDbConnTtl $ olcDbNetworkTimeout $ olcDbCancel $ olcDbQuarantine $ olcDbIDAssertPassThru $ olcChainingBehavior $ olcAccessLogOps $ olcAccessLogPurge $ olcAccessLogOld $ olcAccessLogOldAttr $ olcAccessLogBase $ olcAuditlogFile $ olcCollectInfo $ olcConstraintAttribute $ olcDDSmaxTtl $ olcDDSminTtl $ olcDDSdefaultTtl $ olcDDSinterval $ olcDDStolerance $ olcDGAttrPair $ olcDlAttrSet $ olcMemberOfDangling $ olcMemberOfGroupOC $ olcMemberOfMemberAD $ olcMemberOfMemberOfAD $ olcMemberOfDanglingError $ olcPcache $ olcPcacheAttrset $ olcPcacheTemplate $ olcPcachePosition $ olcPcacheBind $ olcRefintAttribute $ olcRetcodeItem $ olcRwmRewrite $ olcRwmTFSupport $ olcRwmMap $ olcSpCheckpoint $ olcTranslucentLocal $ olcTranslucentRemote $ olcUniqueIgnore $ olcUniqueAttribute $ olcUniqueURI $ olcValSortAttr $ knowledgeInformation $ sn $ serialNumber $ c $ l $ st $ street $ o $ ou $ title $ businessCategory $ postalCode $ postOfficeBox $ physicalDeliveryOfficeName $ destinationIndicator $ givenName $ initials $ generationQualifier $ dnQualifier $ houseIdentifier $ dmdName $ pseudonym $ textEncodedORAddress $ info $ drink $ roomNumber $ userClass $ host $ documentIdentifier $ documentTitle $ documentVersion $ documentLocation $ personalTitle $ co $ uniqueIdentifier $ organizationalStatus $ buildingName $ documentPublisher $ carLicense $ departmentNumber $ displayName $ employeeNumber $ employeeType $ preferredLanguage $ ipServiceProtocol $ ipHostNumber $ ipNetworkNumber $ nisMapName $ suseSearchFilter $ suseDefaultValue $ susePlugin $ suseMapAttribute $ suseImapServer $ suseImapAdmin ) )",
            "( ******** NAME 'distinguishedNameMatch' APPLIES ( creatorsName $ modifiersName $ subschemaSubentry $ entryDN $ namingContexts $ aliasedObjectName $ dynamicSubtrees $ distinguishedName $ seeAlso $ olcDefaultSearchBase $ olcRootDN $ olcSchemaDN $ olcSuffix $ olcUpdateDN $ olcDbACLAuthcDn $ olcDbIDAssertAuthcDn $ olcRelay $ olcAccessLogDB $ memberOf $ olcMemberOfDN $ pwdPolicySubentry $ olcPPolicyDefault $ olcRefintNothing $ olcRefintModifiersName $ olcRetcodeParent $ olcUniqueBase $ member $ owner $ roleOccupant $ manager $ documentAuthor $ secretary $ associatedName $ dITRedirect $ suseDefaultBase $ suseDefaultTemplate $ suseSecondaryGroup ) )",
            "( ******** NAME 'objectIdentifierMatch' APPLIES ( supportedControl $ supportedExtension $ supportedFeatures $ supportedApplicationContext ) )"
        ],
        "matchingRules": [
            "( *******.1.16.3 NAME 'UUIDOrderingMatch' SYNTAX *******.1.16.1 )",
            "( *******.1.16.2 NAME 'UUIDMatch' SYNTAX *******.1.16.1 )",
            "( 1.2.840.113556.1.4.804 NAME 'integerBitOrMatch' SYNTAX *******.4.1.1466.************ )",
            "( 1.2.840.113556.1.4.803 NAME 'integerBitAndMatch' SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.4203.1.2.1 NAME 'caseExactIA5SubstringsMatch' SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.1466.109.114.3 NAME 'caseIgnoreIA5SubstringsMatch' SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.1466.109.114.2 NAME 'caseIgnoreIA5Match' SYNTAX *******.4.1.1466.************ )",
            "( *******.4.1.1466.109.114.1 NAME 'caseExactIA5Match' SYNTAX *******.4.1.1466.************ )",
            "( ********8 NAME 'certificateListExactMatch' SYNTAX *******.1.15.5 )",
            "( ********4 NAME 'certificateExactMatch' SYNTAX *******.1.15.1 )",
            "( ********0 NAME 'objectIdentifierFirstComponentMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********9 NAME 'integerFirstComponentMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********8 NAME 'generalizedTimeOrderingMatch' SYNTAX *******.4.1.1466.115.121.1.24 )",
            "( ********7 NAME 'generalizedTimeMatch' SYNTAX *******.4.1.1466.115.121.1.24 )",
            "( ********3 NAME 'uniqueMemberMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********1 NAME 'telephoneNumberSubstringsMatch' SYNTAX *******.4.1.1466.115.121.1.58 )",
            "( ********0 NAME 'telephoneNumberMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********9 NAME 'octetStringSubstringsMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********8 NAME 'octetStringOrderingMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********7 NAME 'octetStringMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********6 NAME 'bitStringMatch' SYNTAX *******.4.1.1466.115.121.1.6 )",
            "( ********5 NAME 'integerOrderingMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********4 NAME 'integerMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********3 NAME 'booleanMatch' SYNTAX *******.4.1.1466.*********** )",
            "( ********* NAME 'caseIgnoreListMatch' SYNTAX *******.4.1.1466.************ )",
            "( ********0 NAME 'numericStringSubstringsMatch' SYNTAX *******.4.1.1466.115.121.1.58 )",
            "( ******** NAME 'numericStringOrderingMatch' SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'numericStringMatch' SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'caseExactSubstringsMatch' SYNTAX *******.4.1.1466.115.121.1.58 )",
            "( ******** NAME 'caseExactOrderingMatch' SYNTAX *******.4.1.1466.11********** )",
            "( ******** NAME 'caseExactMatch' SYNTAX *******.4.1.1466.11********** )",
            "( ******** NAME 'caseIgnoreSubstringsMatch' SYNTAX *******.4.1.1466.115.121.1.58 )",
            "( ******** NAME 'caseIgnoreOrderingMatch' SYNTAX *******.4.1.1466.11********** )",
            "( ******** NAME 'caseIgnoreMatch' SYNTAX *******.4.1.1466.11********** )",
            "( 1.2.36.79672281.1.13.3 NAME 'rdnMatch' SYNTAX 1.2.36.79672281.1.5.0 )",
            "( ******** NAME 'distinguishedNameMatch' SYNTAX *******.4.1.1466.************ )",
            "( ******** NAME 'objectIdentifierMatch' SYNTAX *******.4.1.1466.************ )"
        ],
        "modifyTimestamp": [
            "20141024204149Z"
        ],
        "objectClass": [
            "top",
            "subentry",
            "subschema",
            "extensibleObject"
        ],
        "objectClasses": [
            "( 2.5.6.0 NAME 'top' DESC 'top of the superclass chain' ABSTRACT MUST objectClass )",
            "( *******.4.1.1466.101.120.111 NAME 'extensibleObject' DESC 'RFC4512: extensible object' SUP top AUXILIARY )",
            "( 2.5.6.1 NAME 'alias' DESC 'RFC4512: an alias' SUP top STRUCTURAL MUST aliasedObjectName )",
            "( 2.16.840.1.113730.3.2.6 NAME 'referral' DESC 'namedref: named subordinate referral' SUP top STRUCTURAL MUST ref )",
            "( *******.4.1.4203.1.4.1 NAME ( 'OpenLDAProotDSE' 'LDAProotDSE' ) DESC 'OpenLDAP Root DSE object' SUP top STRUCTURAL MAY cn )",
            "( 2.5.17.0 NAME 'subentry' DESC 'RFC3672: subentry' SUP top STRUCTURAL MUST ( cn $ subtreeSpecification ) )",
            "( 2.5.20.1 NAME 'subschema' DESC 'RFC4512: controlling subschema (sub)entry' AUXILIARY MAY ( dITStructureRules $ nameForms $ dITContentRules $ objectClasses $ attributeTypes $ matchingRules $ matchingRuleUse ) )",
            "( *******.4.1.1466.101.119.2 NAME 'dynamicObject' DESC 'RFC2589: Dynamic Object' SUP top AUXILIARY )",
            "( *******.4.1.4203.********.0.0 NAME 'olcConfig' DESC 'OpenLDAP configuration object' SUP top ABSTRACT )",
            "( *******.4.1.4203.********.0.1 NAME 'olcGlobal' DESC 'OpenLDAP Global configuration options' SUP olcConfig STRUCTURAL MAY ( cn $ olcConfigFile $ olcConfigDir $ olcAllows $ olcArgsFile $ olcAttributeOptions $ olcAuthIDRewrite $ olcAuthzPolicy $ olcAuthzRegexp $ olcConcurrency $ olcConnMaxPending $ olcConnMaxPendingAuth $ olcDisallows $ olcGentleHUP $ olcIdleTimeout $ olcIndexSubstrIfMaxLen $ olcIndexSubstrIfMinLen $ olcIndexSubstrAnyLen $ olcIndexSubstrAnyStep $ olcIndexIntLen $ olcLocalSSF $ olcLogFile $ olcLogLevel $ olcPasswordCryptSaltFormat $ olcPasswordHash $ olcPidFile $ olcPluginLogFile $ olcReadOnly $ olcReferral $ olcReplogFile $ olcRequires $ olcRestrict $ olcReverseLookup $ olcRootDSE $ olcSaslAuxprops $ olcSaslHost $ olcSaslRealm $ olcSaslSecProps $ olcSecurity $ olcServerID $ olcSizeLimit $ olcSockbufMaxIncoming $ olcSockbufMaxIncomingAuth $ olcTCPBuffer $ olcThreads $ olcTimeLimit $ olcTLSCACertificateFile $ olcTLSCACertificatePath $ olcTLSCertificateFile $ olcTLSCertificateKeyFile $ olcTLSCipherSuite $ olcTLSCRLCheck $ olcTLSRandFile $ olcTLSVerifyClient $ olcTLSDHParamFile $ olcTLSCRLFile $ olcToolThreads $ olcWriteTimeout $ olcObjectIdentifier $ olcAttributeTypes $ olcObjectClasses $ olcDitContentRules $ olcLdapSyntaxes ) )",
            "( *******.4.1.4203.********.0.2 NAME 'olcSchemaConfig' DESC 'OpenLDAP schema object' SUP olcConfig STRUCTURAL MAY ( cn $ olcObjectIdentifier $ olcAttributeTypes $ olcObjectClasses $ olcDitContentRules $ olcLdapSyntaxes ) )",
            "( *******.4.1.4203.********.0.3 NAME 'olcBackendConfig' DESC 'OpenLDAP Backend-specific options' SUP olcConfig STRUCTURAL MUST olcBackend )",
            "( *******.4.1.4203.********.0.4 NAME 'olcDatabaseConfig' DESC 'OpenLDAP Database-specific options' SUP olcConfig STRUCTURAL MUST olcDatabase MAY ( olcHidden $ olcSuffix $ olcSubordinate $ olcAccess $ olcAddContentAcl $ olcLastMod $ olcLimits $ olcMaxDerefDepth $ olcPlugin $ olcReadOnly $ olcReplica $ olcReplicaArgsFile $ olcReplicaPidFile $ olcReplicationInterval $ olcReplogFile $ olcRequires $ olcRestrict $ olcRootDN $ olcRootPW $ olcSchemaDN $ olcSecurity $ olcSizeLimit $ olcSyncUseSubentry $ olcSyncrepl $ olcTimeLimit $ olcUpdateDN $ olcUpdateRef $ olcMirrorMode $ olcMonitoring $ olcExtraAttrs ) )",
            "( *******.4.1.4203.********.0.5 NAME 'olcOverlayConfig' DESC 'OpenLDAP Overlay-specific options' SUP olcConfig STRUCTURAL MUST olcOverlay )",
            "( *******.4.1.4203.********.0.6 NAME 'olcIncludeFile' DESC 'OpenLDAP configuration include file' SUP olcConfig STRUCTURAL MUST olcInclude MAY ( cn $ olcRootDSE ) )",
            "( *******.4.1.4203.********.0.7 NAME 'olcFrontendConfig' DESC 'OpenLDAP frontend configuration' AUXILIARY MAY ( olcDefaultSearchBase $ olcPasswordHash $ olcSortVals ) )",
            "( *******.4.1.4203.********.0.8 NAME 'olcModuleList' DESC 'OpenLDAP dynamic module info' SUP olcConfig STRUCTURAL MAY ( cn $ olcModulePath $ olcModuleLoad ) )",
            "( *******.4.1.4203.********.2.2.1 NAME 'olcLdifConfig' DESC 'LDIF backend configuration' SUP olcDatabaseConfig STRUCTURAL MUST olcDbDirectory )",
            "( *******.4.1.4203.********.2.4.1 NAME 'olcMonitorConfig' DESC 'Monitor backend configuration' SUP olcDatabaseConfig STRUCTURAL )",
            "( *******.4.1.4203.********.2.1.1 NAME 'olcBdbConfig' DESC 'BDB backend configuration' SUP olcDatabaseConfig STRUCTURAL MUST olcDbDirectory MAY ( olcDbCacheSize $ olcDbCheckpoint $ olcDbConfig $ olcDbCryptFile $ olcDbCryptKey $ olcDbNoSync $ olcDbDirtyRead $ olcDbIDLcacheSize $ olcDbIndex $ olcDbLinearIndex $ olcDbLockDetect $ olcDbMode $ olcDbSearchStack $ olcDbShmKey $ olcDbCacheFree $ olcDbDNcacheSize $ olcDbPageSize ) )",
            "( *******.4.1.4203.********.2.1.2 NAME 'olcHdbConfig' DESC 'HDB backend configuration' SUP olcDatabaseConfig STRUCTURAL MUST olcDbDirectory MAY ( olcDbCacheSize $ olcDbCheckpoint $ olcDbConfig $ olcDbCryptFile $ olcDbCryptKey $ olcDbNoSync $ olcDbDirtyRead $ olcDbIDLcacheSize $ olcDbIndex $ olcDbLinearIndex $ olcDbLockDetect $ olcDbMode $ olcDbSearchStack $ olcDbShmKey $ olcDbCacheFree $ olcDbDNcacheSize $ olcDbPageSize ) )",
            "( *******.4.1.4203.********.2.3.1 NAME 'olcLDAPConfig' DESC 'LDAP backend configuration' SUP olcDatabaseConfig STRUCTURAL MAY ( olcDbURI $ olcDbStartTLS $ olcDbACLAuthcDn $ olcDbACLPasswd $ olcDbACLBind $ olcDbIDAssertAuthcDn $ olcDbIDAssertPasswd $ olcDbIDAssertBind $ olcDbIDAssertMode $ olcDbIDAssertAuthzFrom $ olcDbIDAssertPassThru $ olcDbRebindAsUser $ olcDbChaseReferrals $ olcDbTFSupport $ olcDbProxyWhoAmI $ olcDbTimeout $ olcDbIdleTimeout $ olcDbConnTtl $ olcDbNetworkTimeout $ olcDbProtocolVersion $ olcDbSingleConn $ olcDbCancel $ olcDbQuarantine $ olcDbUseTemporaryConn $ olcDbConnectionPoolMax $ olcDbNoRefs $ olcDbNoUndefFilter ) )",
            "( *******.4.1.4203.********.3.3.1 NAME 'olcChainConfig' DESC 'Chain configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcChainingBehavior $ olcChainCacheURI $ olcChainMaxReferralDepth $ olcChainReturnError ) )",
            "( *******.4.1.4203.********.3.3.2 NAME 'olcChainDatabase' DESC 'Chain remote server configuration' AUXILIARY )",
            "( *******.4.1.4203.********.3.3.3 NAME 'olcPBindConfig' DESC 'Proxy Bind configuration' SUP olcOverlayConfig STRUCTURAL MUST olcDbURI MAY ( olcDbStartTLS $ olcDbNetworkTimeout $ olcDbQuarantine ) )",
            "( *******.4.1.4203.********.3.7.1 NAME 'olcDistProcConfig' DESC 'Distributed procedures <draft-sermersheim-ldap-distproc> configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcChainingBehavior $ olcChainCacheURI ) )",
            "( *******.4.1.4203.********.3.7.2 NAME 'olcDistProcDatabase' DESC 'Distributed procedure remote server configuration' AUXILIARY )",
            "( *******.4.1.4203.********.2.5.1 NAME 'olcRelayConfig' DESC 'Relay backend configuration' SUP olcDatabaseConfig STRUCTURAL MAY olcRelay )",
            "( *******.4.1.4203.********.3.4.1 NAME 'olcAccessLogConfig' DESC 'Access log configuration' SUP olcOverlayConfig STRUCTURAL MUST olcAccessLogDB MAY ( olcAccessLogOps $ olcAccessLogPurge $ olcAccessLogSuccess $ olcAccessLogOld $ olcAccessLogOldAttr $ olcAccessLogBase ) )",
            "( *******.4.1.4203.********.3.15.1 NAME 'olcAuditlogConfig' DESC 'Auditlog configuration' SUP olcOverlayConfig STRUCTURAL MAY olcAuditlogFile )",
            "( *******.4.1.4203.********.3.19.1 NAME 'olcCollectConfig' DESC 'Collective Attribute configuration' SUP olcOverlayConfig STRUCTURAL MAY olcCollectInfo )",
            "( *******.4.1.4203.********.3.13.1 NAME 'olcConstraintConfig' DESC 'Constraint overlay configuration' SUP olcOverlayConfig STRUCTURAL MAY olcConstraintAttribute )",
            "( *******.4.1.4203.********.3.9.1 NAME 'olcDDSConfig' DESC 'RFC2589 Dynamic directory services configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcDDSstate $ olcDDSmaxTtl $ olcDDSminTtl $ olcDDSdefaultTtl $ olcDDSinterval $ olcDDStolerance $ olcDDSmaxDynamicObjects ) )",
            "( *******.4.1.4203.********.3.17.1 NAME 'olcDGConfig' DESC 'Dynamic Group configuration' SUP olcOverlayConfig STRUCTURAL MAY olcDGAttrPair )",
            "( *******.4.1.4203.********.3.8.1 NAME 'olcDynamicList' DESC 'Dynamic list configuration' SUP olcOverlayConfig STRUCTURAL MAY olcDLattrSet )",
            "( *******.4.1.4203.********.3.18.1 NAME 'olcMemberOf' DESC 'Member-of configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcMemberOfDN $ olcMemberOfDangling $ olcMemberOfDanglingError $ olcMemberOfRefInt $ olcMemberOfGroupOC $ olcMemberOfMemberAD $ olcMemberOfMemberOfAD ) )",
            "( *******.4.1.4203.********.3.12.1 NAME 'olcPPolicyConfig' DESC 'Password Policy configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcPPolicyDefault $ olcPPolicyHashCleartext $ olcPPolicyUseLockout $ olcPPolicyForwardUpdates ) )",
            "( *******.4.1.4203.********.3.2.1 NAME 'olcPcacheConfig' DESC 'ProxyCache configuration' SUP olcOverlayConfig STRUCTURAL MUST ( olcPcache $ olcPcacheAttrset $ olcPcacheTemplate ) MAY ( olcPcachePosition $ olcPcacheMaxQueries $ olcPcachePersist $ olcPcacheValidate $ olcPcacheOffline $ olcPcacheBind ) )",
            "( *******.4.1.4203.********.3.2.2 NAME 'olcPcacheDatabase' DESC 'Cache database configuration' AUXILIARY )",
            "( *******.4.1.4203.********.3.11.1 NAME 'olcRefintConfig' DESC 'Referential integrity configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcRefintAttribute $ olcRefintNothing $ olcRefintModifiersName ) )",
            "( *******.4.1.4203.********.3.20.1 NAME 'olcRetcodeConfig' DESC 'Retcode configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcRetcodeParent $ olcRetcodeItem $ olcRetcodeInDir $ olcRetcodeSleep ) )",
            "( *******.4.1.4203.********.3.16.1 NAME 'olcRwmConfig' DESC 'Rewrite/remap configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcRwmRewrite $ olcRwmTFSupport $ olcRwmMap $ olcRwmNormalizeMapped ) )",
            "( *******.4.1.4203.********.3.21.1 NAME 'olcSssVlvConfig' DESC 'SSS VLV configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcSssVlvMax $ olcSssVlvMaxKeys ) )",
            "( *******.4.1.4203.********.3.1.1 NAME 'olcSyncProvConfig' DESC 'SyncRepl Provider configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcSpCheckpoint $ olcSpSessionlog $ olcSpNoPresent $ olcSpReloadHint ) )",
            "( *******.4.1.4203.********.3.14.1 NAME 'olcTranslucentConfig' DESC 'Translucent configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcTranslucentStrict $ olcTranslucentNoGlue $ olcTranslucentLocal $ olcTranslucentRemote $ olcTranslucentBindLocal $ olcTranslucentPwModLocal ) )",
            "( *******.4.1.4203.********.3.14.2 NAME 'olcTranslucentDatabase' DESC 'Translucent target database configuration' AUXILIARY )",
            "( *******.4.1.4203.********.3.10.1 NAME 'olcUniqueConfig' DESC 'Attribute value uniqueness configuration' SUP olcOverlayConfig STRUCTURAL MAY ( olcUniqueBase $ olcUniqueIgnore $ olcUniqueAttribute $ olcUniqueStrict $ olcUniqueURI ) )",
            "( *******.4.1.4203.********.3.5.1 NAME 'olcValSortConfig' DESC 'Value Sorting configuration' SUP olcOverlayConfig STRUCTURAL MUST olcValSortAttr )",
            "( ******* NAME 'country' DESC 'RFC2256: a country' SUP top STRUCTURAL MUST c MAY ( searchGuide $ description ) )",
            "( ******* NAME 'locality' DESC 'RFC2256: a locality' SUP top STRUCTURAL MAY ( street $ seeAlso $ searchGuide $ st $ l $ description ) )",
            "( ******* NAME 'organization' DESC 'RFC2256: an organization' SUP top STRUCTURAL MUST o MAY ( userPassword $ searchGuide $ seeAlso $ businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l $ description ) )",
            "( ******* NAME 'organizationalUnit' DESC 'RFC2256: an organizational unit' SUP top STRUCTURAL MUST ou MAY ( userPassword $ searchGuide $ seeAlso $ businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l $ description ) )",
            "( ******* NAME 'person' DESC 'RFC2256: a person' SUP top STRUCTURAL MUST ( sn $ cn ) MAY ( userPassword $ telephoneNumber $ seeAlso $ description ) )",
            "( ******* NAME 'organizationalPerson' DESC 'RFC2256: an organizational person' SUP person STRUCTURAL MAY ( title $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ ou $ st $ l ) )",
            "( ******* NAME 'organizationalRole' DESC 'RFC2256: an organizational role' SUP top STRUCTURAL MUST cn MAY ( x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ seeAlso $ roleOccupant $ preferredDeliveryMethod $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ ou $ st $ l $ description ) )",
            "( ******* NAME 'groupOfNames' DESC 'RFC2256: a group of names (DNs)' SUP top STRUCTURAL MUST ( member $ cn ) MAY ( businessCategory $ seeAlso $ owner $ ou $ o $ description ) )",
            "( ******** NAME 'residentialPerson' DESC 'RFC2256: an residential person' SUP person STRUCTURAL MUST l MAY ( businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ preferredDeliveryMethod $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l ) )",
            "( ******** NAME 'applicationProcess' DESC 'RFC2256: an application process' SUP top STRUCTURAL MUST cn MAY ( seeAlso $ ou $ l $ description ) )",
            "( ******** NAME 'applicationEntity' DESC 'RFC2256: an application entity' SUP top STRUCTURAL MUST ( presentationAddress $ cn ) MAY ( supportedApplicationContext $ seeAlso $ ou $ o $ l $ description ) )",
            "( ******** NAME 'dSA' DESC 'RFC2256: a directory system agent (a server)' SUP applicationEntity STRUCTURAL MAY knowledgeInformation )",
            "( ******** NAME 'device' DESC 'RFC2256: a device' SUP top STRUCTURAL MUST cn MAY ( serialNumber $ seeAlso $ owner $ ou $ o $ l $ description ) )",
            "( ******** NAME 'strongAuthenticationUser' DESC 'RFC2256: a strong authentication user' SUP top AUXILIARY MUST userCertificate )",
            "( ******** NAME 'certificationAuthority' DESC 'RFC2256: a certificate authority' SUP top AUXILIARY MUST ( authorityRevocationList $ certificateRevocationList $ cACertificate ) MAY crossCertificatePair )",
            "( ******** NAME 'groupOfUniqueNames' DESC 'RFC2256: a group of unique names (DN and Unique Identifier)' SUP top STRUCTURAL MUST ( uniqueMember $ cn ) MAY ( businessCategory $ seeAlso $ owner $ ou $ o $ description ) )",
            "( ******** NAME 'userSecurityInformation' DESC 'RFC2256: a user security information' SUP top AUXILIARY MAY supportedAlgorithms )",
            "( ********.2 NAME 'certificationAuthority-V2' SUP certificationAuthority AUXILIARY MAY deltaRevocationList )",
            "( ******** NAME 'cRLDistributionPoint' SUP top STRUCTURAL MUST cn MAY ( certificateRevocationList $ authorityRevocationList $ deltaRevocationList ) )",
            "( *******0 NAME 'dmd' SUP top STRUCTURAL MUST dmdName MAY ( userPassword $ searchGuide $ seeAlso $ businessCategory $ x121Address $ registeredAddress $ destinationIndicator $ preferredDeliveryMethod $ telexNumber $ teletexTerminalIdentifier $ telephoneNumber $ internationaliSDNNumber $ facsimileTelephoneNumber $ street $ postOfficeBox $ postalCode $ postalAddress $ physicalDeliveryOfficeName $ st $ l $ description ) )",
            "( *******1 NAME 'pkiUser' DESC 'RFC2587: a PKI user' SUP top AUXILIARY MAY userCertificate )",
            "( *******2 NAME 'pkiCA' DESC 'RFC2587: PKI certificate authority' SUP top AUXILIARY MAY ( authorityRevocationList $ certificateRevocationList $ cACertificate $ crossCertificatePair ) )",
            "( *******3 NAME 'deltaCRL' DESC 'RFC2587: PKI user' SUP top AUXILIARY MAY deltaRevocationList )",
            "( *******.*********.15 NAME 'labeledURIObject' DESC 'RFC2079: object that contains the URI attribute type' SUP top AUXILIARY MAY labeledURI )",
            "( 0.9.2342.********.100.4.19 NAME 'simpleSecurityObject' DESC 'RFC1274: simple security object' SUP top AUXILIARY MUST userPassword )",
            "( *******.4.1.1466.344 NAME 'dcObject' DESC 'RFC2247: domain component object' SUP top AUXILIARY MUST dc )",
            "( *******.1.3.1 NAME 'uidObject' DESC 'RFC2377: uid object' SUP top AUXILIARY MUST uid )",
            "( 0.9.2342.********.100.4.4 NAME ( 'pilotPerson' 'newPilotPerson' ) SUP person STRUCTURAL MAY ( userid $ textEncodedORAddress $ rfc822Mailbox $ favouriteDrink $ roomNumber $ userClass $ homeTelephoneNumber $ homePostalAddress $ secretary $ personalTitle $ preferredDeliveryMethod $ businessCategory $ janetMailbox $ otherMailbox $ mobileTelephoneNumber $ pagerTelephoneNumber $ organizationalStatus $ mailPreferenceOption $ personalSignature ) )",
            "( 0.9.2342.********.100.4.5 NAME 'account' SUP top STRUCTURAL MUST userid MAY ( description $ seeAlso $ localityName $ organizationName $ organizationalUnitName $ host ) )",
            "( 0.9.2342.********.100.4.6 NAME 'document' SUP top STRUCTURAL MUST documentIdentifier MAY ( commonName $ description $ seeAlso $ localityName $ organizationName $ organizationalUnitName $ documentTitle $ documentVersion $ documentAuthor $ documentLocation $ documentPublisher ) )",
            "( 0.9.2342.********.100.4.7 NAME 'room' SUP top STRUCTURAL MUST commonName MAY ( roomNumber $ description $ seeAlso $ telephoneNumber ) )",
            "( 0.9.2342.********.100.4.9 NAME 'documentSeries' SUP top STRUCTURAL MUST commonName MAY ( description $ seeAlso $ telephonenumber $ localityName $ organizationName $ organizationalUnitName ) )",
            "( 0.9.2342.********.100.4.13 NAME 'domain' SUP top STRUCTURAL MUST domainComponent MAY ( associatedName $ organizationName $ description $ businessCategory $ seeAlso $ searchGuide $ userPassword $ localityName $ stateOrProvinceName $ streetAddress $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ streetAddress $ facsimileTelephoneNumber $ internationalISDNNumber $ telephoneNumber $ teletexTerminalIdentifier $ telexNumber $ preferredDeliveryMethod $ destinationIndicator $ registeredAddress $ x121Address ) )",
            "( 0.9.2342.********.100.4.14 NAME 'RFC822localPart' SUP domain STRUCTURAL MAY ( commonName $ surname $ description $ seeAlso $ telephoneNumber $ physicalDeliveryOfficeName $ postalAddress $ postalCode $ postOfficeBox $ streetAddress $ facsimileTelephoneNumber $ internationalISDNNumber $ telephoneNumber $ teletexTerminalIdentifier $ telexNumber $ preferredDeliveryMethod $ destinationIndicator $ registeredAddress $ x121Address ) )",
            "( 0.9.2342.********.100.4.15 NAME 'dNSDomain' SUP domain STRUCTURAL MAY ( ARecord $ MDRecord $ MXRecord $ NSRecord $ SOARecord $ CNAMERecord ) )",
            "( 0.9.2342.********.100.4.17 NAME 'domainRelatedObject' DESC 'RFC1274: an object related to an domain' SUP top AUXILIARY MUST associatedDomain )",
            "( 0.9.2342.********.100.4.18 NAME 'friendlyCountry' SUP country STRUCTURAL MUST friendlyCountryName )",
            "( 0.9.2342.********.100.4.20 NAME 'pilotOrganization' SUP ( organization $ organizationalUnit ) STRUCTURAL MAY buildingName )",
            "( 0.9.2342.********.100.4.21 NAME 'pilotDSA' SUP dsa STRUCTURAL MAY dSAQuality )",
            "( 0.9.2342.********.100.4.22 NAME 'qualityLabelledData' SUP top AUXILIARY MUST dsaQuality MAY ( subtreeMinimumQuality $ subtreeMaximumQuality ) )",
            "( 2.16.840.1.113730.3.2.2 NAME 'inetOrgPerson' DESC 'RFC2798: Internet Organizational Person' SUP organizationalPerson STRUCTURAL MAY ( audio $ businessCategory $ carLicense $ departmentNumber $ displayName $ employeeNumber $ employeeType $ givenName $ homePhone $ homePostalAddress $ initials $ jpegPhoto $ labeledURI $ mail $ manager $ mobile $ o $ pager $ photo $ roomNumber $ secretary $ uid $ userCertificate $ x500uniqueIdentifier $ preferredLanguage $ userSMIMECertificate $ userPKCS12 ) )",
            "( *******.******* NAME 'posixAccount' DESC 'Abstraction of an account with POSIX attributes' SUP top AUXILIARY MUST ( cn $ uid $ uidNumber $ gidNumber $ homeDirectory ) MAY ( userPassword $ loginShell $ gecos $ description ) )",
            "( *******.******* NAME 'shadowAccount' DESC 'Additional attributes for shadow passwords' SUP top AUXILIARY MUST uid MAY ( userPassword $ description $ shadowLastChange $ shadowMin $ shadowMax $ shadowWarning $ shadowInactive $ shadowExpire $ shadowFlag ) )",
            "( *******.******* NAME 'posixGroup' DESC 'Abstraction of a group of accounts' SUP top AUXILIARY MUST gidNumber MAY ( userPassword $ memberUid $ description ) )",
            "( *******.******* NAME 'ipService' DESC 'Abstraction an Internet Protocol service.       Maps an IP port and protocol (such as tcp or udp)       to one or more names; the distinguished value of       the cn attribute denotes the services canonical       name' SUP top STRUCTURAL MUST ( cn $ ipServicePort $ ipServiceProtocol ) MAY description )",
            "( *******.******* NAME 'ipProtocol' DESC 'Abstraction of an IP protocol. Maps a protocol number       to one or more names. The distinguished value of the cn       attribute denotes the protocols canonical name' SUP top STRUCTURAL MUST ( cn $ ipProtocolNumber ) MAY description )",
            "( *******.******* NAME 'oncRpc' DESC 'Abstraction of an Open Network Computing (ONC)      [RFC1057] Remote Procedure Call (RPC) binding.      This class maps an ONC RPC number to a name.      The distinguished value of the cn attribute denotes      the RPC services canonical name' SUP top STRUCTURAL MUST ( cn $ oncRpcNumber ) MAY description )",
            "( *******.******* NAME 'ipHost' DESC 'Abstraction of a host, an IP device. The distinguished       value of the cn attribute denotes the hosts canonical       name. Device SHOULD be used as a structural class' SUP top AUXILIARY MUST ( cn $ ipHostNumber ) MAY ( userPassword $ l $ description $ manager ) )",
            "( *******.******* NAME 'ipNetwork' DESC 'Abstraction of a network. The distinguished value of       the cn attribute denotes the networks canonical name' SUP top STRUCTURAL MUST ipNetworkNumber MAY ( cn $ ipNetmaskNumber $ l $ description $ manager ) )",
            "( *******.******* NAME 'nisNetgroup' DESC 'Abstraction of a netgroup. May refer to other netgroups' SUP top STRUCTURAL MUST cn MAY ( nisNetgroupTriple $ memberNisNetgroup $ description ) )",
            "( *******.******* NAME 'nisMap' DESC 'A generic abstraction of a NIS map' SUP top STRUCTURAL MUST nisMapName MAY description )",
            "( *******.******** NAME 'nisObject' DESC 'An entry in a NIS map' SUP top STRUCTURAL MUST ( cn $ nisMapEntry $ nisMapName ) MAY description )",
            "( *******.******** NAME 'ieee802Device' DESC 'A device with a MAC address; device SHOULD be       used as a structural class' SUP top AUXILIARY MAY macAddress )",
            "( *******.******** NAME 'bootableDevice' DESC 'A device with boot parameters; device SHOULD be       used as a structural class' SUP top AUXILIARY MAY ( bootFile $ bootParameter ) )",
            "( *******.******** NAME 'nisKeyObject' DESC 'An object with a public and secret key' SUP top AUXILIARY MUST ( cn $ nisPublicKey $ nisSecretKey ) MAY ( uidNumber $ description ) )",
            "( *******.******** NAME 'nisDomainObject' DESC 'Associates a NIS domain with a naming context' SUP top AUXILIARY MUST nisDomain )",
            "( *******.******** NAME 'automountMap' SUP top STRUCTURAL MUST automountMapName MAY description )",
            "( *******.******** NAME 'automount' DESC 'Automount information' SUP top STRUCTURAL MUST ( automountKey $ automountInformation ) MAY description )",
            "( *******.4.1.5322.13.1.1 NAME 'namedObject' SUP top STRUCTURAL MAY cn )",
            "( *******.4.1.7057.********.2 NAME 'suseModuleConfiguration' DESC 'Contains configuration of Management Modules' SUP top STRUCTURAL MUST cn MAY suseDefaultBase )",
            "( *******.4.1.7057.********.3 NAME 'suseUserConfiguration' DESC 'Configuration of user management tools' SUP suseModuleConfiguration STRUCTURAL MAY ( suseMinPasswordLength $ suseMaxPasswordLength $ susePasswordHash $ suseSkelDir $ suseNextUniqueId $ suseMinUniqueId $ suseMaxUniqueId $ suseDefaultTemplate $ suseSearchFilter $ suseMapAttribute ) )",
            "( *******.4.1.7057.********.4 NAME 'suseObjectTemplate' DESC 'Base Class for Object-Templates' SUP top STRUCTURAL MUST cn MAY ( susePlugin $ suseDefaultValue $ suseNamingAttribute ) )",
            "( *******.4.1.7057.********.5 NAME 'suseUserTemplate' DESC 'User object template' SUP suseObjectTemplate STRUCTURAL MUST cn MAY suseSecondaryGroup )",
            "( *******.4.1.7057.********.6 NAME 'suseGroupTemplate' DESC 'Group object template' SUP suseObjectTemplate STRUCTURAL MUST cn )",
            "( *******.4.1.7057.********.7 NAME 'suseGroupConfiguration' DESC 'Configuration of user management tools' SUP suseModuleConfiguration STRUCTURAL MAY ( suseNextUniqueId $ suseMinUniqueId $ suseMaxUniqueId $ suseDefaultTemplate $ suseSearchFilter $ suseMapAttribute ) )",
            "( *******.4.1.7057.********.8 NAME 'suseCaConfiguration' DESC 'Configuration of CA management tools' SUP suseModuleConfiguration STRUCTURAL )",
            "( *******.4.1.7057.********.9 NAME 'suseDnsConfiguration' DESC 'Configuration of mail server management tools' SUP suseModuleConfiguration STRUCTURAL )",
            "( *******.4.1.7057.********.10 NAME 'suseDhcpConfiguration' DESC 'Configuration of DHCP server management tools' SUP suseModuleConfiguration STRUCTURAL )",
            "( *******.4.1.7057.********.11 NAME 'suseMailConfiguration' DESC 'Configuration of IMAP user management tools' SUP suseModuleConfiguration STRUCTURAL MUST ( suseImapServer $ suseImapAdmin $ suseImapDefaultQuota $ suseImapUseSsl ) )"
        ],
        "structuralObjectClass": [
            "subentry"
        ],
        "subschemaSubentry": [
            "cn=Subschema"
        ]
    },
    "schema_entry": "cn=Subschema",
    "type": "SchemaInfo"
}
"""

slapd_2_4_dsa_info = """
{
    "raw": {
        "configContext": [
            "cn=config"
        ],
        "entryDN": [
            ""
        ],
        "namingContexts": [
            "o=services",
            "o=test"
        ],
        "objectClass": [
            "top",
            "OpenLDAProotDSE"
        ],
        "structuralObjectClass": [
            "OpenLDAProotDSE"
        ],
        "subschemaSubentry": [
            "cn=Subschema"
        ],
        "supportedControl": [
            "*******.4.1.4203.1.9.1.1",
            "2.16.840.1.113730.3.4.18",
            "2.16.840.1.113730.3.4.2",
            "*******.4.1.4203.1.10.1",
            "1.2.840.113556.1.4.319",
            "1.2.826.0.1.3344810.2.3",
            "*******.1.13.2",
            "*******.1.13.1",
            "*******.1.12"
        ],
        "supportedExtension": [
            "*******.4.1.1466.20037",
            "*******.4.1.4203.1.11.1",
            "*******.4.1.4203.1.11.3",
            "*******.1.8"
        ],
        "supportedFeatures": [
            "*******.1.14",
            "*******.4.1.4203.1.5.1",
            "*******.4.1.4203.1.5.2",
            "*******.4.1.4203.1.5.3",
            "*******.4.1.4203.1.5.4",
            "*******.4.1.4203.1.5.5"
        ],
        "supportedLDAPVersion": [
            "3"
        ],
        "supportedSASLMechanisms": [
            "GSSAPI",
            "DIGEST-MD5"
        ]
    },
    "type": "DsaInfo"
}
"""
