"""
"""

# Created on 2013.08.30
#
# Author: <PERSON>
#
# Copyright 2013 - 2020 <PERSON>
#
# This file is part of ldap3.
#
# ldap3 is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published
# by the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# ldap3 is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with ldap3 in the COPYING and COPYING.LESSER files.
# If not, see <http://www.gnu.org/licenses/>.

from .. import SEQUENCE_TYPES

# Holds info about OIDs.
# Each OID info is a named tuple with the following attributes:
# oid - the OID number
# type - type of OID
# name - description of OID
# doc - reference document of OID
#
# Source of information is IANA ldap-parameters.txt, oid-registry and products documentation as of 2013.08.21


# OID database definition
OID_CONTROL = 'CONTROL'
OID_EXTENSION = 'EXTENSION'
OID_FEATURE = 'FEATURE'
OID_UNSOLICITED_NOTICE = 'UNSOLICITED_NOTICE'
OID_ATTRIBUTE_TYPE = 'ATTRIBUTE_TYPE'
OID_DIT_CONTENT_RULE = 'DIT_CONTENT_RULE'
OID_LDAP_URL_EXTENSION = 'LDAP_URL_EXTENSION'
OID_FAMILY = 'FAMILY'
OID_MATCHING_RULE = 'MATCHING_RULE'
OID_NAME_FORM = 'NAME_FORM'
OID_OBJECT_CLASS = 'OBJECT_CLASS'
OID_ADMINISTRATIVE_ROLE = 'ADMINISTRATIVE_ROLE'
OID_LDAP_SYNTAX = 'LDAP_SYNTAX'

# class kind
CLASS_STRUCTURAL = 'STRUCTURAL'
CLASS_ABSTRACT = 'ABSTRACT'
CLASS_AUXILIARY = 'AUXILIARY'

# attribute kind
ATTRIBUTE_USER_APPLICATION = 'USER_APPLICATION'
ATTRIBUTE_DIRECTORY_OPERATION = 'DIRECTORY_OPERATION'
ATTRIBUTE_DISTRIBUTED_OPERATION = 'DISTRIBUTED_OPERATION'
ATTRIBUTE_DSA_OPERATION = 'DSA_OPERATION'


def constant_to_oid_kind(oid_kind):
    if oid_kind == OID_CONTROL:
        return 'Control'
    elif oid_kind == OID_EXTENSION:
        return 'Extension'
    elif oid_kind == OID_FEATURE:
        return 'Feature'
    elif oid_kind == OID_UNSOLICITED_NOTICE:
        return 'Unsolicited Notice'
    elif oid_kind == OID_ATTRIBUTE_TYPE:
        return 'Attribute Type'
    elif oid_kind == OID_DIT_CONTENT_RULE:
        return 'DIT Content Rule'
    elif oid_kind == OID_LDAP_URL_EXTENSION:
        return 'LDAP URL Extension'
    elif oid_kind == OID_FAMILY:
        return 'Family'
    elif oid_kind == OID_MATCHING_RULE:
        return 'Matching Rule'
    elif oid_kind == OID_NAME_FORM:
        return 'Name Form'
    elif oid_kind == OID_OBJECT_CLASS:
        return 'Object Class'
    elif oid_kind == OID_ADMINISTRATIVE_ROLE:
        return 'Administrative Role'
    elif oid_kind == OID_LDAP_SYNTAX:
        return 'LDAP Syntax'
    else:
        return 'Unknown'


def decode_oids(sequence):
    if sequence:
        return sorted([Oids.get(oid, (oid, None, None, None)) for oid in sequence if oid])
    return list()


def decode_syntax(syntax):
    if not syntax:
        return None
    return Oids.get(syntax, None)


def oid_to_string(oid):
    s = oid[0]
    if oid[2]:
        s += ' - ' + ((', '.join(oid[2])) if isinstance(oid[2], SEQUENCE_TYPES) else oid[2])
    s += (' - ' + constant_to_oid_kind(oid[1])) if oid[1] is not None else ''
    s += (' - ' + oid[3]) if oid[3] else ''

    return s

# tuple structure: (oid, kind, name, docs)

# noinspection PyPep8
Oids = {  # administrative role
          '2.5.23.1': ('2.5.23.1', OID_ADMINISTRATIVE_ROLE, 'autonomousArea', 'RFC3672'),
          '2.5.23.2': ('2.5.23.2', OID_ADMINISTRATIVE_ROLE, 'accessControlSpecificArea', 'RFC3672'),
          '2.5.23.3': ('2.5.23.3', OID_ADMINISTRATIVE_ROLE, 'accessControlInnerArea', 'RFC3672'),
          '2.5.23.4': ('2.5.23.4', OID_ADMINISTRATIVE_ROLE, 'subschemaAdminSpecificArea', 'RFC3672'),
          '********': ('********', OID_ADMINISTRATIVE_ROLE, 'collectiveAttributeSpecificArea', 'RFC3672'),
          '********': ('********', OID_ADMINISTRATIVE_ROLE, 'collectiveAttributeInnerArea', 'RFC3672'),

          # attributes type
          '0.9.2342.********.100.1.1': ('0.9.2342.********.100.1.1', OID_ATTRIBUTE_TYPE, ['uid', 'userId'], 'RFC4519'),
          '0.9.2342.********.100.1.2': ('0.9.2342.********.100.1.2', OID_ATTRIBUTE_TYPE, 'textEncodedORAddress', 'RFC1274'),
          '0.9.2342.********.100.1.3': ('0.9.2342.********.100.1.3', OID_ATTRIBUTE_TYPE, ['mail', 'RFC822Mailbox'], 'RFC4524'),
          '0.9.2342.********.100.1.4': ('0.9.2342.********.100.1.4', OID_ATTRIBUTE_TYPE, 'info', 'RFC4524'),
          '0.9.2342.********.100.1.5': ('0.9.2342.********.100.1.5', OID_ATTRIBUTE_TYPE, ['drink', 'favouriteDrink'], 'RFC4524'),
          '0.9.2342.********.100.1.6': ('0.9.2342.********.100.1.6', OID_ATTRIBUTE_TYPE, 'roomNumber', 'RFC4524'),
          '0.9.2342.********.100.1.7': ('0.9.2342.********.100.1.7', OID_ATTRIBUTE_TYPE, 'photo', 'RFC1274'),
          '0.9.2342.********.100.1.8': ('0.9.2342.********.100.1.8', OID_ATTRIBUTE_TYPE, 'userClass', 'RFC4524'),
          '0.9.2342.********.100.1.9': ('0.9.2342.********.100.1.9', OID_ATTRIBUTE_TYPE, 'host', 'RFC4524'),
          '0.9.2342.********.100.1.10': ('0.9.2342.********.100.1.10', OID_ATTRIBUTE_TYPE, 'manager', 'RFC4524'),
          '0.9.2342.********.100.1.11': ('0.9.2342.********.100.1.11', OID_ATTRIBUTE_TYPE, 'documentIdentifier', 'RFC4524'),
          '0.9.2342.********.100.1.12': ('0.9.2342.********.100.1.12', OID_ATTRIBUTE_TYPE, 'documentTitle', 'RFC4524'),
          '0.9.2342.********.100.1.13': ('0.9.2342.********.100.1.13', OID_ATTRIBUTE_TYPE, 'documentVersion', 'RFC4524'),
          '0.9.2342.********.100.1.14': ('0.9.2342.********.100.1.14', OID_ATTRIBUTE_TYPE, 'documentAuthor', 'RFC4524'),
          '0.9.2342.********.100.1.15': ('0.9.2342.********.100.1.15', OID_ATTRIBUTE_TYPE, 'documentLocation', 'RFC4524'),
          '0.9.2342.********.100.1.20': ('0.9.2342.********.100.1.20', OID_ATTRIBUTE_TYPE, ['homePhone', 'homeTelephone'], 'RFC4524'),
          '0.9.2342.********.100.1.21': ('0.9.2342.********.100.1.21', OID_ATTRIBUTE_TYPE, 'secretary', 'RFC4524'),
          '0.9.2342.********.100.1.22': ('0.9.2342.********.100.1.22', OID_ATTRIBUTE_TYPE, 'otherMailbox', 'RFC1274'),
          '0.9.2342.********.100.1.23': ('0.9.2342.********.100.1.23', OID_ATTRIBUTE_TYPE, 'lastModifiedTime', 'RFC1274'),
          '0.9.2342.********.100.1.24': ('0.9.2342.********.100.1.24', OID_ATTRIBUTE_TYPE, 'lastModifiedBy', 'RFC1274'),
          '0.9.2342.********.100.1.25': ('0.9.2342.********.100.1.25', OID_ATTRIBUTE_TYPE, ['DC', 'domainComponent'], 'RFC4519'),
          '0.9.2342.********.100.1.26': ('0.9.2342.********.100.1.26', OID_ATTRIBUTE_TYPE, 'aRecord', 'RFC1274'),
          '0.9.2342.********.100.1.27': ('0.9.2342.********.100.1.27', OID_ATTRIBUTE_TYPE, 'mDRecord', 'RFC1274'),
          '0.9.2342.********.100.1.28': ('0.9.2342.********.100.1.28', OID_ATTRIBUTE_TYPE, 'mXRecord', 'RFC1274'),
          '0.9.2342.********.100.1.29': ('0.9.2342.********.100.1.29', OID_ATTRIBUTE_TYPE, 'nSRecord', 'RFC1274'),
          '0.9.2342.********.100.1.30': ('0.9.2342.********.100.1.30', OID_ATTRIBUTE_TYPE, 'sOARecord', 'RFC1274'),
          '0.9.2342.********.100.1.31': ('0.9.2342.********.100.1.31', OID_ATTRIBUTE_TYPE, 'cNAMERecord', 'RFC1274'),
          '0.9.2342.********.100.1.37': ('0.9.2342.********.100.1.37', OID_ATTRIBUTE_TYPE, 'associatedDomain', 'RFC4524'),
          '0.9.2342.********.100.1.38': ('0.9.2342.********.100.1.38', OID_ATTRIBUTE_TYPE, 'associatedName', 'RFC4524'),
          '0.9.2342.********.100.1.39': ('0.9.2342.********.100.1.39', OID_ATTRIBUTE_TYPE, 'homePostalAddress', 'RFC4524'),
          '0.9.2342.********.100.1.40': ('0.9.2342.********.100.1.40', OID_ATTRIBUTE_TYPE, 'personalTitle', 'RFC4524'),
          '0.9.2342.********.100.1.41': ('0.9.2342.********.100.1.41', OID_ATTRIBUTE_TYPE, ['mobile', 'mobileTelephoneNumber'], 'RFC4524'),
          '0.9.2342.********.100.1.42': ('0.9.2342.********.100.1.42', OID_ATTRIBUTE_TYPE, ['pager', 'pagerTelephoneNumber'], 'RFC4524'),
          '0.9.2342.********.100.1.43': ('0.9.2342.********.100.1.43', OID_ATTRIBUTE_TYPE, ['co', 'friendlyCountryName'], 'RFC4524'),
          '0.9.2342.********.100.1.44': ('0.9.2342.********.100.1.44', OID_ATTRIBUTE_TYPE, 'uniqueIdentifier', 'RFC4524'),
          '0.9.2342.********.100.1.45': ('0.9.2342.********.100.1.45', OID_ATTRIBUTE_TYPE, 'organizationalStatus', 'RFC4524'),
          '0.9.2342.********.100.1.46': ('0.9.2342.********.100.1.46', OID_ATTRIBUTE_TYPE, 'janetMailbox', 'RFC1274'),
          '0.9.2342.********.100.1.47': ('0.9.2342.********.100.1.47', OID_ATTRIBUTE_TYPE, 'mailPreferenceOption', 'RFC1274'),
          '0.9.2342.********.100.1.48': ('0.9.2342.********.100.1.48', OID_ATTRIBUTE_TYPE, 'buildingName', 'RFC4524'),
          '0.9.2342.********.100.1.49': ('0.9.2342.********.100.1.49', OID_ATTRIBUTE_TYPE, 'dSAQuality', 'RFC1274'),
          '0.9.2342.********.100.1.50': ('0.9.2342.********.100.1.50', OID_ATTRIBUTE_TYPE, 'singleLevelQuality', 'RFC4524'),
          '0.9.2342.********.100.1.51': ('0.9.2342.********.100.1.51', OID_ATTRIBUTE_TYPE, 'subtreeMinimumQuality', 'RFC1274'),
          '0.9.2342.********.100.1.52': ('0.9.2342.********.100.1.52', OID_ATTRIBUTE_TYPE, 'subtreeMaximumQuality', 'RFC1274'),
          '0.9.2342.********.100.1.53': ('0.9.2342.********.100.1.53', OID_ATTRIBUTE_TYPE, 'personalSignature', 'RFC1274'),
          '0.9.2342.********.100.1.54': ('0.9.2342.********.100.1.54', OID_ATTRIBUTE_TYPE, 'dITRedirect', 'RFC1274'),
          '0.9.2342.********.100.1.55': ('0.9.2342.********.100.1.55', OID_ATTRIBUTE_TYPE, 'audio', 'RFC1274'),
          '0.9.2342.********.100.1.56': ('0.9.2342.********.100.1.56', OID_ATTRIBUTE_TYPE, 'documentPublisher', 'RFC4524'),
          '0.9.2342.********.100.1.60': ('0.9.2342.********.100.1.60', OID_ATTRIBUTE_TYPE, 'jpegPhoto', 'RFC2798'),
          '1.2.840.113549.1.9.1': ('1.2.840.113549.1.9.1', OID_ATTRIBUTE_TYPE, ['email', 'emailAddress'], 'RFC3280'),
          '1.2.840.113556.1.4.478': ('1.2.840.113556.1.4.478', OID_ATTRIBUTE_TYPE, 'calCalURI', 'RFC2739'),
          '1.2.840.113556.1.4.479': ('1.2.840.113556.1.4.479', OID_ATTRIBUTE_TYPE, 'calFBURL', 'RFC2739'),
          '1.2.840.113556.1.4.480': ('1.2.840.113556.1.4.480', OID_ATTRIBUTE_TYPE, 'calCAPURI', 'RFC2739'),
          '1.2.840.113556.1.4.481': ('1.2.840.113556.1.4.481', OID_ATTRIBUTE_TYPE, 'calCalAdrURI', 'RFC2739'),
          '1.2.840.113556.1.4.482': ('1.2.840.113556.1.4.482', OID_ATTRIBUTE_TYPE, 'calOtherCalURIs', 'RFC2739'),
          '1.2.840.113556.1.4.483': ('1.2.840.113556.1.4.483', OID_ATTRIBUTE_TYPE, 'calOtherFBURLs', 'RFC2739'),
          '1.2.840.113556.1.4.484': ('1.2.840.113556.1.4.484', OID_ATTRIBUTE_TYPE, 'calOtherCAPURIs', 'RFC2739'),
          '1.2.840.113556.1.4.485': ('1.2.840.113556.1.4.485', OID_ATTRIBUTE_TYPE, 'calOtherCalAdrURIs', 'RFC2739'),
          '********.2.4.1107': ('********.2.4.1107', OID_ATTRIBUTE_TYPE, 'printer-xri-supported', 'RFC3712'),
          '********.2.4.1108': ('********.2.4.1108', OID_ATTRIBUTE_TYPE, 'printer-aliases', 'RFC3712'),
          '********.2.4.1109': ('********.2.4.1109', OID_ATTRIBUTE_TYPE, 'printer-charset-configured', 'RFC3712'),
          '********.2.4.1110': ('********.2.4.1110', OID_ATTRIBUTE_TYPE, 'printer-job-priority-supported', 'RFC3712'),
          '********.2.4.1111': ('********.2.4.1111', OID_ATTRIBUTE_TYPE, 'printer-job-k-octets-supported', 'RFC3712'),
          '********.2.4.1112': ('********.2.4.1112', OID_ATTRIBUTE_TYPE, 'printer-current-operator', 'RFC3712'),
          '********.2.4.1113': ('********.2.4.1113', OID_ATTRIBUTE_TYPE, 'printer-service-person', 'RFC3712'),
          '********.2.4.1114': ('********.2.4.1114', OID_ATTRIBUTE_TYPE, 'printer-delivery-orientation-supported', 'RFC3712'),
          '********.2.4.1115': ('********.2.4.1115', OID_ATTRIBUTE_TYPE, 'printer-stacking-order-supported', 'RFC3712'),
          '********.2.4.1116': ('********.2.4.1116', OID_ATTRIBUTE_TYPE, 'printer-output-features-supported', 'RFC3712'),
          '********.2.4.1117': ('********.2.4.1117', OID_ATTRIBUTE_TYPE, 'printer-media-local-supported', 'RFC3712'),
          '********.2.4.1118': ('********.2.4.1118', OID_ATTRIBUTE_TYPE, 'printer-copies-supported', 'RFC3712'),
          '********.2.4.1119': ('********.2.4.1119', OID_ATTRIBUTE_TYPE, 'printer-natural-language-configured', 'RFC3712'),
          '********.2.4.1120': ('********.2.4.1120', OID_ATTRIBUTE_TYPE, 'printer-print-quality-supported', 'RFC3712'),
          '********.2.4.1121': ('********.2.4.1121', OID_ATTRIBUTE_TYPE, 'printer-resolution-supported', 'RFC3712'),
          '********.2.4.1122': ('********.2.4.1122', OID_ATTRIBUTE_TYPE, 'printer-media-supported', 'RFC3712'),
          '********.2.4.1123': ('********.2.4.1123', OID_ATTRIBUTE_TYPE, 'printer-sides-supported', 'RFC3712'),
          '********.2.4.1124': ('********.2.4.1124', OID_ATTRIBUTE_TYPE, 'printer-number-up-supported', 'RFC3712'),
          '********.2.4.1125': ('********.2.4.1125', OID_ATTRIBUTE_TYPE, 'printer-finishings-supported', 'RFC3712'),
          '********.2.4.1126': ('********.2.4.1126', OID_ATTRIBUTE_TYPE, 'printer-pages-per-minute-color', 'RFC3712'),
          '********.2.4.1127': ('********.2.4.1127', OID_ATTRIBUTE_TYPE, 'printer-pages-per-minute', 'RFC3712'),
          '********.2.4.1128': ('********.2.4.1128', OID_ATTRIBUTE_TYPE, 'printer-compression-supported', 'RFC3712'),
          '********.2.4.1129': ('********.2.4.1129', OID_ATTRIBUTE_TYPE, 'printer-color-supported', 'RFC3712'),
          '********.2.4.1130': ('********.2.4.1130', OID_ATTRIBUTE_TYPE, 'printer-document-format-supported', 'RFC3712'),
          '********.2.4.1131': ('********.2.4.1131', OID_ATTRIBUTE_TYPE, 'printer-charset-supported', 'RFC3712'),
          '********.2.4.1132': ('********.2.4.1132', OID_ATTRIBUTE_TYPE, 'printer-multiple-document-jobs-supported', 'RFC3712'),
          '********.2.4.1133': ('********.2.4.1133', OID_ATTRIBUTE_TYPE, 'printer-ipp-versions-supported', 'RFC3712'),
          '********.2.4.1134': ('********.2.4.1134', OID_ATTRIBUTE_TYPE, 'printer-more-info', 'RFC3712'),
          '********.2.4.1135': ('********.2.4.1135', OID_ATTRIBUTE_TYPE, 'printer-name', 'RFC3712'),
          '********.2.4.1136': ('********.2.4.1136', OID_ATTRIBUTE_TYPE, 'printer-location', 'RFC3712'),
          '********.2.4.1137': ('********.2.4.1137', OID_ATTRIBUTE_TYPE, 'printer-generated-natural-language-supported', 'RFC3712'),
          '********.2.4.1138': ('********.2.4.1138', OID_ATTRIBUTE_TYPE, 'printer-make-and-model', 'RFC3712'),
          '********.2.4.1139': ('********.2.4.1139', OID_ATTRIBUTE_TYPE, 'printer-info', 'RFC3712'),
          '********.2.4.1140': ('********.2.4.1140', OID_ATTRIBUTE_TYPE, 'printer-uri', 'RFC3712'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiBusinessKey', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiAuthorizedName', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiOperator', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiName', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiDescription', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiDiscoveryURLs', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiUseType', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiPersonName', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'uddiPhone', 'RFC4403'),
          '*******.********0': ('*******.********0', OID_ATTRIBUTE_TYPE, 'uddiEMail', 'RFC4403'),
          '*******.********1': ('*******.********1', OID_ATTRIBUTE_TYPE, 'uddiSortCode', 'RFC4403'),
          '*******.********2': ('*******.********2', OID_ATTRIBUTE_TYPE, 'uddiTModelKey', 'RFC4403'),
          '*******.********3': ('*******.********3', OID_ATTRIBUTE_TYPE, 'uddiAddressLine', 'RFC4403'),
          '*******.********4': ('*******.********4', OID_ATTRIBUTE_TYPE, 'uddiIdentifierBag', 'RFC4403'),
          '*******.********5': ('*******.********5', OID_ATTRIBUTE_TYPE, 'uddiCategoryBag', 'RFC4403'),
          '*******.********6': ('*******.********6', OID_ATTRIBUTE_TYPE, 'uddiKeyedReference', 'RFC4403'),
          '*******.********7': ('*******.********7', OID_ATTRIBUTE_TYPE, 'uddiServiceKey', 'RFC4403'),
          '*******.********8': ('*******.********8', OID_ATTRIBUTE_TYPE, 'uddiBindingKey', 'RFC4403'),
          '*******.********9': ('*******.********9', OID_ATTRIBUTE_TYPE, 'uddiAccessPoint', 'RFC4403'),
          '*******.********0': ('*******.********0', OID_ATTRIBUTE_TYPE, 'uddiHostingRedirector', 'RFC4403'),
          '*******.********1': ('*******.********1', OID_ATTRIBUTE_TYPE, 'uddiInstanceDescription', 'RFC4403'),
          '*******.********2': ('*******.********2', OID_ATTRIBUTE_TYPE, 'uddiInstanceParms', 'RFC4403'),
          '*******.********3': ('*******.********3', OID_ATTRIBUTE_TYPE, 'uddiOverviewDescription', 'RFC4403'),
          '*******.********4': ('*******.********4', OID_ATTRIBUTE_TYPE, 'uddiOverviewURL', 'RFC4403'),
          '*******.********5': ('*******.********5', OID_ATTRIBUTE_TYPE, 'uddiFromKey', 'RFC4403'),
          '*******.********6': ('*******.********6', OID_ATTRIBUTE_TYPE, 'uddiToKey', 'RFC4403'),
          '*******.********7': ('*******.********7', OID_ATTRIBUTE_TYPE, 'uddiUUID', 'RFC4403'),
          '*******.********8': ('*******.********8', OID_ATTRIBUTE_TYPE, 'uddiIsHidden', 'RFC4403'),
          '*******.********9': ('*******.********9', OID_ATTRIBUTE_TYPE, 'uddiIsProjection', 'RFC4403'),
          '*******.********0': ('*******.********0', OID_ATTRIBUTE_TYPE, 'uddiLang', 'RFC4403'),
          '*******.********1': ('*******.********1', OID_ATTRIBUTE_TYPE, 'uddiv3BusinessKey', 'RFC4403'),
          '*******.********2': ('*******.********2', OID_ATTRIBUTE_TYPE, 'uddiv3ServiceKey', 'RFC4403'),
          '*******.********3': ('*******.********3', OID_ATTRIBUTE_TYPE, 'uddiv3BindingKey', 'RFC4403'),
          '*******.********4': ('*******.********4', OID_ATTRIBUTE_TYPE, 'uddiv3TmodelKey', 'RFC4403'),
          '*******.********5': ('*******.********5', OID_ATTRIBUTE_TYPE, 'uddiv3DigitalSignature', 'RFC4403'),
          '*******.********6': ('*******.********6', OID_ATTRIBUTE_TYPE, 'uddiv3NodeId', 'RFC4403'),
          '*******.********7': ('*******.********7', OID_ATTRIBUTE_TYPE, 'uddiv3EntityModificationTime', 'RFC4403'),
          '*******.********8': ('*******.********8', OID_ATTRIBUTE_TYPE, 'uddiv3SubscriptionKey', 'RFC4403'),
          '*******.********9': ('*******.********9', OID_ATTRIBUTE_TYPE, 'uddiv3SubscriptionFilter', 'RFC4403'),
          '*******.********0': ('*******.********0', OID_ATTRIBUTE_TYPE, 'uddiv3NotificationInterval', 'RFC4403'),
          '*******.********1': ('*******.********1', OID_ATTRIBUTE_TYPE, 'uddiv3MaxEntities', 'RFC4403'),
          '*******.********2': ('*******.********2', OID_ATTRIBUTE_TYPE, 'uddiv3ExpiresAfter', 'RFC4403'),
          '*******.********3': ('*******.********3', OID_ATTRIBUTE_TYPE, 'uddiv3BriefResponse', 'RFC4403'),
          '*******.********4': ('*******.********4', OID_ATTRIBUTE_TYPE, 'uddiv3EntityKey', 'RFC4403'),
          '*******.********5': ('*******.********5', OID_ATTRIBUTE_TYPE, 'uddiv3EntityCreationTime', 'RFC4403'),
          '*******.********6': ('*******.********6', OID_ATTRIBUTE_TYPE, 'uddiv3EntityDeletionTime', 'RFC4403'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'vPIMTelephoneNumber', 'RFC4237'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'vPIMRfc822Mailbox', 'RFC4237'),
          '*******.1.11.2.3': ('*******.1.11.2.3', OID_ATTRIBUTE_TYPE, 'vPIMSpokenName', 'RFC4237'),
          '*******.1.11.2.4': ('*******.1.11.2.4', OID_ATTRIBUTE_TYPE, 'vPIMSupportedUABehaviors', 'RFC4237'),
          '*******.1.11.2.5': ('*******.1.11.2.5', OID_ATTRIBUTE_TYPE, 'vPIMSupportedAudioMediaTypes', 'RFC4237'),
          '*******.1.11.2.6': ('*******.1.11.2.6', OID_ATTRIBUTE_TYPE, 'vPIMSupportedMessageContext', 'RFC4237'),
          '*******.1.11.2.7': ('*******.1.11.2.7', OID_ATTRIBUTE_TYPE, 'vPIMTextName', 'RFC4237'),
          '*******.1.11.2.8': ('*******.1.11.2.8', OID_ATTRIBUTE_TYPE, 'vPIMExtendedAbsenceStatus', 'RFC4237'),
          '*******.1.11.2.9': ('*******.1.11.2.9', OID_ATTRIBUTE_TYPE, 'vPIMMaxMessageSize', 'RFC4237'),
          '*******.********0': ('*******.********0', OID_ATTRIBUTE_TYPE, 'vPIMSubMailboxes', 'RFC4237'),
          '*******.1.16.4': ('*******.1.16.4', OID_ATTRIBUTE_TYPE, 'entryUUID', 'RFC4530'),
          '*******.1.20': ('*******.1.20', OID_ATTRIBUTE_TYPE, 'entryDN', 'RFC5020'),
          '*******.1.6.2.3': ('*******.1.6.2.3', OID_ATTRIBUTE_TYPE, 'pcimKeywords', 'RFC3703'),
          '*******.1.6.2.4': ('*******.1.6.2.4', OID_ATTRIBUTE_TYPE, 'pcimGroupName', 'RFC3703'),
          '*******.1.6.2.5': ('*******.1.6.2.5', OID_ATTRIBUTE_TYPE, 'pcimRuleName', 'RFC3703'),
          '*******.1.6.2.6': ('*******.1.6.2.6', OID_ATTRIBUTE_TYPE, 'pcimRuleEnabled', 'RFC3703'),
          '*******.1.6.2.7': ('*******.1.6.2.7', OID_ATTRIBUTE_TYPE, 'pcimRuleConditionListType', 'RFC3703'),
          '*******.1.6.2.8': ('*******.1.6.2.8', OID_ATTRIBUTE_TYPE, 'pcimRuleConditionList', 'RFC3703'),
          '*******.1.6.2.9': ('*******.1.6.2.9', OID_ATTRIBUTE_TYPE, 'pcimRuleActionList', 'RFC3703'),
          '*******.1.6.2.10': ('*******.1.6.2.10', OID_ATTRIBUTE_TYPE, 'pcimRuleValidityPeriodList', 'RFC3703'),
          '*******.1.6.2.11': ('*******.1.6.2.11', OID_ATTRIBUTE_TYPE, 'pcimRuleUsage', 'RFC3703'),
          '*******.1.6.2.12': ('*******.1.6.2.12', OID_ATTRIBUTE_TYPE, 'pcimRulePriority', 'RFC3703'),
          '*******.1.6.2.13': ('*******.1.6.2.13', OID_ATTRIBUTE_TYPE, 'pcimRuleMandatory', 'RFC3703'),
          '*******.1.6.2.14': ('*******.1.6.2.14', OID_ATTRIBUTE_TYPE, 'pcimRuleSequencedActions', 'RFC3703'),
          '*******.1.6.2.15': ('*******.1.6.2.15', OID_ATTRIBUTE_TYPE, 'pcimRoles', 'RFC3703'),
          '*******.1.6.2.16': ('*******.1.6.2.16', OID_ATTRIBUTE_TYPE, 'pcimConditionGroupNumber', 'RFC3703'),
          '*******.1.6.2.17': ('*******.1.6.2.17', OID_ATTRIBUTE_TYPE, 'pcimConditionNegated', 'RFC3703'),
          '*******.1.6.2.18': ('*******.1.6.2.18', OID_ATTRIBUTE_TYPE, 'pcimConditionName', 'RFC3703'),
          '*******.1.6.2.19': ('*******.1.6.2.19', OID_ATTRIBUTE_TYPE, 'pcimConditionDN', 'RFC3703'),
          '*******.1.6.2.20': ('*******.1.6.2.20', OID_ATTRIBUTE_TYPE, 'pcimValidityConditionName', 'RFC3703'),
          '*******.1.6.2.21': ('*******.1.6.2.21', OID_ATTRIBUTE_TYPE, 'pcimTimePeriodConditionDN', 'RFC3703'),
          '*******.1.6.2.22': ('*******.1.6.2.22', OID_ATTRIBUTE_TYPE, 'pcimActionName', 'RFC3703'),
          '*******.1.6.2.23': ('*******.1.6.2.23', OID_ATTRIBUTE_TYPE, 'pcimActionOrder', 'RFC3703'),
          '*******.1.6.2.24': ('*******.1.6.2.24', OID_ATTRIBUTE_TYPE, 'pcimActionDN', 'RFC3703'),
          '*******.1.6.2.25': ('*******.1.6.2.25', OID_ATTRIBUTE_TYPE, 'pcimTPCTime', 'RFC3703'),
          '*******.1.6.2.26': ('*******.1.6.2.26', OID_ATTRIBUTE_TYPE, 'pcimTPCMonthOfYearMask', 'RFC3703'),
          '*******.1.6.2.27': ('*******.1.6.2.27', OID_ATTRIBUTE_TYPE, 'pcimTPCDayOfMonthMask', 'RFC3703'),
          '*******.1.6.2.28': ('*******.1.6.2.28', OID_ATTRIBUTE_TYPE, 'pcimTPCDayOfWeekMask', 'RFC3703'),
          '*******.1.6.2.29': ('*******.1.6.2.29', OID_ATTRIBUTE_TYPE, 'pcimTPCTimeOfDayMask', 'RFC3703'),
          '*******.1.6.2.30': ('*******.1.6.2.30', OID_ATTRIBUTE_TYPE, 'pcimTPCLocalOrUtcTime', 'RFC3703'),
          '*******.1.6.2.31': ('*******.1.6.2.31', OID_ATTRIBUTE_TYPE, 'pcimVendorConstraintData', 'RFC3703'),
          '*******.1.6.2.32': ('*******.1.6.2.32', OID_ATTRIBUTE_TYPE, 'pcimVendorConstraintEncoding', 'RFC3703'),
          '*******.1.6.2.33': ('*******.1.6.2.33', OID_ATTRIBUTE_TYPE, 'pcimVendorActionData', 'RFC3703'),
          '*******.1.6.2.34': ('*******.1.6.2.34', OID_ATTRIBUTE_TYPE, 'pcimVendorActionEncoding', 'RFC3703'),
          '*******.1.6.2.35': ('*******.1.6.2.35', OID_ATTRIBUTE_TYPE, 'pcimPolicyInstanceName', 'RFC3703'),
          '*******.1.6.2.36': ('*******.1.6.2.36', OID_ATTRIBUTE_TYPE, 'pcimRepositoryName', 'RFC3703'),
          '*******.1.6.2.37': ('*******.1.6.2.37', OID_ATTRIBUTE_TYPE, 'pcimSubtreesAuxContainedSet', 'RFC3703'),
          '*******.1.6.2.38': ('*******.1.6.2.38', OID_ATTRIBUTE_TYPE, 'pcimGroupsAuxContainedSet', 'RFC3703'),
          '*******.1.6.2.39': ('*******.1.6.2.39', OID_ATTRIBUTE_TYPE, 'pcimRulesAuxContainedSet', 'RFC3703'),
          '*******.1.9.2.1': ('*******.1.9.2.1', OID_ATTRIBUTE_TYPE, 'pcelsPolicySetName', 'RFC4104'),
          '*******.1.9.2.2': ('*******.1.9.2.2', OID_ATTRIBUTE_TYPE, 'pcelsDecisionStrategy', 'RFC4104'),
          '*******.1.9.2.3': ('*******.1.9.2.3', OID_ATTRIBUTE_TYPE, 'pcelsPolicySetList', 'RFC4104'),
          '*******.1.9.2.4': ('*******.1.9.2.4', OID_ATTRIBUTE_TYPE, 'pcelsPriority', 'RFC4104'),
          '*******.1.9.2.5': ('*******.1.9.2.5', OID_ATTRIBUTE_TYPE, 'pcelsPolicySetDN', 'RFC4104'),
          '*******.1.9.2.6': ('*******.1.9.2.6', OID_ATTRIBUTE_TYPE, 'pcelsConditionListType', 'RFC4104'),
          '*******.1.9.2.7': ('*******.1.9.2.7', OID_ATTRIBUTE_TYPE, 'pcelsConditionList', 'RFC4104'),
          '*******.1.9.2.8': ('*******.1.9.2.8', OID_ATTRIBUTE_TYPE, 'pcelsActionList', 'RFC4104'),
          '*******.1.9.2.9': ('*******.1.9.2.9', OID_ATTRIBUTE_TYPE, 'pcelsSequencedActions', 'RFC4104'),
          '*******.1.9.2.10': ('*******.1.9.2.10', OID_ATTRIBUTE_TYPE, 'pcelsExecutionStrategy', 'RFC4104'),
          '*******.1.9.2.11': ('*******.1.9.2.11', OID_ATTRIBUTE_TYPE, 'pcelsVariableDN', 'RFC4104'),
          '*******.1.9.2.12': ('*******.1.9.2.12', OID_ATTRIBUTE_TYPE, 'pcelsValueDN', 'RFC4104'),
          '*******.1.9.2.13': ('*******.1.9.2.13', OID_ATTRIBUTE_TYPE, 'pcelsIsMirrored', 'RFC4104'),
          '*******.1.9.2.14': ('*******.1.9.2.14', OID_ATTRIBUTE_TYPE, 'pcelsVariableName', 'RFC4104'),
          '*******.1.9.2.15': ('*******.1.9.2.15', OID_ATTRIBUTE_TYPE, 'pcelsExpectedValueList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsVariableModelClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsVariableModelProperty', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsExpectedValueTypes', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsValueName', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPv4AddrList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPv6AddrList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsMACAddrList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsStringList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsBitStringList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIntegerList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsBoolean', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsReusableContainerName', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsReusableContainerList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsRole', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsRoleCollectionName', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsElementList', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsFilterName', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsFilterIsNegated', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrVersion', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrSourceAddress', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrSourceAddressEndOfRange', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrSourceMask', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrDestAddress', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrDestAddressEndOfRange', 'RFC4104'),
          '*******.********': ('*******.********', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrDestMask', 'RFC4104'),
          '*******.1.9.2.41': ('*******.1.9.2.41', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrProtocolID', 'RFC4104'),
          '*******.1.9.2.42': ('*******.1.9.2.42', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrSourcePortStart', 'RFC4104'),
          '*******.1.9.2.43': ('*******.1.9.2.43', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrSourcePortEnd', 'RFC4104'),
          '*******.1.9.2.44': ('*******.1.9.2.44', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrDestPortStart', 'RFC4104'),
          '*******.1.9.2.45': ('*******.1.9.2.45', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrDestPortEnd', 'RFC4104'),
          '*******.1.9.2.46': ('*******.1.9.2.46', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrDSCPList', 'RFC4104'),
          '*******.1.9.2.47': ('*******.1.9.2.47', OID_ATTRIBUTE_TYPE, 'pcelsIPHdrFlowLabel', 'RFC4104'),
          '*******.1.9.2.48': ('*******.1.9.2.48', OID_ATTRIBUTE_TYPE, 'pcels8021HdrSourceMACAddress', 'RFC4104'),
          '*******.1.9.2.49': ('*******.1.9.2.49', OID_ATTRIBUTE_TYPE, 'pcels8021HdrSourceMACMask', 'RFC4104'),
          '*******.1.9.2.50': ('*******.1.9.2.50', OID_ATTRIBUTE_TYPE, 'pcels8021HdrDestMACAddress', 'RFC4104'),
          '*******.1.9.2.51': ('*******.1.9.2.51', OID_ATTRIBUTE_TYPE, 'pcels8021HdrDestMACMask', 'RFC4104'),
          '*******.1.9.2.52': ('*******.1.9.2.52', OID_ATTRIBUTE_TYPE, 'pcels8021HdrProtocolID', 'RFC4104'),
          '*******.1.9.2.53': ('*******.1.9.2.53', OID_ATTRIBUTE_TYPE, 'pcels8021HdrPriority', 'RFC4104'),
          '*******.1.9.2.54': ('*******.1.9.2.54', OID_ATTRIBUTE_TYPE, 'pcels8021HdrVLANID', 'RFC4104'),
          '*******.1.9.2.55': ('*******.1.9.2.55', OID_ATTRIBUTE_TYPE, 'pcelsFilterListName', 'RFC4104'),
          '*******.1.9.2.56': ('*******.1.9.2.56', OID_ATTRIBUTE_TYPE, 'pcelsFilterDirection', 'RFC4104'),
          '*******.1.9.2.57': ('*******.1.9.2.57', OID_ATTRIBUTE_TYPE, 'pcelsFilterEntryList', 'RFC4104'),
          '*******.1.9.2.58': ('*******.1.9.2.58', OID_ATTRIBUTE_TYPE, 'pcelsVendorVariableData', 'RFC4104'),
          '*******.1.9.2.59': ('*******.1.9.2.59', OID_ATTRIBUTE_TYPE, 'pcelsVendorVariableEncoding', 'RFC4104'),
          '*******.1.9.2.60': ('*******.1.9.2.60', OID_ATTRIBUTE_TYPE, 'pcelsVendorValueData', 'RFC4104'),
          '*******.1.9.2.61': ('*******.1.9.2.61', OID_ATTRIBUTE_TYPE, 'pcelsVendorValueEncoding', 'RFC4104'),
          '*******.1.9.2.62': ('*******.1.9.2.62', OID_ATTRIBUTE_TYPE, 'pcelsRuleValidityPeriodList', 'RFC4104'),
          '*******.********.3.1.1.0': ('*******.********.3.1.1.0', OID_ATTRIBUTE_TYPE, 'defaultServerList', 'RFC4876'),
          '*******.********.3.1.1.1': ('*******.********.3.1.1.1', OID_ATTRIBUTE_TYPE, 'defaultSearchBase', 'RFC4876'),
          '*******.********.3.1.1.2': ('*******.********.3.1.1.2', OID_ATTRIBUTE_TYPE, 'preferredServerList', 'RFC4876'),
          '*******.********.3.1.1.3': ('*******.********.3.1.1.3', OID_ATTRIBUTE_TYPE, 'search_time_limit', 'RFC4876'),
          '*******.********.3.1.1.4': ('*******.********.3.1.1.4', OID_ATTRIBUTE_TYPE, 'bindTimeLimit', 'RFC4876'),
          '*******.********.3.1.1.5': ('*******.********.3.1.1.5', OID_ATTRIBUTE_TYPE, 'followReferrals', 'RFC4876'),
          '*******.********.3.1.1.6': ('*******.********.3.1.1.6', OID_ATTRIBUTE_TYPE, 'authenticationMethod', 'RFC4876'),
          '*******.********.3.1.1.7': ('*******.********.3.1.1.7', OID_ATTRIBUTE_TYPE, 'profileTTL', 'RFC4876'),
          '*******.********.3.1.1.9': ('*******.********.3.1.1.9', OID_ATTRIBUTE_TYPE, 'attributeMap', 'RFC4876'),
          '*******.********.3.1.1.10': ('*******.********.3.1.1.10', OID_ATTRIBUTE_TYPE, 'credentialLevel', 'RFC4876'),
          '*******.********.********': ('*******.********.********', OID_ATTRIBUTE_TYPE, 'objectclassMap', 'RFC4876'),
          '*******.********.********': ('*******.********.********', OID_ATTRIBUTE_TYPE, 'defaultSearchScope', 'RFC4876'),
          '*******.********.********': ('*******.********.********', OID_ATTRIBUTE_TYPE, 'serviceCredentialLevel', 'RFC4876'),
          '*******.********.********': ('*******.********.********', OID_ATTRIBUTE_TYPE, 'serviceSearchDescriptor', 'RFC4876'),
          '*******.********.********': ('*******.********.********', OID_ATTRIBUTE_TYPE, 'serviceAuthenticationMethod', 'RFC4876'),
          '*******.********.********': ('*******.********.********', OID_ATTRIBUTE_TYPE, 'dereferenceAliases', 'RFC4876'),
          '*******.4.1.1466.101.119.3': ('*******.4.1.1466.101.119.3', OID_ATTRIBUTE_TYPE, 'entryTtl', 'RFC2589'),
          '*******.4.1.1466.101.119.4': ('*******.4.1.1466.101.119.4', OID_ATTRIBUTE_TYPE, 'dynamicSubtrees', 'RFC2589'),
          '*******.4.1.1466.101.120.1': ('*******.4.1.1466.101.120.1', OID_ATTRIBUTE_TYPE, 'administratorsAddress', 'Mark_Wahl'),
          '*******.4.1.1466.101.120.5': ('*******.4.1.1466.101.120.5', OID_ATTRIBUTE_TYPE, 'namingContexts', 'RFC4512'),
          '*******.4.1.1466.101.120.6': ('*******.4.1.1466.101.120.6', OID_ATTRIBUTE_TYPE, 'altServer', 'RFC4512'),
          '*******.4.1.1466.101.120.7': ('*******.4.1.1466.101.120.7', OID_ATTRIBUTE_TYPE, 'supportedExtension', 'RFC4512'),
          '*******.4.1.1466.101.120.13': ('*******.4.1.1466.101.120.13', OID_ATTRIBUTE_TYPE, 'supportedControl', 'RFC4512'),
          '*******.4.1.1466.101.120.14': ('*******.4.1.1466.101.120.14', OID_ATTRIBUTE_TYPE, 'supportedSASLMechanisms', 'RFC4512'),
          '*******.4.1.1466.101.120.15': ('*******.4.1.1466.101.120.15', OID_ATTRIBUTE_TYPE, 'supportedLDAPVersion', 'RFC4512'),
          '*******.4.1.1466.101.120.16': ('*******.4.1.1466.101.120.16', OID_ATTRIBUTE_TYPE, 'ldapSyntaxes', 'RFC4512'),
          '*******.4.1.16572.2.2.1': ('*******.4.1.16572.2.2.1', OID_ATTRIBUTE_TYPE, 'providerCertificateHash', 'RFC6109'),
          '*******.4.1.16572.2.2.2': ('*******.4.1.16572.2.2.2', OID_ATTRIBUTE_TYPE, 'providerCertificate', 'RFC6109'),
          '*******.4.1.16572.2.2.3': ('*******.4.1.16572.2.2.3', OID_ATTRIBUTE_TYPE, 'providerName', 'RFC6109'),
          '*******.4.1.16572.2.2.4': ('*******.4.1.16572.2.2.4', OID_ATTRIBUTE_TYPE, 'mailReceipt', 'RFC6109'),
          '*******.4.1.16572.2.2.5': ('*******.4.1.16572.2.2.5', OID_ATTRIBUTE_TYPE, 'managedDomains', 'RFC6109'),
          '*******.4.1.16572.2.2.6': ('*******.4.1.16572.2.2.6', OID_ATTRIBUTE_TYPE, 'LDIFLocationURL', 'RFC6109'),
          '*******.4.1.16572.2.2.7': ('*******.4.1.16572.2.2.7', OID_ATTRIBUTE_TYPE, 'providerUnit', 'RFC6109'),
          '*******.*********.57': ('*******.*********.57', OID_ATTRIBUTE_TYPE, 'labeledURI', 'RFC2079'),
          '*******.4.1.31103.1.1': ('*******.4.1.31103.1.1', OID_ATTRIBUTE_TYPE, 'fedfsUuid', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.2': ('*******.4.1.31103.1.2', OID_ATTRIBUTE_TYPE, 'fedfsNetAddr', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.3': ('*******.4.1.31103.1.3', OID_ATTRIBUTE_TYPE, 'fedfsNetPort', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.4': ('*******.4.1.31103.1.4', OID_ATTRIBUTE_TYPE, 'fedfsFsnUuid', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.5': ('*******.4.1.31103.1.5', OID_ATTRIBUTE_TYPE, 'fedfsNsdbName', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.6': ('*******.4.1.31103.1.6', OID_ATTRIBUTE_TYPE, 'fedfsNsdbPort', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.7': ('*******.4.1.31103.1.7', OID_ATTRIBUTE_TYPE, 'fedfsNcePrefix', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.8': ('*******.4.1.31103.1.8', OID_ATTRIBUTE_TYPE, 'fedfsFslUuid', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.9': ('*******.4.1.31103.1.9', OID_ATTRIBUTE_TYPE, 'fedfsFslHost', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.10': ('*******.4.1.31103.1.10', OID_ATTRIBUTE_TYPE, 'fedfsFslPort', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.11': ('*******.4.1.31103.1.11', OID_ATTRIBUTE_TYPE, 'fedfsFslTTL', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.12': ('*******.4.1.31103.1.12', OID_ATTRIBUTE_TYPE, 'fedfsAnnotation', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.13': ('*******.4.1.31103.1.13', OID_ATTRIBUTE_TYPE, 'fedfsDescr', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.14': ('*******.4.1.31103.1.14', OID_ATTRIBUTE_TYPE, 'fedfsNceDN', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.15': ('*******.4.1.31103.1.15', OID_ATTRIBUTE_TYPE, 'fedfsFsnTTL', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.100': ('*******.4.1.31103.1.100', OID_ATTRIBUTE_TYPE, 'fedfsNfsPath', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.101': ('*******.4.1.31103.1.101', OID_ATTRIBUTE_TYPE, 'fedfsNfsMajorVer', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.102': ('*******.4.1.31103.1.102', OID_ATTRIBUTE_TYPE, 'fedfsNfsMinorVer', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.103': ('*******.4.1.31103.1.103', OID_ATTRIBUTE_TYPE, 'fedfsNfsCurrency', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.104': ('*******.4.1.31103.1.104', OID_ATTRIBUTE_TYPE, 'fedfsNfsGenFlagWritable', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.105': ('*******.4.1.31103.1.105', OID_ATTRIBUTE_TYPE, 'fedfsNfsGenFlagGoing', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.106': ('*******.4.1.31103.1.106', OID_ATTRIBUTE_TYPE, 'fedfsNfsGenFlagSplit', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.107': ('*******.4.1.31103.1.107', OID_ATTRIBUTE_TYPE, 'fedfsNfsTransFlagRdma', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.108': ('*******.4.1.31103.1.108', OID_ATTRIBUTE_TYPE, 'fedfsNfsClassSimul', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.109': ('*******.4.1.31103.1.109', OID_ATTRIBUTE_TYPE, 'fedfsNfsClassHandle', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.110': ('*******.4.1.31103.1.110', OID_ATTRIBUTE_TYPE, 'fedfsNfsClassFileid', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.111': ('*******.4.1.31103.1.111', OID_ATTRIBUTE_TYPE, 'fedfsNfsClassWritever', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.112': ('*******.4.1.31103.1.112', OID_ATTRIBUTE_TYPE, 'fedfsNfsClassChange', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.113': ('*******.4.1.31103.1.113', OID_ATTRIBUTE_TYPE, 'fedfsNfsClassReaddir', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.114': ('*******.4.1.31103.1.114', OID_ATTRIBUTE_TYPE, 'fedfsNfsReadRank', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.115': ('*******.4.1.31103.1.115', OID_ATTRIBUTE_TYPE, 'fedfsNfsReadOrder', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.116': ('*******.4.1.31103.1.116', OID_ATTRIBUTE_TYPE, 'fedfsNfsWriteRank', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.117': ('*******.4.1.31103.1.117', OID_ATTRIBUTE_TYPE, 'fedfsNfsWriteOrder', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.118': ('*******.4.1.31103.1.118', OID_ATTRIBUTE_TYPE, 'fedfsNfsVarSub', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.119': ('*******.4.1.31103.1.119', OID_ATTRIBUTE_TYPE, 'fedfsNfsValidFor', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.120': ('*******.4.1.31103.1.120', OID_ATTRIBUTE_TYPE, 'fedfsNfsURI', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.4203.1.3.5': ('*******.4.1.4203.1.3.5', OID_ATTRIBUTE_TYPE, 'supportedFeatures', 'RFC4512'),
          '*******.4.1.453.7.2.1': ('*******.4.1.453.7.2.1', OID_ATTRIBUTE_TYPE, 'textTableKey', 'RFC2293'),
          '*******.4.1.453.7.2.2': ('*******.4.1.453.7.2.2', OID_ATTRIBUTE_TYPE, 'textTableValue', 'RFC2293'),
          '*******.4.1.453.7.2.3': ('*******.4.1.453.7.2.3', OID_ATTRIBUTE_TYPE, ['associatedX400Gateway', 'distinguishedNameTableKey'], 'RFC2164-RFC2293'),
          '*******.4.1.453.7.2.6': ('*******.4.1.453.7.2.6', OID_ATTRIBUTE_TYPE, 'associatedORAddress', 'RFC2164'),
          '*******.4.1.453.7.2.7': ('*******.4.1.453.7.2.7', OID_ATTRIBUTE_TYPE, 'oRAddressComponentType', 'RFC2164'),
          '*******.4.1.453.7.2.8': ('*******.4.1.453.7.2.8', OID_ATTRIBUTE_TYPE, 'associatedInternetGateway', 'RFC2164'),
          '*******.4.1.453.7.2.9': ('*******.4.1.453.7.2.9', OID_ATTRIBUTE_TYPE, 'mcgamTables', 'RFC2164'),
          '2.16.840.1.113730.3.1.34': ('2.16.840.1.113730.3.1.34', OID_ATTRIBUTE_TYPE, 'ref', 'RFC3296'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'createTimestamp', 'RFC4512'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'modifyTimestamp', 'RFC4512'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'creatorsName', 'RFC4512'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'modifiersName', 'RFC4512'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'administrativeRole', 'RFC3672'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'subtreeSpecification', 'RFC3672'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'collectiveExclusions', 'RFC3671'),
          '********0': ('********0', OID_ATTRIBUTE_TYPE, 'subschemaSubentry', 'RFC4512'),
          '********2': ('********2', OID_ATTRIBUTE_TYPE, 'collectiveAttributeSubentries', 'RFC3671'),
          '2.5.21.1': ('2.5.21.1', OID_ATTRIBUTE_TYPE, 'dITStructureRules', 'RFC4512'),
          '2.5.21.2': ('2.5.21.2', OID_ATTRIBUTE_TYPE, 'dITContentRules', 'RFC4512'),
          '2.5.21.4': ('2.5.21.4', OID_ATTRIBUTE_TYPE, 'matchingRules', 'RFC4512'),
          '2.5.21.5': ('2.5.21.5', OID_ATTRIBUTE_TYPE, 'attributeTypes', 'RFC4512'),
          '2.5.21.6': ('2.5.21.6', OID_ATTRIBUTE_TYPE, 'objectClasses', 'RFC4512'),
          '2.5.21.7': ('2.5.21.7', OID_ATTRIBUTE_TYPE, 'nameForms', 'RFC4512'),
          '2.5.21.8': ('2.5.21.8', OID_ATTRIBUTE_TYPE, 'matchingRuleUse', 'RFC4512'),
          '2.5.21.9': ('2.5.21.9', OID_ATTRIBUTE_TYPE, 'structuralObjectClass', 'RFC4512'),
          '2.5.21.10': ('2.5.21.10', OID_ATTRIBUTE_TYPE, 'governingStructureRule', 'RFC4512'),
          '2.5.4.0': ('2.5.4.0', OID_ATTRIBUTE_TYPE, 'objectClass', 'RFC4512'),
          '2.5.4.1': ('2.5.4.1', OID_ATTRIBUTE_TYPE, ['aliasedEntryName', 'aliasedObjectName'], 'X.501-RFC4512'),
          '2.5.4.2': ('2.5.4.2', OID_ATTRIBUTE_TYPE, 'knowledgeInformation', 'RFC2256'),
          '2.5.4.3': ('2.5.4.3', OID_ATTRIBUTE_TYPE, ['cn', 'commonName'], 'RFC4519'),
          '2.5.4.4': ('2.5.4.4', OID_ATTRIBUTE_TYPE, ['sn', 'surname'], 'RFC4519'),
          '2.5.4.5': ('2.5.4.5', OID_ATTRIBUTE_TYPE, 'serialNumber', 'RFC4519'),
          '*******': ('*******', OID_ATTRIBUTE_TYPE, ['c', 'countryName'], 'RFC4519'),
          '*******': ('*******', OID_ATTRIBUTE_TYPE, ['L', 'localityName'], 'RFC4519'),
          '*******.1': ('*******.1', OID_ATTRIBUTE_TYPE, 'c-l', 'RFC3671'),
          '*******': ('*******', OID_ATTRIBUTE_TYPE, ['st', 'stateOrProvinceName'], 'RFC4519-RFC2256'),
          '*******.1': ('*******.1', OID_ATTRIBUTE_TYPE, 'c-st', 'RFC3671'),
          '*******': ('*******', OID_ATTRIBUTE_TYPE, ['street', 'streetAddress'], 'RFC4519-RFC2256'),
          '*******.1': ('*******.1', OID_ATTRIBUTE_TYPE, 'c-street', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, ['o', 'organizationName'], 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-o', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, ['ou', 'organizationalUnitName'], 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-ou', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'title', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'description', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'searchGuide', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'businessCategory', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'postalAddress', 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-PostalAddress', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'postalCode', 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-PostalCode', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'postOfficeBox', 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-PostOfficeBox', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'physicalDeliveryOfficeName', 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-PhysicalDeliveryOffice', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'telephoneNumber', 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-TelephoneNumber', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'telexNumber', 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-TelexNumber', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'teletexTerminalIdentifier', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'facsimileTelephoneNumber', 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-FacsimileTelephoneNumber', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'x121Address', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'internationaliSDNNumber', 'RFC4519'),
          '********.1': ('********.1', OID_ATTRIBUTE_TYPE, 'c-InternationalISDNNumber', 'RFC3671'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'registeredAddress', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'destinationIndicator', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'preferredDeliveryMethod', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'presentationAddress', 'RFC2256'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'supportedApplicationContext', 'RFC2256'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'member', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'owner', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'roleOccupant', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'seeAlso', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'userPassword', 'RFC4519'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'userCertificate', 'RFC4523'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'cACertificate', 'RFC4523'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'authorityRevocationList', 'RFC4523'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'certificateRevocationList', 'RFC4523'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'crossCertificatePair', 'RFC4523'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'name', 'RFC4519'),
          '2.5.4.42': ('2.5.4.42', OID_ATTRIBUTE_TYPE, 'givenName', 'RFC4519'),
          '2.5.4.43': ('2.5.4.43', OID_ATTRIBUTE_TYPE, 'initials', 'RFC4519'),
          '2.5.4.44': ('2.5.4.44', OID_ATTRIBUTE_TYPE, 'generationQualifier', 'RFC4519'),
          '2.5.4.45': ('2.5.4.45', OID_ATTRIBUTE_TYPE, 'x500UniqueIdentifier', 'RFC4519'),
          '2.5.4.46': ('2.5.4.46', OID_ATTRIBUTE_TYPE, 'dnQualifier', 'RFC4519'),
          '2.5.4.47': ('2.5.4.47', OID_ATTRIBUTE_TYPE, 'enhancedSearchGuide', 'RFC4519'),
          '2.5.4.48': ('2.5.4.48', OID_ATTRIBUTE_TYPE, 'protocolInformation', 'RFC2256'),
          '2.5.4.49': ('2.5.4.49', OID_ATTRIBUTE_TYPE, 'distinguishedName', 'RFC4519'),
          '2.5.4.50': ('2.5.4.50', OID_ATTRIBUTE_TYPE, 'uniqueMember', 'RFC4519'),
          '2.5.4.51': ('2.5.4.51', OID_ATTRIBUTE_TYPE, 'houseIdentifier', 'RFC4519'),
          '2.5.4.52': ('2.5.4.52', OID_ATTRIBUTE_TYPE, 'supportedAlgorithms', 'RFC4523'),
          '2.5.4.53': ('2.5.4.53', OID_ATTRIBUTE_TYPE, 'deltaRevocationList', 'RFC4523'),
          '********': ('********', OID_ATTRIBUTE_TYPE, 'dmdName', 'RFC2256'),
          '*******5': ('*******5', OID_ATTRIBUTE_TYPE, 'pseudonym', 'RFC3280'),
          '2.16.840.1.113719.*******.501': ('2.16.840.1.113719.*******.501', OID_ATTRIBUTE_TYPE, 'GUID', 'NOVELL'),
          '2.16.840.1.113719.*********': ('2.16.840.1.113719.*********', OID_ATTRIBUTE_TYPE, 'localEntryID', 'NOVELL'),
          '2.16.840.1.113730.*******': ('2.16.840.1.113730.*******', OID_ATTRIBUTE_TYPE, 'ipaUniqueID', 'freeIPA'),
          '2.16.840.1.113730.*******': ('2.16.840.1.113730.*******', OID_ATTRIBUTE_TYPE, 'ipaClientVersion', 'freeIPA'),
          '2.16.840.1.113730.*******': ('2.16.840.1.113730.*******', OID_ATTRIBUTE_TYPE, 'enrolledBy', 'freeIPA'),
          '2.16.840.1.113730.*******': ('2.16.840.1.113730.*******', OID_ATTRIBUTE_TYPE, 'fqdn', 'freeIPA'),
          '2.16.840.1.113730.*******8': ('2.16.840.1.113730.*******8', OID_ATTRIBUTE_TYPE, 'managedBy', 'freeIPA'),
          '2.16.840.1.113730.********': ('2.16.840.1.113730.********', OID_ATTRIBUTE_TYPE, 'ipaEntitlementId', 'freeIPA'),

          # controls
          '1.2.826.0.1.3344810.2.3': ('1.2.826.0.1.3344810.2.3', OID_CONTROL, 'Matched Values', 'RFC3876'),
          '1.2.840.113556.1.4.319': ('1.2.840.113556.1.4.319', OID_CONTROL, 'LDAP Simple Paged Results', 'RFC2696'),
          '1.2.840.113556.1.4.417': ('1.2.840.113556.1.4.417', OID_CONTROL, 'LDAP server show deleted objects', 'MICROSOFT'),
          '1.2.840.113556.1.4.473': ('1.2.840.113556.1.4.473', OID_CONTROL, 'Sort Request', 'RFC2891'),
          '1.2.840.113556.1.4.474': ('1.2.840.113556.1.4.474', OID_CONTROL, 'Sort Response', 'RFC2891'),
          '1.2.840.113556.1.4.521': ('1.2.840.113556.1.4.521', OID_CONTROL, 'Cross-domain move', 'MICROSOFT'),
          '1.2.840.113556.1.4.528': ('1.2.840.113556.1.4.528', OID_CONTROL, 'Server search notification', 'MICROSOFT'),
          '1.2.840.113556.1.4.529': ('1.2.840.113556.1.4.529', OID_CONTROL, 'Extended DN', 'MICROSOFT'),
          '1.2.840.113556.1.4.619': ('1.2.840.113556.1.4.619', OID_CONTROL, 'Lazy commit', 'MICROSOFT'),
          '1.2.840.113556.1.4.801': ('1.2.840.113556.1.4.801', OID_CONTROL, 'Security descriptor flags', 'MICROSOFT'),
          '1.2.840.113556.1.4.802': ('1.2.840.113556.1.4.802', OID_CONTROL, 'Range option', 'MICROSOFT'),
          '1.2.840.113556.1.4.805': ('1.2.840.113556.1.4.805', OID_CONTROL, 'Tree delete', 'MICROSOFT'),
          '1.2.840.113556.1.4.841': ('1.2.840.113556.1.4.841', OID_CONTROL, 'Directory synchronization', 'MICROSOFT'),
          '1.2.840.113556.1.4.970': ('1.2.840.113556.1.4.970', OID_CONTROL, 'Get stats', 'MICROSOFT'),
          '1.2.840.113556.1.4.1338': ('1.2.840.113556.1.4.1338', OID_CONTROL, 'Verify name', 'MICROSOFT'),
          '1.2.840.113556.1.4.1339': ('1.2.840.113556.1.4.1339', OID_CONTROL, 'Domain scope', 'MICROSOFT'),
          '1.2.840.113556.1.4.1340': ('1.2.840.113556.1.4.1340', OID_CONTROL, 'Search options', 'MICROSOFT'),
          '1.2.840.113556.1.4.1341': ('1.2.840.113556.1.4.1341', OID_CONTROL, 'RODC DCPROMO', 'MICROSOFT'),
          '1.2.840.113556.1.4.1413': ('1.2.840.113556.1.4.1413', OID_CONTROL, 'Permissive modify', 'MICROSOFT'),
          '1.2.840.113556.1.4.1504': ('1.2.840.113556.1.4.1504', OID_CONTROL, 'Attribute scoped query', 'MICROSOFT'),
          '1.2.840.113556.1.4.1852': ('1.2.840.113556.1.4.1852', OID_CONTROL, 'User quota', 'MICROSOFT'),
          '1.2.840.113556.1.4.1907': ('1.2.840.113556.1.4.1907', OID_CONTROL, 'Server shutdown notify', 'MICROSOFT'),
          '1.2.840.113556.1.4.1948': ('1.2.840.113556.1.4.1948', OID_CONTROL, 'Range retrieval no error', 'MICROSOFT'),
          '1.2.840.113556.1.4.1974': ('1.2.840.113556.1.4.1974', OID_CONTROL, 'Server force update', 'MICROSOFT'),
          '1.2.840.113556.1.4.2026': ('1.2.840.113556.1.4.2026', OID_CONTROL, 'Input DN', 'MICROSOFT'),
          '1.2.840.113556.1.4.2064': ('1.2.840.113556.1.4.2064', OID_CONTROL, 'Show recycled', 'MICROSOFT'),
          '1.2.840.113556.1.4.2065': ('1.2.840.113556.1.4.2065', OID_CONTROL, 'Show deactivated link', 'MICROSOFT'),
          '1.2.840.113556.1.4.2066': ('1.2.840.113556.1.4.2066', OID_CONTROL, 'Policy hints [DEPRECATED]', 'MICROSOFT'),
          '1.2.840.113556.1.4.2090': ('1.2.840.113556.1.4.2090', OID_CONTROL, 'DirSync EX', 'MICROSOFT'),
          '1.2.840.113556.1.4.2204': ('1.2.840.113556.1.4.2204', OID_CONTROL, 'Tree deleted EX', 'MICROSOFT'),
          '1.2.840.113556.1.4.2205': ('1.2.840.113556.1.4.2205', OID_CONTROL, 'Updates stats', 'MICROSOFT'),
          '1.2.840.113556.1.4.2206': ('1.2.840.113556.1.4.2206', OID_CONTROL, 'Search hints', 'MICROSOFT'),
          '1.2.840.113556.1.4.2211': ('1.2.840.113556.1.4.2211', OID_CONTROL, 'Expected entry count', 'MICROSOFT'),
          '1.2.840.113556.1.4.2239': ('1.2.840.113556.1.4.2239', OID_CONTROL, 'Policy hints', 'MICROSOFT'),
          '1.2.840.113556.1.4.2255': ('1.2.840.113556.1.4.2255', OID_CONTROL, 'Set owner', 'MICROSOFT'),
          '1.2.840.113556.1.4.2256': ('1.2.840.113556.1.4.2256', OID_CONTROL, 'Bypass quota', 'MICROSOFT'),
          '*******.1.7.1': ('*******.1.7.1', OID_CONTROL, 'LCUP Sync Request', 'RFC3928'),
          '*******.1.7.2': ('*******.1.7.2', OID_CONTROL, 'LCUP Sync Update', 'RFC3928'),
          '*******.1.7.3': ('*******.1.7.3', OID_CONTROL, 'LCUP Sync Done', 'RFC3928'),
          '*******.1.12': ('*******.1.12', OID_CONTROL, 'Assertion', 'RFC4528'),
          '*******.1.13.1': ('*******.1.13.1', OID_CONTROL, 'LDAP Pre-read', 'RFC4527'),
          '*******.1.13.2': ('*******.1.13.2', OID_CONTROL, 'LDAP Post-read', 'RFC4527'),
          '*******.1.21.2': ('*******.1.21.2', OID_CONTROL, 'Transaction Specification', 'RFC5805'),
          '*******.1.22': ('*******.1.22', OID_CONTROL, "LDAP Don't Use Copy", 'RFC6171'),
          '*******.4.1.42.2.27.8.5.1': ('*******.4.1.42.2.27.8.5.1', OID_CONTROL, 'Password policy', 'IETF DRAFT behera-ldap-password-policy'),
          '*******.4.1.42.2.27.9.5.2': ('*******.4.1.42.2.27.9.5.2', OID_CONTROL, 'Get effective rights', 'IETF DRAFT draft-ietf-ldapext-acl-model'),
          '*******.4.1.42.2.27.9.5.8': ('*******.4.1.42.2.27.9.5.8', OID_CONTROL, 'Account usability', 'SUN microsystems'),
          '*******.4.1.1466.29539.12': ('*******.4.1.1466.29539.12', OID_CONTROL, 'Chaining loop detect', 'SUN microsystems'),
          '*******.4.1.4203.1.9.1.1': ('*******.4.1.4203.1.9.1.1', OID_CONTROL, 'LDAP content synchronization', 'RFC4533'),
          '*******.4.1.4203.1.10.1': ('*******.4.1.4203.1.10.1', OID_CONTROL, 'Subentries', 'RFC3672'),
          '*******.4.1.4203.1.10.2': ('*******.4.1.4203.1.10.2', OID_CONTROL, 'No-Operation', 'IETF DRAFT draft-zeilenga-ldap-noop'),
          '*******.4.1.4203.666.5.16': ('*******.4.1.4203.666.5.16', OID_CONTROL, 'LDAP Dereference', 'IETF DRAFT draft-masarati-ldap-deref'),
          '*******.4.1.7628.5.101.1': ('*******.4.1.7628.5.101.1', OID_CONTROL, 'LDAP subentries', 'IETF DRAFT draft-ietf-ldup-subentry'),
          '*******.4.1.26027.1.5.2': ('*******.4.1.26027.1.5.2', OID_CONTROL, 'Replication repair', 'OpenDS'),
          '2.16.840.1.113719.1.27.101.5': ('2.16.840.1.113719.1.27.101.5', OID_CONTROL, 'Simple password', 'NOVELL'),
          '*******.4.1.26027.1.6.1': ('*******.4.1.26027.1.6.1', OID_CONTROL, 'Password policy state', 'OpenDS'),
          '*******.4.1.26027.1.6.2': ('*******.4.1.26027.1.6.2', OID_CONTROL, 'Get connection ID', 'OpenDS'),
          '*******.4.1.26027.1.6.3': ('*******.4.1.26027.1.6.3', OID_CONTROL, 'Get symmetric key', 'OpenDS'),
          '2.16.840.1.113719.1.27.101.6': ('2.16.840.1.113719.1.27.101.6', OID_CONTROL, 'Forward reference', 'NOVELL'),
          '2.16.840.1.113719.1.27.103.7': ('2.16.840.1.113719.1.27.103.7', OID_CONTROL, 'Grouping', 'NOVELL'),
          '2.16.840.1.113730.3.4.2': ('2.16.840.1.113730.3.4.2', OID_CONTROL, 'ManageDsaIT', 'RFC3296'),
          '2.16.840.1.113730.3.4.3': ('2.16.840.1.113730.3.4.3', OID_CONTROL, 'Persistent Search', 'IETF'),
          '2.16.840.1.113730.3.4.4': ('2.16.840.1.113730.3.4.4', OID_CONTROL, 'Netscape Password Expired', 'Netscape'),
          '2.16.840.1.113730.3.4.5': ('2.16.840.1.113730.3.4.5', OID_CONTROL, 'Netscape Password Expiring', 'Netscape'),
          '2.16.840.1.113730.3.4.6': ('2.16.840.1.113730.3.4.6', OID_CONTROL, 'Netscape NT Synchronization Client', 'Netscape'),
          '2.16.840.1.113730.3.4.7': ('2.16.840.1.113730.3.4.7', OID_CONTROL, 'Entry Change Notification', 'Netscape'),
          '2.16.840.1.113730.3.4.9': ('2.16.840.1.113730.3.4.9', OID_CONTROL, 'Virtual List View Request', 'IETF'),
          '2.16.840.1.113730.3.4.10': ('2.16.840.1.113730.3.4.10', OID_CONTROL, 'Virtual List View Response', 'IETF'),
          '2.16.840.1.113730.3.4.12': ('2.16.840.1.113730.3.4.12', OID_CONTROL, 'Proxied Authorization (old)', 'Netscape'),
          '2.16.840.1.113730.3.4.13': ('2.16.840.1.113730.3.4.13', OID_CONTROL, 'iPlanet Directory Server Replication Update Information', 'Netscape'),
          '2.16.840.1.113730.3.4.14': ('2.16.840.1.113730.3.4.14', OID_CONTROL, 'Search on specific database', 'Netscape'),
          '2.16.840.1.113730.3.4.15': ('2.16.840.1.113730.3.4.15', OID_CONTROL, 'Authorization Identity Response Control', 'RFC3829'),
          '2.16.840.1.113730.3.4.16': ('2.16.840.1.113730.3.4.16', OID_CONTROL, 'Authorization Identity Request Control', 'RFC3829'),
          '2.16.840.1.113730.3.4.17': ('2.16.840.1.113730.3.4.17', OID_CONTROL, 'Real attribute only request', 'Netscape'),
          '2.16.840.1.113730.3.4.18': ('2.16.840.1.113730.3.4.18', OID_CONTROL, 'Proxy Authorization Control', 'RFC6171'),
          '2.16.840.1.113730.3.4.19': ('2.16.840.1.113730.3.4.19', OID_CONTROL, 'Chaining loop detection', 'Netscape'),
          '2.16.840.1.113730.3.4.20': ('2.16.840.1.113730.3.4.20', OID_CONTROL, 'Mapping Tree Node - Use one backend [extended]', 'openLDAP'),
          '2.16.840.1.113730.********': ('2.16.840.1.113730.********', OID_CONTROL, 'OTP Sync Request', 'freeIPA'),

          # dit content rules

          # extensions
          '1.2.840.113556.1.4.1781': ('1.2.840.113556.1.4.1781', OID_EXTENSION, 'Fast concurrent bind', 'MICROSOFT'),
          '1.2.840.113556.1.4.2212': ('1.2.840.113556.1.4.2212', OID_EXTENSION, 'Batch request', 'MICROSOFT'),
          '*******.1.8': ('*******.1.8', OID_EXTENSION, 'Cancel Operation', 'RFC3909'),
          '*******.1.21.1': ('*******.1.21.1', OID_EXTENSION, 'Start Transaction Extended Request', 'RFC5805'),
          '*******.1.21.3': ('*******.1.21.3', OID_EXTENSION, 'End Transaction Extended Request', 'RFC5805'),
          '*******.4.1.1466.101.119.1': ('*******.4.1.1466.101.119.1', OID_EXTENSION, 'Dynamic Refresh', 'RFC2589'),
          '*******.4.1.1466.20037': ('*******.4.1.1466.20037', OID_EXTENSION, 'StartTLS', 'RFC4511-RFC4513'),
          '*******.4.1.4203.1.11.1': ('*******.4.1.4203.1.11.1', OID_EXTENSION, 'Modify Password', 'RFC3062'),
          '*******.4.1.4203.1.11.3': ('*******.4.1.4203.1.11.3', OID_EXTENSION, 'Who am I', 'RFC4532'),
          '*******.1.17.1': ('*******.1.17.1', OID_EXTENSION, 'StartLBURPRequest LDAP ExtendedRequest message', 'RFC4373'),
          '*******.1.17.2': ('*******.1.17.2', OID_EXTENSION, 'StartLBURPResponse LDAP ExtendedResponse message', 'RFC4373'),
          '*******.1.17.3': ('*******.1.17.3', OID_EXTENSION, 'EndLBURPRequest LDAP ExtendedRequest message', 'RFC4373'),
          '*******.1.17.4': ('*******.1.17.4', OID_EXTENSION, 'EndLBURPResponse LDAP ExtendedResponse message', 'RFC4373'),
          '*******.1.17.5': ('*******.1.17.5', OID_EXTENSION, 'LBURPUpdateRequest LDAP ExtendedRequest message', 'RFC4373'),
          '*******.1.17.6': ('*******.1.17.6', OID_EXTENSION, 'LBURPUpdateResponse LDAP ExtendedResponse message', 'RFC4373'),
          '*******.1.19': ('*******.1.19', OID_EXTENSION, 'LDAP Turn Operation', 'RFC4531'),
          '2.16.840.1.113719.1.14.100.1': ('2.16.840.1.113719.1.14.100.1', OID_EXTENSION, 'getDriverSetRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.2': ('2.16.840.1.113719.1.14.100.2', OID_EXTENSION, 'getDriverSetResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.3': ('2.16.840.1.113719.1.14.100.3', OID_EXTENSION, 'setDriverSetRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.4': ('2.16.840.1.113719.1.14.100.4', OID_EXTENSION, 'setDriverSetResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.5': ('2.16.840.1.113719.1.14.100.5', OID_EXTENSION, 'clearDriverSetRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.6': ('2.16.840.1.113719.1.14.100.6', OID_EXTENSION, 'clearDriverSetResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.7': ('2.16.840.1.113719.1.14.100.7', OID_EXTENSION, 'getDriverStartOptionRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.8': ('2.16.840.1.113719.1.14.100.8', OID_EXTENSION, 'getDriverStartOptionResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.9': ('2.16.840.1.113719.1.14.100.9', OID_EXTENSION, 'setDriverStartOptionRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.10': ('2.16.840.1.113719.1.14.100.10', OID_EXTENSION, 'setDriverStartOptionResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.11': ('2.16.840.1.113719.1.14.100.11', OID_EXTENSION, 'getVersionRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.12': ('2.16.840.1.113719.1.14.100.12', OID_EXTENSION, 'getVersionResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.13': ('2.16.840.1.113719.1.14.100.13', OID_EXTENSION, 'getDriverStateRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.14': ('2.16.840.1.113719.1.14.100.14', OID_EXTENSION, 'getDriverStateResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.15': ('2.16.840.1.113719.1.14.100.15', OID_EXTENSION, 'startDriverRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.16': ('2.16.840.1.113719.1.14.100.16', OID_EXTENSION, 'startDriverResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.17': ('2.16.840.1.113719.1.14.100.17', OID_EXTENSION, 'stopDriverRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.18': ('2.16.840.1.113719.1.14.100.18', OID_EXTENSION, 'stopDriverResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.19': ('2.16.840.1.113719.1.14.100.19', OID_EXTENSION, 'getDriverStatsRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.20': ('2.16.840.1.113719.1.14.100.20', OID_EXTENSION, 'getDriverStatsResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.21': ('2.16.840.1.113719.1.14.100.21', OID_EXTENSION, 'driverGetSchemaRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.22': ('2.16.840.1.113719.1.14.100.22', OID_EXTENSION, 'driverGetSchemaResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.23': ('2.16.840.1.113719.1.14.100.23', OID_EXTENSION, 'driverResyncRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.24': ('2.16.840.1.113719.1.14.100.24', OID_EXTENSION, 'driverResyncResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.25': ('2.16.840.1.113719.1.14.100.25', OID_EXTENSION, 'migrateAppRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.26': ('2.16.840.1.113719.1.14.100.26', OID_EXTENSION, 'migrateAppResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.27': ('2.16.840.1.113719.1.14.100.27', OID_EXTENSION, 'queueEventRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.28': ('2.16.840.1.113719.1.14.100.28', OID_EXTENSION, 'queueEventResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.29': ('2.16.840.1.113719.1.14.100.29', OID_EXTENSION, 'submitCommandRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.30': ('2.16.840.1.113719.1.14.100.30', OID_EXTENSION, 'submitCommandResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.31': ('2.16.840.1.113719.1.14.100.31', OID_EXTENSION, 'submitEventRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.32': ('2.16.840.1.113719.1.14.100.32', OID_EXTENSION, 'submitEventResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.33': ('2.16.840.1.113719.1.14.100.33', OID_EXTENSION, 'getChunkedResultRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.34': ('2.16.840.1.113719.1.14.100.34', OID_EXTENSION, 'getChunkedResultResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.35': ('2.16.840.1.113719.1.14.100.35', OID_EXTENSION, 'closeChunkedResultRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.36': ('2.16.840.1.113719.1.14.100.36', OID_EXTENSION, 'closeChunkedResultResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.37': ('2.16.840.1.113719.1.14.100.37', OID_EXTENSION, 'checkObjectPasswordRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.38': ('2.16.840.1.113719.1.14.100.38', OID_EXTENSION, 'checkObjectPasswordResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.39': ('2.16.840.1.113719.1.14.100.39', OID_EXTENSION, 'initDriverObjectRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.40': ('2.16.840.1.113719.1.14.100.40', OID_EXTENSION, 'initDriverObjectResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.41': ('2.16.840.1.113719.1.14.100.41', OID_EXTENSION, 'viewCacheEntriesRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.42': ('2.16.840.1.113719.1.14.100.42', OID_EXTENSION, 'viewCacheEntriesResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.43': ('2.16.840.1.113719.1.14.100.43', OID_EXTENSION, 'deleteCacheEntriesRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.44': ('2.16.840.1.113719.1.14.100.44', OID_EXTENSION, 'deleteCacheEntriesResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.45': ('2.16.840.1.113719.1.14.100.45', OID_EXTENSION, 'getPasswordsStateRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.46': ('2.16.840.1.113719.1.14.100.46', OID_EXTENSION, 'getPasswordsStateResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.47': ('2.16.840.1.113719.1.14.100.47', OID_EXTENSION, 'regenerateKeyRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.48': ('2.16.840.1.113719.1.14.100.48', OID_EXTENSION, 'regenerateKeyResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.49': ('2.16.840.1.113719.1.14.100.49', OID_EXTENSION, 'getServerCertRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.50': ('2.16.840.1.113719.1.14.100.50', OID_EXTENSION, 'getServerCertResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.51': ('2.16.840.1.113719.1.14.100.51', OID_EXTENSION, 'discoverJobsRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.52': ('2.16.840.1.113719.1.14.100.52', OID_EXTENSION, 'discoverJobsResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.53': ('2.16.840.1.113719.1.14.100.53', OID_EXTENSION, 'notifyJobUpdateRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.54': ('2.16.840.1.113719.1.14.100.54', OID_EXTENSION, 'notifyJobUpdateResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.55': ('2.16.840.1.113719.1.14.100.55', OID_EXTENSION, 'startJobRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.56': ('2.16.840.1.113719.1.14.100.56', OID_EXTENSION, 'startJobResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.57': ('2.16.840.1.113719.1.14.100.57', OID_EXTENSION, 'abortJobRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.58': ('2.16.840.1.113719.1.14.100.58', OID_EXTENSION, 'abortJobresponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.59': ('2.16.840.1.113719.1.14.100.59', OID_EXTENSION, 'getJobStateRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.60': ('2.16.840.1.113719.1.14.100.60', OID_EXTENSION, 'getJobStateResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.61': ('2.16.840.1.113719.1.14.100.61', OID_EXTENSION, 'checkJobConfigRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.62': ('2.16.840.1.113719.1.14.100.62', OID_EXTENSION, 'checkJobConfigResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.63': ('2.16.840.1.113719.1.14.100.63', OID_EXTENSION, 'setLogEventsRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.64': ('2.16.840.1.113719.1.14.100.64', OID_EXTENSION, 'setLogEventsResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.65': ('2.16.840.1.113719.1.14.100.65', OID_EXTENSION, 'clearLogEventsRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.66': ('2.16.840.1.113719.1.14.100.66', OID_EXTENSION, 'clearLogEventsResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.67': ('2.16.840.1.113719.1.14.100.67', OID_EXTENSION, 'setAppPasswordRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.68': ('2.16.840.1.113719.1.14.100.68', OID_EXTENSION, 'setAppPasswordResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.69': ('2.16.840.1.113719.1.14.100.69', OID_EXTENSION, 'clearAppPasswordRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.70': ('2.16.840.1.113719.1.14.100.70', OID_EXTENSION, 'clearAppPasswordResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.71': ('2.16.840.1.113719.1.14.100.71', OID_EXTENSION, 'setRemoteLoaderPasswordRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.72': ('2.16.840.1.113719.1.14.100.72', OID_EXTENSION, 'setRemoteLoaderPasswordResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.73': ('2.16.840.1.113719.1.14.100.73', OID_EXTENSION, 'clearRemoteLoaderPasswordRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.74': ('2.16.840.1.113719.1.14.100.74', OID_EXTENSION, 'clearRemoteLoaderPasswordResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.75': ('2.16.840.1.113719.1.14.100.75', OID_EXTENSION, 'setNamedPasswordRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.76': ('2.16.840.1.113719.1.14.100.76', OID_EXTENSION, 'setNamedPasswordResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.77': ('2.16.840.1.113719.1.14.100.77', OID_EXTENSION, 'removeNamedPasswordRequest', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.78': ('2.16.840.1.113719.1.14.100.78', OID_EXTENSION, 'removeNamedPasswordResponse', 'NOVELL'),
          '2.16.840.1.113719.1.14.100.79': ('2.16.840.1.113719.1.14.100.79', OID_EXTENSION, 'removeAllNamedPasswordsRequest', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'removeAllNamedPasswordsResponse', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'listNamedPasswordsRequest', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'listNamedPasswordsResponse', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'getDefaultReciprocalAttrsMapRequest', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'getDefaultReciprocalAttrsMapResponse', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'resetDriverStatsRequest', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'resetDriverStatsResponse', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'regenerateAllKeysRequest', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'regenerateAllKeysResponse', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'getDriverGCVRequest', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'getDriverGCVResponse', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'getNamedPasswordRequest', 'NOVELL'),
          '2.16.840.1.113719.***********': ('2.16.840.1.113719.***********', OID_EXTENSION, 'getNamedPasswordResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.1': ('2.16.840.1.113719.1.27.100.1', OID_EXTENSION, 'ndsToLdapResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.2': ('2.16.840.1.113719.1.27.100.2', OID_EXTENSION, 'ndsToLdapRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.3': ('2.16.840.1.113719.1.27.100.3', OID_EXTENSION, 'splitPartitionRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.4': ('2.16.840.1.113719.1.27.100.4', OID_EXTENSION, 'splitPartitionResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.5': ('2.16.840.1.113719.1.27.100.5', OID_EXTENSION, 'mergePartitionRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.6': ('2.16.840.1.113719.1.27.100.6', OID_EXTENSION, 'mergePartitionResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.7': ('2.16.840.1.113719.1.27.100.7', OID_EXTENSION, 'addReplicaRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.8': ('2.16.840.1.113719.1.27.100.8', OID_EXTENSION, 'addReplicaResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.9': ('2.16.840.1.113719.1.27.100.9', OID_EXTENSION, 'refreshLDAPServerRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.10': ('2.16.840.1.113719.1.27.100.10', OID_EXTENSION, 'refreshLDAPServerResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.11': ('2.16.840.1.113719.1.27.100.11', OID_EXTENSION, 'removeReplicaRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.12': ('2.16.840.1.113719.1.27.100.12', OID_EXTENSION, 'removeReplicaResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.13': ('2.16.840.1.113719.1.27.100.13', OID_EXTENSION, 'partitionEntryCountRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.14': ('2.16.840.1.113719.1.27.100.14', OID_EXTENSION, 'partitionEntryCountResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.15': ('2.16.840.1.113719.1.27.100.15', OID_EXTENSION, 'changeReplicaTypeRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.16': ('2.16.840.1.113719.1.27.100.16', OID_EXTENSION, 'changeReplicaTypeResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.17': ('2.16.840.1.113719.1.27.100.17', OID_EXTENSION, 'getReplicaInfoRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.18': ('2.16.840.1.113719.1.27.100.18', OID_EXTENSION, 'getReplicaInfoResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.19': ('2.16.840.1.113719.1.27.100.19', OID_EXTENSION, 'listReplicaRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.20': ('2.16.840.1.113719.1.27.100.20', OID_EXTENSION, 'listReplicaResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.21': ('2.16.840.1.113719.1.27.100.21', OID_EXTENSION, 'receiveAllUpdatesRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.22': ('2.16.840.1.113719.1.27.100.22', OID_EXTENSION, 'receiveAllUpdatesResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.23': ('2.16.840.1.113719.1.27.100.23', OID_EXTENSION, 'sendAllUpdatesRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.24': ('2.16.840.1.113719.1.27.100.24', OID_EXTENSION, 'sendAllUpdatesResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.25': ('2.16.840.1.113719.1.27.100.25', OID_EXTENSION, 'requestPartitionSyncRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.26': ('2.16.840.1.113719.1.27.100.26', OID_EXTENSION, 'requestPartitionSyncResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.27': ('2.16.840.1.113719.1.27.100.27', OID_EXTENSION, 'requestSchemaSyncRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.28': ('2.16.840.1.113719.1.27.100.28', OID_EXTENSION, 'requestSchemaSyncResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.29': ('2.16.840.1.113719.1.27.100.29', OID_EXTENSION, 'abortPartitionOperationRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.30': ('2.16.840.1.113719.1.27.100.30', OID_EXTENSION, 'abortPartitionOperationResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.31': ('2.16.840.1.113719.1.27.100.31', OID_EXTENSION, 'getBindDNRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.32': ('2.16.840.1.113719.1.27.100.32', OID_EXTENSION, 'getBindDNResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.33': ('2.16.840.1.113719.1.27.100.33', OID_EXTENSION, 'getEffectivePrivilegesRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.34': ('2.16.840.1.113719.1.27.100.34', OID_EXTENSION, 'getEffectivePrivilegesResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.35': ('2.16.840.1.113719.1.27.100.35', OID_EXTENSION, 'setReplicationFilterRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.36': ('2.16.840.1.113719.1.27.100.36', OID_EXTENSION, 'setReplicationFilterResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.37': ('2.16.840.1.113719.1.27.100.37', OID_EXTENSION, 'getReplicationFilterRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.38': ('2.16.840.1.113719.1.27.100.38', OID_EXTENSION, 'getReplicationFilterResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.39': ('2.16.840.1.113719.1.27.100.39', OID_EXTENSION, 'splitOrphanPartitionRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.40': ('2.16.840.1.113719.1.27.100.40', OID_EXTENSION, 'splitOrphanPartitionResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.41': ('2.16.840.1.113719.1.27.100.41', OID_EXTENSION, 'removeOrphanPartitionRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.42': ('2.16.840.1.113719.1.27.100.42', OID_EXTENSION, 'removeOrphanPartitionResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.43': ('2.16.840.1.113719.1.27.100.43', OID_EXTENSION, 'triggerBKLinkerRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.44': ('2.16.840.1.113719.1.27.100.44', OID_EXTENSION, 'triggerBKLinkerResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.45': ('2.16.840.1.113719.1.27.100.45', OID_EXTENSION, 'triggerDRLProcessRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.46': ('2.16.840.1.113719.1.27.100.46', OID_EXTENSION, 'triggerDRLProcessResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.47': ('2.16.840.1.113719.1.27.100.47', OID_EXTENSION, 'triggerJanitorRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.48': ('2.16.840.1.113719.1.27.100.48', OID_EXTENSION, 'triggerJanitorResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.49': ('2.16.840.1.113719.1.27.100.49', OID_EXTENSION, 'triggerLimberRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.50': ('2.16.840.1.113719.1.27.100.50', OID_EXTENSION, 'triggerLimberResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.51': ('2.16.840.1.113719.1.27.100.51', OID_EXTENSION, 'triggerSkulkerRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.52': ('2.16.840.1.113719.1.27.100.52', OID_EXTENSION, 'triggerSkulkerResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.53': ('2.16.840.1.113719.1.27.100.53', OID_EXTENSION, 'triggerSchemaSyncRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.54': ('2.16.840.1.113719.1.27.100.54', OID_EXTENSION, 'triggerSchemaSyncResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.55': ('2.16.840.1.113719.1.27.100.55', OID_EXTENSION, 'triggerPartitionPurgeRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.56': ('2.16.840.1.113719.1.27.100.56', OID_EXTENSION, 'triggerPartitionPurgeResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.79': ('2.16.840.1.113719.1.27.100.79', OID_EXTENSION, 'eventMonitorRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.80': ('2.16.840.1.113719.1.27.100.80', OID_EXTENSION, 'eventMonitorResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.81': ('2.16.840.1.113719.1.27.100.81', OID_EXTENSION, 'nldapEventNotification', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.84': ('2.16.840.1.113719.1.27.100.84', OID_EXTENSION, 'filteredEventMonitorRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.85': ('2.16.840.1.113719.1.27.100.85', OID_EXTENSION, 'filteredEventMonitorResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.96': ('2.16.840.1.113719.1.27.100.96', OID_EXTENSION, 'ldapBackupRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.97': ('2.16.840.1.113719.1.27.100.97', OID_EXTENSION, 'ldapBackupResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.98': ('2.16.840.1.113719.1.27.100.98', OID_EXTENSION, 'ldapRestoreRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.99': ('2.16.840.1.113719.1.27.100.99', OID_EXTENSION, 'ldapRestoreResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.101': ('2.16.840.1.113719.1.27.100.101', OID_EXTENSION, 'LDAPDNStoX500DNRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.102': ('2.16.840.1.113719.1.27.100.102', OID_EXTENSION, 'LDAPDNStoX500DNResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.103': ('2.16.840.1.113719.1.27.100.103', OID_EXTENSION, 'getPrivilegesListRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.100.104': ('2.16.840.1.113719.1.27.100.104', OID_EXTENSION, 'getPrivilegesListResponse', 'NOVELL'),
          '2.16.840.1.113719.1.27.103.1': ('2.16.840.1.113719.1.27.103.1', OID_EXTENSION, 'createGroupingRequest', 'NOVELL'),
          '2.16.840.1.113719.1.27.103.2': ('2.16.840.1.113719.1.27.103.2', OID_EXTENSION, 'endGroupingRequest', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.1': ('2.16.840.1.113719.1.39.42.100.1', OID_EXTENSION, 'NMAS Put Login Configuration', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.3': ('2.16.840.1.113719.1.39.42.100.3', OID_EXTENSION, 'NMAS Get Login Configuration', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.5': ('2.16.840.1.113719.1.39.42.100.5', OID_EXTENSION, 'NMAS Delete Login Configuration', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.7': ('2.16.840.1.113719.1.49.42.100.7', OID_EXTENSION, 'NMAS Put Login Secret', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.9': ('2.16.840.1.113719.1.39.42.100.9', OID_EXTENSION, 'NMAS Delete Login Secret', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.11': ('2.16.840.1.113719.1.39.42.100.11', OID_EXTENSION, 'NMAS Set Universal Password Request', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.12': ('2.16.840.1.113719.1.39.42.100.12', OID_EXTENSION, 'NMAS Set Universal Password Response', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.13': ('2.16.840.1.113719.1.39.42.100.13', OID_EXTENSION, 'NMAS Get Universal Password Request', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.14': ('2.16.840.1.113719.1.39.42.100.14', OID_EXTENSION, 'NMAS Get Universal Password Response', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.15': ('2.16.840.1.113719.1.39.42.100.15', OID_EXTENSION, 'NMAS Delete Universal Password', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.17': ('2.16.840.1.113719.1.39.42.100.17', OID_EXTENSION, 'NMAS Check password against password policy', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.19': ('2.16.840.1.113719.1.39.42.100.19', OID_EXTENSION, 'NMAS Get password policy information', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.21': ('2.16.840.1.113719.1.39.42.100.21', OID_EXTENSION, 'NMAS Change Universal Password', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.23': ('2.16.840.1.113719.1.39.42.100.23', OID_EXTENSION, 'NMAS Graded Authentication management', 'NOVELL'),
          '2.16.840.1.113719.1.39.42.100.25': ('2.16.840.1.113719.1.39.42.100.25', OID_EXTENSION, 'NMAS management (new with NMAS 3.1.0)', 'NOVELL'),
          '2.16.840.1.113719.1.142.1.4.1': ('2.16.840.1.113719.1.142.1.4.1', OID_EXTENSION, 'LBURPIncUpdate', 'NOVELL'),
          '2.16.840.1.113719.1.142.1.4.2': ('2.16.840.1.113719.1.142.1.4.2', OID_EXTENSION, 'LBURPFullUpdate', 'NOVELL'),
          '2.16.840.1.113719.1.142.100.1': ('2.16.840.1.113719.1.142.100.1', OID_EXTENSION, 'LBURPStartReplRequest', 'NOVELL'),
          '2.16.840.1.113719.1.142.100.2': ('2.16.840.1.113719.1.142.100.2', OID_EXTENSION, 'LBURPStartReplResponse', 'NOVELL'),
          '2.16.840.1.113719.1.142.100.4': ('2.16.840.1.113719.1.142.100.4', OID_EXTENSION, 'LBURPEndReplRequest', 'NOVELL'),
          '2.16.840.1.113719.1.142.100.5': ('2.16.840.1.113719.1.142.100.5', OID_EXTENSION, 'LBURPEndReplResponse', 'NOVELL'),
          '2.16.840.1.113719.1.142.100.6': ('2.16.840.1.113719.1.142.100.6', OID_EXTENSION, 'LBURPOperationRequest', 'NOVELL'),
          '2.16.840.1.113719.1.142.100.7': ('2.16.840.1.113719.1.142.100.7', OID_EXTENSION, 'LBURPOperationResponse', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.1': ('2.16.840.1.113719.1.148.100.1', OID_EXTENSION, 'SSLDAP_GET_SERVICE_INFO_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.2': ('2.16.840.1.113719.1.148.100.2', OID_EXTENSION, 'SSLDAP_GET_SERVICE_INFO_REPLY', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.3': ('2.16.840.1.113719.1.148.100.3', OID_EXTENSION, 'SSLDAP_READ_SECRET_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.4': ('2.16.840.1.113719.1.148.100.4', OID_EXTENSION, 'SSLDAP_READ_SECRET_REPLY', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.5': ('2.16.840.1.113719.1.148.100.5', OID_EXTENSION, 'SSLDAP_WRITE_SECRET_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.6': ('2.16.840.1.113719.1.148.100.6', OID_EXTENSION, 'SSLDAP_WRITE_SECRET_REPLY', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.7': ('2.16.840.1.113719.1.148.100.7', OID_EXTENSION, 'SSLDAP_ADD_SECRET_ID_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.8': ('2.16.840.1.113719.1.148.100.8', OID_EXTENSION, 'SSLDAP_ADD_SECRET_ID_REPLY', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.9': ('2.16.840.1.113719.1.148.100.9', OID_EXTENSION, 'SSLDAP_REMOVE_SECRET_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.10': ('2.16.840.1.113719.1.148.100.10', OID_EXTENSION, 'SSLDAP_REMOVE_SECRET_REPLY', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.11': ('2.16.840.1.113719.1.148.100.11', OID_EXTENSION, 'SSLDAP_REMOVE_SECRET_STORE_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.12': ('2.16.840.1.113719.1.148.100.12', OID_EXTENSION, 'SSLDAP_REMOVE_SECRET_STORE_REPLY', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.13': ('2.16.840.1.113719.1.148.100.13', OID_EXTENSION, 'SSLDAP_ENUMERATE_SECRET_IDS_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.14': ('2.16.840.1.113719.1.148.100.14', OID_EXTENSION, 'SSLDAP_ENUMERATE_SECRET_IDS_REPLY', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.15': ('2.16.840.1.113719.1.148.100.15', OID_EXTENSION, 'SSLDAP_UNLOCK_SECRETS_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.16': ('2.16.840.1.113719.1.148.100.16', OID_EXTENSION, 'SSLDAP_UNLOCK_SECRETS_REPLY', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.17': ('2.16.840.1.113719.1.148.100.17', OID_EXTENSION, 'SSLDAP_SET_EP_MASTER_PASSWORD_REQUEST', 'NOVELL'),
          '2.16.840.1.113719.1.148.100.18': ('2.16.840.1.113719.1.148.100.18', OID_EXTENSION, 'SSLDAP_SET_EP_MASTER_PASSWORD_REPLY', 'NOVELL'),
          '2.16.840.1.113730.3.5.1': ('2.16.840.1.113730.3.5.1', OID_EXTENSION, 'Transaction Request Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.2': ('2.16.840.1.113730.3.5.2', OID_EXTENSION, 'Transaction Response Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.3': ('2.16.840.1.113730.3.5.3', OID_EXTENSION, 'Transaction Response Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.4': ('2.16.840.1.113730.3.5.4', OID_EXTENSION, 'iPlanet Replication Response Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.5': ('2.16.840.1.113730.3.5.5', OID_EXTENSION, 'iPlanet End Replication Request Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.6': ('2.16.840.1.113730.3.5.6', OID_EXTENSION, 'iPlanet Replication Entry Request Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.7': ('2.16.840.1.113730.3.5.7', OID_EXTENSION, 'iPlanet Bulk Import Start Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.8': ('2.16.840.1.113730.3.5.8', OID_EXTENSION, 'iPlanet Bulk Import Finished Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.9': ('2.16.840.1.113730.3.5.9', OID_EXTENSION, 'iPlanet Digest Authentication Calculation Extended Operation', 'Netscape'),
          '2.16.840.1.113730.3.5.10': ('2.16.840.1.113730.3.5.10', OID_EXTENSION, 'Distributed Numeric Assignment Extended Request', 'Netscape'),
          '2.16.840.1.113730.3.5.11': ('2.16.840.1.113730.3.5.11', OID_EXTENSION, 'Distributed Numeric Assignment Extended Response', 'Netscape'),
          '2.16.840.1.113730.3.5.12': ('2.16.840.1.113730.3.5.12', OID_EXTENSION, 'Start replication request', 'Netscape'),
          '2.16.840.1.113730.3.5.13': ('2.16.840.1.113730.3.5.13', OID_EXTENSION, 'Start replication response', 'Netscape'),
          '2.16.840.1.113730.3.6.5': ('2.16.840.1.113730.3.6.5', OID_EXTENSION, 'Replication CleanAllRUV', 'Netscape'),
          '2.16.840.1.113730.3.6.6': ('2.16.840.1.113730.3.6.6', OID_EXTENSION, 'Replication Abort CleanAllRUV', 'Netscape'),
          '2.16.840.1.113730.3.6.7': ('2.16.840.1.113730.3.6.7', OID_EXTENSION, 'Replication CleanAllRUV Retrieve MaxCSN', 'Netscape'),
          '2.16.840.1.113730.3.6.8': ('2.16.840.1.113730.3.6.8', OID_EXTENSION, 'Replication CleanAllRUV Check Status', 'Netscape'),
          '2.16.840.1.113730.********': ('2.16.840.1.113730.********', OID_EXTENSION, 'KeyTab set', 'FreeIPA'),
          '2.16.840.1.113730.********': ('2.16.840.1.113730.********', OID_EXTENSION, 'KeyTab ret', 'FreeIPA'),
          '2.16.840.1.113730.********': ('2.16.840.1.113730.********', OID_EXTENSION, 'Enrollment join', 'FreeIPA'),
          '2.16.840.1.113730.********': ('2.16.840.1.113730.********', OID_EXTENSION, 'KeyTab get', 'FreeIPA'),

          # features (capabilities)
          '1.2.840.113556.1.4.800': ('1.2.840.113556.1.4.800', OID_FEATURE, 'Active directory', 'MICROSOFT'),
          '1.2.840.113556.1.4.1670': ('1.2.840.113556.1.4.1670', OID_FEATURE, 'Active directory V51', 'MICROSOFT'),
          '1.2.840.113556.1.4.1791': ('1.2.840.113556.1.4.1791', OID_FEATURE, 'Active directory LDAP Integration', 'MICROSOFT'),
          '1.2.840.113556.1.4.1880': ('1.2.840.113556.1.4.1880', OID_FEATURE, 'Active directory ADAM digest', 'MICROSOFT'),
          '1.2.840.113556.1.4.1851': ('1.2.840.113556.1.4.1851', OID_FEATURE, 'Active directory ADAM', 'MICROSOFT'),
          '1.2.840.113556.1.4.1920': ('1.2.840.113556.1.4.1920', OID_FEATURE, 'Active directory partial secrets', 'MICROSOFT'),
          '1.2.840.113556.1.4.1935': ('1.2.840.113556.1.4.1935', OID_FEATURE, 'Active directory V60', 'MICROSOFT'),
          '1.2.840.113556.1.4.2080': ('1.2.840.113556.1.4.2080', OID_FEATURE, 'Active directory V61 R2', 'MICROSOFT'),
          '1.2.840.113556.1.4.2237': ('1.2.840.113556.1.4.2237', OID_FEATURE, 'Active directory W8', 'MICROSOFT'),
          '*******.1.14': ('*******.1.14', OID_FEATURE, 'Modify-Increment', 'RFC4525'),
          '*******.1.17.7': ('*******.1.17.7', OID_FEATURE, 'LBURP Incremental Update style OID', 'RFC4373'),
          '*******.4.1.4203.1.5.1': ('*******.4.1.4203.1.5.1', OID_FEATURE, 'All Op Attrs', 'RFC3673'),
          '*******.4.1.4203.1.5.2': ('*******.4.1.4203.1.5.2', OID_FEATURE, 'OC AD Lists', 'RFC4529'),
          '*******.4.1.4203.1.5.3': ('*******.4.1.4203.1.5.3', OID_FEATURE, 'True/False filters', 'RFC4526'),
          '*******.4.1.4203.1.5.4': ('*******.4.1.4203.1.5.4', OID_FEATURE, 'Language Tag Options', 'RFC3866'),
          '*******.4.1.4203.1.5.5': ('*******.4.1.4203.1.5.5', OID_FEATURE, 'language Range Options', 'RFC3866'),
          '2.16.840.1.113719.1.27.99.1': ('2.16.840.1.113719.1.27.99.1', OID_FEATURE, 'Superior References', 'NOVELL'),

          # ldap syntaxes
          '1.2.840.113556.1.4.903': ('1.2.840.113556.1.4.903', OID_LDAP_SYNTAX, 'Object (DN-binary)', 'MICROSOFT'),
          '1.2.840.113556.1.4.904': ('1.2.840.113556.1.4.904', OID_LDAP_SYNTAX, 'Object(DN-string)', 'MICROSOFT'),
          '1.2.840.113556.1.4.905': ('1.2.840.113556.1.4.905', OID_LDAP_SYNTAX, 'String (Teletex)', 'MICROSOFT'),
          '1.2.840.113556.1.4.906': ('1.2.840.113556.1.4.906', OID_LDAP_SYNTAX, 'Large Integer', 'MICROSOFT'),
          '1.2.840.113556.1.4.907': ('1.2.840.113556.1.4.907', OID_LDAP_SYNTAX, 'String (NT-Sec-Desc)', 'MICROSOFT'),
          '1.2.840.113556.1.4.1221': ('1.2.840.113556.1.4.1221', OID_LDAP_SYNTAX, 'Object (OR-Name)', 'MICROSOFT'),
          '1.2.840.113556.1.4.1362': ('1.2.840.113556.1.4.1362', OID_LDAP_SYNTAX, 'String (Case)', 'MICROSOFT'),
          '*******.1.16.1': ('*******.1.16.1', OID_LDAP_SYNTAX, 'Universally Unique Identifier (UUID)', 'RFC4530'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'ACI item [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'Access point [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'Attribute Type Description', 'RFC4517'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'Audio [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'Binary [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'Bit String', 'RFC4517'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'Boolean', 'RFC4517'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'Certificate [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********': ('*******.4.1.1466.***********', OID_LDAP_SYNTAX, 'Certificate List [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.************': ('*******.4.1.1466.************', OID_LDAP_SYNTAX, 'Certificate Pair [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.************': ('*******.4.1.1466.************', OID_LDAP_SYNTAX, 'Country String', 'RFC4517'),
          '*******.4.1.1466.***********2': ('*******.4.1.1466.***********2', OID_LDAP_SYNTAX, 'DN', 'RFC4517'),
          '*******.4.1.1466.***********3': ('*******.4.1.1466.***********3', OID_LDAP_SYNTAX, 'Data Quality Syntax [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********4': ('*******.4.1.1466.***********4', OID_LDAP_SYNTAX, 'Delivery Method', 'RFC4517'),
          '*******.4.1.1466.***********5': ('*******.4.1.1466.***********5', OID_LDAP_SYNTAX, 'Directory String', 'RFC4517'),
          '*******.4.1.1466.***********6': ('*******.4.1.1466.***********6', OID_LDAP_SYNTAX, 'DIT Content Rule Description', 'RFC4517'),
          '*******.4.1.1466.***********7': ('*******.4.1.1466.***********7', OID_LDAP_SYNTAX, 'DIT Structure Rule Description', 'RFC4517'),
          '*******.4.1.1466.***********8': ('*******.4.1.1466.***********8', OID_LDAP_SYNTAX, 'DL Submit Permission [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********9': ('*******.4.1.1466.***********9', OID_LDAP_SYNTAX, 'DSA Quality Syntax [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********0': ('*******.4.1.1466.***********0', OID_LDAP_SYNTAX, 'DSE Type [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********1': ('*******.4.1.1466.***********1', OID_LDAP_SYNTAX, 'Enhanced Guide', 'RFC4517'),
          '*******.4.1.1466.***********2': ('*******.4.1.1466.***********2', OID_LDAP_SYNTAX, 'Facsimile Telephone Number', 'RFC4517'),
          '*******.4.1.1466.***********3': ('*******.4.1.1466.***********3', OID_LDAP_SYNTAX, 'Fax', 'RFC4517'),
          '*******.4.1.1466.***********4': ('*******.4.1.1466.***********4', OID_LDAP_SYNTAX, 'Generalized Time', 'RFC4517'),
          '*******.4.1.1466.***********5': ('*******.4.1.1466.***********5', OID_LDAP_SYNTAX, 'Guide [OBSOLETE]', 'RFC4517'),
          '*******.4.1.1466.***********6': ('*******.4.1.1466.***********6', OID_LDAP_SYNTAX, 'IA5 String', 'RFC4517'),
          '*******.4.1.1466.***********7': ('*******.4.1.1466.***********7', OID_LDAP_SYNTAX, 'Integer', 'RFC4517'),
          '*******.4.1.1466.***********8': ('*******.4.1.1466.***********8', OID_LDAP_SYNTAX, 'JPEG', 'RFC4517'),
          '*******.4.1.1466.***********9': ('*******.4.1.1466.***********9', OID_LDAP_SYNTAX, 'Master and Shadow Access Points [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********0': ('*******.4.1.1466.***********0', OID_LDAP_SYNTAX, 'Matching Rule Description', 'RFC4517'),
          '*******.4.1.1466.***********1': ('*******.4.1.1466.***********1', OID_LDAP_SYNTAX, 'Matching Rule Use Description', 'RFC4517'),
          '*******.4.1.1466.***********2': ('*******.4.1.1466.***********2', OID_LDAP_SYNTAX, 'Mail Preference [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********3': ('*******.4.1.1466.***********3', OID_LDAP_SYNTAX, 'MHS OR Address [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********4': ('*******.4.1.1466.***********4', OID_LDAP_SYNTAX, 'Name And Optional UID', 'RFC4517'),
          '*******.4.1.1466.***********5': ('*******.4.1.1466.***********5', OID_LDAP_SYNTAX, 'Name Form Description', 'RFC4517'),
          '*******.4.1.1466.***********6': ('*******.4.1.1466.***********6', OID_LDAP_SYNTAX, 'Numeric String', 'RFC4517'),
          '*******.4.1.1466.***********7': ('*******.4.1.1466.***********7', OID_LDAP_SYNTAX, 'Object Class Description', 'RFC4517'),
          '*******.4.1.1466.***********8': ('*******.4.1.1466.***********8', OID_LDAP_SYNTAX, 'OID', 'RFC4517'),
          '*******.4.1.1466.***********9': ('*******.4.1.1466.***********9', OID_LDAP_SYNTAX, 'Other Mailbox', 'RFC4517'),
          '*******.4.1.1466.***********0': ('*******.4.1.1466.***********0', OID_LDAP_SYNTAX, 'Octet String', 'RFC4517'),
          '*******.4.1.1466.***********1': ('*******.4.1.1466.***********1', OID_LDAP_SYNTAX, 'Postal Address', 'RFC4517'),
          '*******.4.1.1466.***********2': ('*******.4.1.1466.***********2', OID_LDAP_SYNTAX, 'Protocol Information [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********3': ('*******.4.1.1466.***********3', OID_LDAP_SYNTAX, 'Presentation Address [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********4': ('*******.4.1.1466.***********4', OID_LDAP_SYNTAX, 'Printable String', 'RFC4517'),
          '*******.4.1.1466.***********5': ('*******.4.1.1466.***********5', OID_LDAP_SYNTAX, 'Subtree specification [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********6': ('*******.4.1.1466.***********6', OID_LDAP_SYNTAX, 'Supplier Information [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********7': ('*******.4.1.1466.***********7', OID_LDAP_SYNTAX, 'Supplier Or Consumer [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********8': ('*******.4.1.1466.***********8', OID_LDAP_SYNTAX, 'Supplier And Consumer [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********9': ('*******.4.1.1466.***********9', OID_LDAP_SYNTAX, 'Supported Algorithm [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********0': ('*******.4.1.1466.***********0', OID_LDAP_SYNTAX, 'Telephone Number', 'RFC4517'),
          '*******.4.1.1466.***********1': ('*******.4.1.1466.***********1', OID_LDAP_SYNTAX, 'Teletex Terminal Identifier', 'RFC4517'),
          '*******.4.1.1466.***********2': ('*******.4.1.1466.***********2', OID_LDAP_SYNTAX, 'Telex Number', 'RFC4517'),
          '*******.4.1.1466.***********3': ('*******.4.1.1466.***********3', OID_LDAP_SYNTAX, 'UTC Time [DEPRECATED]', 'RFC4517'),
          '*******.4.1.1466.***********4': ('*******.4.1.1466.***********4', OID_LDAP_SYNTAX, 'LDAP Syntax Description', 'RFC4517'),
          '*******.4.1.1466.***********5': ('*******.4.1.1466.***********5', OID_LDAP_SYNTAX, 'Modify rights [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********6': ('*******.4.1.1466.***********6', OID_LDAP_SYNTAX, 'LDAP Schema Definition [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********7': ('*******.4.1.1466.***********7', OID_LDAP_SYNTAX, 'LDAP Schema Description [OBSOLETE]', 'RFC2252'),
          '*******.4.1.1466.***********8': ('*******.4.1.1466.***********8', OID_LDAP_SYNTAX, 'Substring Assertion', 'RFC4517'),
          '2.16.840.1.113719.1.1.5.1.0': ('2.16.840.1.113719.1.1.5.1.0', OID_LDAP_SYNTAX, 'Unknown', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.6': ('2.16.840.1.113719.1.1.5.1.6', OID_LDAP_SYNTAX, 'Case Ignore List', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.12': ('2.16.840.1.113719.1.1.5.1.12', OID_LDAP_SYNTAX, 'Tagged Data', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.13': ('2.16.840.1.113719.1.1.5.1.13', OID_LDAP_SYNTAX, 'Octet List', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.14': ('2.16.840.1.113719.1.1.5.1.14', OID_LDAP_SYNTAX, 'Tagged String', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.15': ('2.16.840.1.113719.1.1.5.1.15', OID_LDAP_SYNTAX, 'Tagged Name And String', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.16': ('2.16.840.1.113719.1.1.5.1.16', OID_LDAP_SYNTAX, 'NDS Replica Pointer', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.17': ('2.16.840.1.113719.1.1.5.1.17', OID_LDAP_SYNTAX, 'NDS ACL', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.19': ('2.16.840.1.113719.1.1.5.1.19', OID_LDAP_SYNTAX, 'NDS Timestamp', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.22': ('2.16.840.1.113719.1.1.5.1.22', OID_LDAP_SYNTAX, 'Counter', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.23': ('2.16.840.1.113719.1.1.5.1.23', OID_LDAP_SYNTAX, 'Tagged Name', 'NOVELL'),
          '2.16.840.1.113719.1.1.5.1.25': ('2.16.840.1.113719.1.1.5.1.25', OID_LDAP_SYNTAX, 'Typed Name', 'NOVELL'),

          # ldap url extensions

          # matching rules
          '1.2.36.79672281.1.13.2': ('1.2.36.79672281.1.13.2', OID_MATCHING_RULE, 'componentFilterMatch', 'RFC3687'),
          '1.2.36.79672281.1.13.3': ('1.2.36.79672281.1.13.3', OID_MATCHING_RULE, 'rdnMatch', 'RFC3687'),
          '1.2.36.79672281.1.13.5': ('1.2.36.79672281.1.13.5', OID_MATCHING_RULE, 'presentMatch', 'RFC3687'),
          '1.2.36.79672281.1.13.6': ('1.2.36.79672281.1.13.6', OID_MATCHING_RULE, 'allComponentsMatch', 'RFC3687'),
          '1.2.36.79672281.1.13.7': ('1.2.36.79672281.1.13.7', OID_MATCHING_RULE, 'directoryComponentsMatch', 'RFC3687'),
          '1.2.840.113556.1.4.803': ('1.2.840.113556.1.4.803', OID_MATCHING_RULE, 'Bit AND', 'MICROSOFT'),
          '1.2.840.113556.1.4.804': ('1.2.840.113556.1.4.804', OID_MATCHING_RULE, 'Bit OR', 'MICROSOFT'),
          '1.2.840.113556.1.4.1941': ('1.2.840.113556.1.4.1941', OID_MATCHING_RULE, 'Transitive Evaluation', 'MICROSOFT'),
          '1.2.840.113556.1.4.2253': ('1.2.840.113556.1.4.2253', OID_MATCHING_RULE, 'DN with data', 'MICROSOFT'),
          '*******.1.16.2': ('*******.1.16.2', OID_MATCHING_RULE, 'uuidMatch', 'RFC4530'),
          '*******.1.16.3': ('*******.1.16.3', OID_MATCHING_RULE, 'uuidOrderingMatch', 'RFC4530'),
          '*******.4.1.1466.109.114.1': ('*******.4.1.1466.109.114.1', OID_MATCHING_RULE, 'caseExactIA5Match', 'RFC4517'),
          '*******.4.1.1466.109.114.2': ('*******.4.1.1466.109.114.2', OID_MATCHING_RULE, 'caseIgnoreIA5Match', 'RFC4517'),
          '*******.4.1.1466.109.114.3': ('*******.4.1.1466.109.114.3', OID_MATCHING_RULE, 'caseIgnoreIA5SubstringsMatch', 'RFC4517'),
          '2.5.13.0': ('2.5.13.0', OID_MATCHING_RULE, 'objectIdentifierMatch', 'RFC4517'),
          '2.5.13.1': ('2.5.13.1', OID_MATCHING_RULE, 'distinguishedNameMatch', 'RFC4517'),
          '2.5.13.2': ('2.5.13.2', OID_MATCHING_RULE, 'caseIgnoreMatch', 'RFC4517'),
          '2.5.13.3': ('2.5.13.3', OID_MATCHING_RULE, 'caseIgnoreOrderingMatch', 'RFC4517'),
          '2.5.13.4': ('2.5.13.4', OID_MATCHING_RULE, 'caseIgnoreSubstringsMatch', 'RFC4517'),
          '2.5.13.5': ('2.5.13.5', OID_MATCHING_RULE, 'caseExactMatch', 'RFC4517'),
          '2.5.13.6': ('2.5.13.6', OID_MATCHING_RULE, 'caseExactOrderingMatch', 'RFC4517'),
          '2.5.13.7': ('2.5.13.7', OID_MATCHING_RULE, 'caseExactSubstringsMatch', 'RFC4517'),
          '2.5.13.8': ('2.5.13.8', OID_MATCHING_RULE, 'numericStringMatch', 'RFC4517'),
          '2.5.13.9': ('2.5.13.9', OID_MATCHING_RULE, 'numericStringOrderingMatch', 'RFC4517'),
          '2.5.13.10': ('2.5.13.10', OID_MATCHING_RULE, 'numericStringSubstringsMatch', 'RFC4517'),
          '2.5.13.11': ('2.5.13.11', OID_MATCHING_RULE, 'caseIgnoreListMatch', 'RFC4517'),
          '2.5.13.12': ('2.5.13.12', OID_MATCHING_RULE, 'caseIgnoreListSubstringsMatch', 'RFC4517'),
          '2.5.13.13': ('2.5.13.13', OID_MATCHING_RULE, 'booleanMatch', 'RFC4517'),
          '2.5.13.14': ('2.5.13.14', OID_MATCHING_RULE, 'integerMatch', 'RFC4517'),
          '2.5.13.15': ('2.5.13.15', OID_MATCHING_RULE, 'integerOrderingMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'bitStringMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'octetStringMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'octetStringOrderingMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'telephoneNumberMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'telephoneNumberSubstringsMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'presentationAddressMatch', 'RFC2252'),
          '*********': ('*********', OID_MATCHING_RULE, 'uniqueMemberMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'protocolInformationMatch', 'RFC2252'),
          '*********': ('*********', OID_MATCHING_RULE, 'generalizedTimeMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'generalizedTimeOrderingMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'integerFirstComponentMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'objectIdentifierFirstComponentMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'directoryStringFirstComponentMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'wordMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'keywordMatch', 'RFC4517'),
          '*********': ('*********', OID_MATCHING_RULE, 'certificateExactMatch', 'RFC4523'),
          '*********': ('*********', OID_MATCHING_RULE, 'certificateMatch', 'RFC4523'),
          '2.5.13.36': ('2.5.13.36', OID_MATCHING_RULE, 'certificatePairExactMatch', 'RFC4523'),
          '2.5.13.37': ('2.5.13.37', OID_MATCHING_RULE, 'certificatePairMatch', 'RFC4523'),
          '2.5.13.38': ('2.5.13.38', OID_MATCHING_RULE, 'certificateListExactMatch', 'RFC4523'),
          '2.5.13.39': ('2.5.13.39', OID_MATCHING_RULE, 'certificateListMatch', 'RFC4523'),
          '*********': ('*********', OID_MATCHING_RULE, 'algorithmIdentifierMatch', 'RFC4523'),
          '*********': ('*********', OID_MATCHING_RULE, 'storedPrefixMatch', 'RFC3698'),

          # name forms
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiBusinessEntityNameForm', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiContactNameForm', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiAddressNameForm', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiBusinessServiceNameForm', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiBindingTemplateNameForm', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiTModelInstanceInfoNameForm', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiTModelNameForm', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiPublisherAssertionNameForm', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_NAME_FORM, 'uddiv3SubscriptionNameForm', 'RFC4403'),
          '*******.*********0': ('*******.*********0', OID_NAME_FORM, 'uddiv3EntityObituaryNameForm', 'RFC4403'),
          '*******.4.1.1466.345': ('*******.4.1.1466.345', OID_NAME_FORM, 'domainNameForm', 'RFC2247'),

          # object classes
          '0.9.2342.********.100.4.3': ('0.9.2342.********.100.4.3', OID_OBJECT_CLASS, 'pilotObject', 'RFC1274'),
          '0.9.2342.********.100.4.4': ('0.9.2342.********.100.4.4', OID_OBJECT_CLASS, 'pilotPerson', 'RFC1274'),
          '0.9.2342.********.100.4.5': ('0.9.2342.********.100.4.5', OID_OBJECT_CLASS, 'account', 'RFC4524'),
          '0.9.2342.********.100.4.6': ('0.9.2342.********.100.4.6', OID_OBJECT_CLASS, 'document', 'RFC4524'),
          '0.9.2342.********.100.4.7': ('0.9.2342.********.100.4.7', OID_OBJECT_CLASS, 'room', 'RFC4524'),
          '0.9.2342.********.100.4.8': ('0.9.2342.********.100.4.8', OID_OBJECT_CLASS, 'documentSeries', 'RFC4524'),
          '0.9.2342.********.100.4.13': ('0.9.2342.********.100.4.13', OID_OBJECT_CLASS, 'domain', 'RFC4524'),
          '0.9.2342.********.100.4.14': ('0.9.2342.********.100.4.14', OID_OBJECT_CLASS, 'RFC822LocalPart', 'RFC4524'),
          '0.9.2342.********.100.4.15': ('0.9.2342.********.100.4.15', OID_OBJECT_CLASS, 'dNSDomain', 'RFC1274'),
          '0.9.2342.********.100.4.17': ('0.9.2342.********.100.4.17', OID_OBJECT_CLASS, 'domainRelatedObject', 'RFC4524'),
          '0.9.2342.********.100.4.18': ('0.9.2342.********.100.4.18', OID_OBJECT_CLASS, 'friendlyCountry', 'RFC4524'),
          '0.9.2342.********.100.4.19': ('0.9.2342.********.100.4.19', OID_OBJECT_CLASS, 'simpleSecurityObject', 'RFC4524'),
          '0.9.2342.********.100.4.20': ('0.9.2342.********.100.4.20', OID_OBJECT_CLASS, 'pilotOrganization', 'RFC1274'),
          '0.9.2342.********.100.4.21': ('0.9.2342.********.100.4.21', OID_OBJECT_CLASS, 'pilotDSA', 'RFC1274'),
          '0.9.2342.********.100.4.22': ('0.9.2342.********.100.4.22', OID_OBJECT_CLASS, 'qualityLabelledData', 'RFC1274'),
          '1.2.840.113556.1.5.87': ('1.2.840.113556.1.5.87', OID_OBJECT_CLASS, 'calEntry', 'RFC2739'),
          '********.2.6.253': ('********.2.6.253', OID_OBJECT_CLASS, 'printerLPR', 'RFC3712'),
          '********.2.6.254': ('********.2.6.254', OID_OBJECT_CLASS, 'slpServicePrinter', 'RFC3712'),
          '********.2.6.255': ('********.2.6.255', OID_OBJECT_CLASS, 'printerService', 'RFC3712'),
          '********.2.6.256': ('********.2.6.256', OID_OBJECT_CLASS, 'printerIPP', 'RFC3712'),
          '********.2.6.257': ('********.2.6.257', OID_OBJECT_CLASS, 'printerServiceAuxClass', 'RFC3712'),
          '********.2.6.258': ('********.2.6.258', OID_OBJECT_CLASS, 'printerAbstract', 'RFC3712'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiBusinessEntity', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiContact', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiAddress', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiBusinessService', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiBindingTemplate', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiTModelInstanceInfo', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiTModel', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiPublisherAssertion', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'uddiv3Subscription', 'RFC4403'),
          '*******.*********': ('*******.*********', OID_OBJECT_CLASS, 'uddiv3EntityObituary', 'RFC4403'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'vPIMUser', 'RFC4237'),
          '*******.1.3.1': ('*******.1.3.1', OID_OBJECT_CLASS, 'uidObject', 'RFC4519'),
          '*******.*******': ('*******.*******', OID_OBJECT_CLASS, 'pcimPolicy', 'RFC3703'),
          '*******.*******': ('*******.*******', OID_OBJECT_CLASS, 'pcimGroup', 'RFC3703'),
          '*******.*******': ('*******.*******', OID_OBJECT_CLASS, 'pcimGroupAuxClass', 'RFC3703'),
          '*******.*******': ('*******.*******', OID_OBJECT_CLASS, 'pcimGroupInstance', 'RFC3703'),
          '*******.*******': ('*******.*******', OID_OBJECT_CLASS, 'pcimRule', 'RFC3703'),
          '*******.*******': ('*******.*******', OID_OBJECT_CLASS, 'pcimRuleAuxClass', 'RFC3703'),
          '*******.*******': ('*******.*******', OID_OBJECT_CLASS, 'pcimRuleInstance', 'RFC3703'),
          '*******.1.6.1.8': ('*******.1.6.1.8', OID_OBJECT_CLASS, 'pcimRuleConditionAssociation', 'RFC3703'),
          '*******.1.6.1.9': ('*******.1.6.1.9', OID_OBJECT_CLASS, 'pcimRuleValidityAssociation', 'RFC3703'),
          '*******.*******0': ('*******.*******0', OID_OBJECT_CLASS, 'pcimRuleActionAssociation', 'RFC3703'),
          '*******.*******1': ('*******.*******1', OID_OBJECT_CLASS, 'pcimConditionAuxClass', 'RFC3703'),
          '*******.*******2': ('*******.*******2', OID_OBJECT_CLASS, 'pcimTPCAuxClass', 'RFC3703'),
          '*******.*******3': ('*******.*******3', OID_OBJECT_CLASS, 'pcimConditionVendorAuxClass', 'RFC3703'),
          '*******.*******4': ('*******.*******4', OID_OBJECT_CLASS, 'pcimActionAuxClass', 'RFC3703'),
          '*******.*******5': ('*******.*******5', OID_OBJECT_CLASS, 'pcimActionVendorAuxClass', 'RFC3703'),
          '*******.*******6': ('*******.*******6', OID_OBJECT_CLASS, 'pcimPolicyInstance', 'RFC3703'),
          '*******.*******7': ('*******.*******7', OID_OBJECT_CLASS, 'pcimElementAuxClass', 'RFC3703'),
          '*******.*******8': ('*******.*******8', OID_OBJECT_CLASS, 'pcimRepository', 'RFC3703'),
          '*******.*******9': ('*******.*******9', OID_OBJECT_CLASS, 'pcimRepositoryAuxClass', 'RFC3703'),
          '*******.*******0': ('*******.*******0', OID_OBJECT_CLASS, 'pcimRepositoryInstance', 'RFC3703'),
          '*******.*******1': ('*******.*******1', OID_OBJECT_CLASS, 'pcimSubtreesPtrAuxClass', 'RFC3703'),
          '*******.*******2': ('*******.*******2', OID_OBJECT_CLASS, 'pcimGroupContainmentAuxClass', 'RFC3703'),
          '*******.*******3': ('*******.*******3', OID_OBJECT_CLASS, 'pcimRuleContainmentAuxClass', 'RFC3703'),
          '*******.1.9.1.1': ('*******.1.9.1.1', OID_OBJECT_CLASS, 'pcelsPolicySet', 'RFC4104'),
          '*******.1.9.1.2': ('*******.1.9.1.2', OID_OBJECT_CLASS, 'pcelsPolicySetAssociation', 'RFC4104'),
          '*******.1.9.1.3': ('*******.1.9.1.3', OID_OBJECT_CLASS, 'pcelsGroup', 'RFC4104'),
          '*******.1.9.1.4': ('*******.1.9.1.4', OID_OBJECT_CLASS, 'pcelsGroupAuxClass', 'RFC4104'),
          '*******.1.9.1.5': ('*******.1.9.1.5', OID_OBJECT_CLASS, 'pcelsGroupInstance', 'RFC4104'),
          '*******.1.9.1.6': ('*******.1.9.1.6', OID_OBJECT_CLASS, 'pcelsRule', 'RFC4104'),
          '*******.1.9.1.7': ('*******.1.9.1.7', OID_OBJECT_CLASS, 'pcelsRuleAuxClass', 'RFC4104'),
          '*******.1.9.1.8': ('*******.1.9.1.8', OID_OBJECT_CLASS, 'pcelsRuleInstance', 'RFC4104'),
          '*******.1.9.1.9': ('*******.1.9.1.9', OID_OBJECT_CLASS, 'pcelsConditionAssociation', 'RFC4104'),
          '*******.1.9.1.10': ('*******.1.9.1.10', OID_OBJECT_CLASS, 'pcelsActionAssociation', 'RFC4104'),
          '*******.1.9.1.11': ('*******.1.9.1.11', OID_OBJECT_CLASS, 'pcelsSimpleConditionAuxClass', 'RFC4104'),
          '*******.1.9.1.12': ('*******.1.9.1.12', OID_OBJECT_CLASS, 'pcelsCompoundConditionAuxClass', 'RFC4104'),
          '*******.1.9.1.13': ('*******.1.9.1.13', OID_OBJECT_CLASS, 'pcelsCompoundFilterConditionAuxClass', 'RFC4104'),
          '*******.1.9.1.14': ('*******.1.9.1.14', OID_OBJECT_CLASS, 'pcelsSimpleActionAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsCompoundActionAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsVariable', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsExplicitVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsImplicitVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsSourceIPv4VariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsSourceIPv6VariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsDestinationIPv4VariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsDestinationIPv6VariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsSourcePortVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsDestinationPortVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsIPProtocolVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsIPVersionVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsIPToSVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsDSCPVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsFlowIdVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsSourceMACVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsDestinationMACVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsVLANVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsCoSVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsEthertypeVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsSourceSAPVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsDestinationSAPVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsSNAPOUIVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsSNAPTypeVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsFlowDirectionVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsValueAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsIPv4AddrValueAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsIPv6AddrValueAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsMACAddrValueAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsStringValueAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsBitStringValueAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsIntegerValueAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsBooleanValueAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsReusableContainer', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsReusableContainerAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsReusableContainerInstance', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsRoleCollection', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsFilterEntryBase', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsIPHeadersFilter', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcels8021Filter', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsFilterListAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsVendorVariableAuxClass', 'RFC4104'),
          '*******.********': ('*******.********', OID_OBJECT_CLASS, 'pcelsVendorValueAuxClass', 'RFC4104'),
          '*******.********.*******': ('*******.********.*******', OID_OBJECT_CLASS, 'DUAConfigProfile', 'RFC4876'),
          '*******.4.1.1466.101.119.2': ('*******.4.1.1466.101.119.2', OID_OBJECT_CLASS, 'dynamicObject', 'RFC2589'),
          '*******.4.1.1466.101.120.111': ('*******.4.1.1466.101.120.111', OID_OBJECT_CLASS, 'extensibleObject', 'RFC4512'),
          '*******.4.1.1466.344': ('*******.4.1.1466.344', OID_OBJECT_CLASS, 'dcObject', 'RFC4519'),
          '*******.4.1.16572.2.1.1': ('*******.4.1.16572.2.1.1', OID_OBJECT_CLASS, 'LDIFLocationURLObject', 'RFC6109'),
          '*******.4.1.16572.2.1.2': ('*******.4.1.16572.2.1.2', OID_OBJECT_CLASS, 'provider', 'RFC6109'),
          '*******.4.1.250.3.15': ('*******.4.1.250.3.15', OID_OBJECT_CLASS, 'labeledURIObject', 'RFC2079'),
          '*******.4.1.31103.1.1001': ('*******.4.1.31103.1.1001', OID_OBJECT_CLASS, 'fedfsNsdbContainerInfo', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.1002': ('*******.4.1.31103.1.1002', OID_OBJECT_CLASS, 'fedfsFsn', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.1003': ('*******.4.1.31103.1.1003', OID_OBJECT_CLASS, 'fedfsFsl', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.31103.1.1004': ('*******.4.1.31103.1.1004', OID_OBJECT_CLASS, 'fedfsNfsFsl', 'RFC-ietf-nfsv4-federated-fs-protocol-15'),
          '*******.4.1.453.7.1.1': ('*******.4.1.453.7.1.1', OID_OBJECT_CLASS, ['rFC822ToX400Mapping', 'subtree'], 'RFC2164-RFC2293'),
          '*******.4.1.453.7.1.2': ('*******.4.1.453.7.1.2', OID_OBJECT_CLASS, ['x400ToRFC822Mapping', 'table'], 'RFC2164-RFC2293'),
          '*******.4.1.453.7.1.3': ('*******.4.1.453.7.1.3', OID_OBJECT_CLASS, ['omittedORAddressComponent', 'tableEntry'], 'RFC2164-RFC2293'),
          '*******.4.1.453.7.1.4': ('*******.4.1.453.7.1.4', OID_OBJECT_CLASS, ['mixerGateway', 'textTableEntry'], 'RFC2164-RFC2293'),
          '*******.4.1.453.7.1.5': ('*******.4.1.453.7.1.5', OID_OBJECT_CLASS, 'distinguishedNameTableEntry', 'RFC2293'),
          '2.16.840.1.113730.3.2.6': ('2.16.840.1.113730.3.2.6', OID_OBJECT_CLASS, 'referral', 'RFC3296'),
          '********': ('********', OID_OBJECT_CLASS, 'subentry', 'RFC3672'),
          '********': ('********', OID_OBJECT_CLASS, 'subschema', 'RFC4512'),
          '********': ('********', OID_OBJECT_CLASS, 'collectiveAttributeSubentry', 'RFC3671'),
          '*******': ('*******', OID_OBJECT_CLASS, 'top', 'RFC4512'),
          '*******': ('*******', OID_OBJECT_CLASS, 'alias', 'RFC4512'),
          '*******': ('*******', OID_OBJECT_CLASS, 'country', 'RFC4519'),
          '*******': ('*******', OID_OBJECT_CLASS, 'locality', 'RFC4519'),
          '*******': ('*******', OID_OBJECT_CLASS, 'organization', 'RFC4519'),
          '2.5.6.5': ('2.5.6.5', OID_OBJECT_CLASS, 'organizationalUnit', 'RFC4519'),
          '2.5.6.6': ('2.5.6.6', OID_OBJECT_CLASS, 'person', 'RFC4519'),
          '2.5.6.7': ('2.5.6.7', OID_OBJECT_CLASS, 'organizationalPerson', 'RFC4519'),
          '2.5.6.8': ('2.5.6.8', OID_OBJECT_CLASS, 'organizationalRole', 'RFC4519'),
          '2.5.6.9': ('2.5.6.9', OID_OBJECT_CLASS, 'groupOfNames', 'RFC4519'),
          '*******0': ('*******0', OID_OBJECT_CLASS, 'residentialPerson', 'RFC4519'),
          '*******1': ('*******1', OID_OBJECT_CLASS, 'applicationProcess', 'RFC4519'),
          '*******2': ('*******2', OID_OBJECT_CLASS, 'applicationEntity', 'RFC2256'),
          '*******3': ('*******3', OID_OBJECT_CLASS, 'dSA', 'RFC2256'),
          '*******4': ('*******4', OID_OBJECT_CLASS, 'device', 'RFC4519'),
          '*******5': ('*******5', OID_OBJECT_CLASS, 'strongAuthenticationUser', 'RFC4523'),
          '*******6': ('*******6', OID_OBJECT_CLASS, 'certificationAuthority', 'RFC4523'),
          '*******6.2': ('*******6.2', OID_OBJECT_CLASS, 'certificationAuthority-V2', 'RFC4523'),
          '*******7': ('*******7', OID_OBJECT_CLASS, 'groupOfUniqueNames', 'RFC4519'),
          '*******8': ('*******8', OID_OBJECT_CLASS, 'userSecurityInformation', 'RFC4523'),
          '*******9': ('*******9', OID_OBJECT_CLASS, 'cRLDistributionPoint', 'RFC4523'),
          '*******0': ('*******0', OID_OBJECT_CLASS, 'dmd', 'RFC2256'),
          '*******1': ('*******1', OID_OBJECT_CLASS, 'pkiUser', 'RFC4523'),
          '*******2': ('*******2', OID_OBJECT_CLASS, 'pkiCA', 'RFC4523'),
          '*******3': ('*******3', OID_OBJECT_CLASS, 'deltaCRL', 'RFC4523'),

          # unsolicited notices
          '*******.1.21.4': ('*******.1.21.4', OID_UNSOLICITED_NOTICE, 'Aborted Transaction Notice', 'RFC5805'), '*******.4.1.1466.20036': ('*******.4.1.1466.20036', OID_UNSOLICITED_NOTICE, 'Notice of Disconnection', 'RFC4511')}
