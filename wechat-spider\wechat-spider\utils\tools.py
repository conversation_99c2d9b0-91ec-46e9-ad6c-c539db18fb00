# -*- coding: utf-8 -*-
'''
Created on 2019/5/19 3:03 PM
---------
@summary:
---------
@author:
'''
import datetime
import json
import re
import ssl
import time
import uuid
from pprint import pformat
import hashlib

import pymysql
from utils.log import log

# 全局取消ssl证书验证
ssl._create_default_https_context = ssl._create_unverified_context

_regexs = {}


# @log_function_time
def get_info(html, regexs, allow_repeat=True, fetch_one=False, split=None):
    regexs = isinstance(regexs, str) and [regexs] or regexs

    infos = []
    for regex in regexs:
        if regex == '':
            continue

        if regex not in _regexs.keys():
            _regexs[regex] = re.compile(regex, re.S)

        if fetch_one:
            infos = _regexs[regex].search(html)
            if infos:
                infos = infos.groups()
            else:
                continue
        else:
            infos = _regexs[regex].findall(str(html))

        if len(infos) > 0:
            # print(regex)
            break

    if fetch_one:
        infos = infos if infos else ('',)
        return infos if len(infos) > 1 else infos[0]
    else:
        infos = allow_repeat and infos or sorted(set(infos), key=infos.index)
        infos = split.join(infos) if split else infos
        return infos


def get_param(url, key):
    params = url.split('?')[-1].split('&')
    for param in params:
        key_value = param.split('=', 1)
        if key == key_value[0]:
            return key_value[1]
    return None


def get_current_timestamp():
    return int(time.time())


def get_current_date(date_format='%Y-%m-%d %H:%M:%S'):
    return datetime.datetime.now().strftime(date_format)
    # return time.strftime(date_format, time.localtime(time.time()))


def timestamp_to_date(timestamp, time_format='%Y-%m-%d %H:%M:%S'):
    '''
    @summary:
    ---------
    @param timestamp: 将时间戳转化为日期
    @param format: 日期格式
    ---------
    @result: 返回日期
    '''

    date = time.localtime(timestamp)
    return time.strftime(time_format, date)


def get_json(json_str):
    '''
    @summary: 取json对象
    ---------
    @param json_str: json格式的字符串
    ---------
    @result: 返回json对象
    '''

    try:
        return json.loads(json_str) if json_str else {}
    except Exception as e1:
        try:
            json_str = json_str.strip()
            json_str = json_str.replace("'", '"')
            keys = get_info(json_str, "(\w+):")
            for key in keys:
                json_str = json_str.replace(key, '"%s"' % key)

            return json.loads(json_str) if json_str else {}

        except Exception as e2:
            log.error(
                '''
                e1: %s
                format json_str: %s
                e2: %s
                ''' % (e1, json_str, e2)
            )

        return {}


def dumps_json(json_, indent=4):
    '''
    @summary: 格式化json 用于打印
    ---------
    @param json_: json格式的字符串或json对象
    ---------
    @result: 格式化后的字符串
    '''
    try:
        if isinstance(json_, str):
            json_ = get_json(json_)

        json_ = json.dumps(json_, ensure_ascii=False, indent=indent, skipkeys=True)

    except Exception as e:
        log.error(e)
        json_ = pformat(json_)

    return json_


############
def format_sql_value(value):
    if isinstance(value, str):
        value = pymysql.escape_string(value)

    elif isinstance(value, list) or isinstance(value, dict):
        value = dumps_json(value, indent=None)

    elif isinstance(value, bool):
        value = int(value)

    return value


def list2str(datas):
    '''
    列表转字符串
    :param datas: [1, 2]
    :return: (1, 2)
    '''
    data_str = str(tuple(datas))
    data_str = re.sub(",\)$", ')', data_str)
    return data_str


def make_insert_sql(table, data, auto_update=False, update_columns=(), insert_ignore=False):
    '''
    @summary: 适用于mysql， oracle数据库时间需要to_date 处理（TODO）
    ---------
    @param table:
    @param data: 表数据 json格式
    @param auto_update: 使用的是replace into， 为完全覆盖已存在的数据
    @param update_columns: 需要更新的列 默认全部，当指定值时，auto_update设置无效，当duplicate key冲突时更新指定的列
    @param insert_ignore: 数据存在忽略
    ---------
    @result:
    '''

    keys = ['`{}`'.format(key) for key in data.keys()]
    keys = list2str(keys).replace("'", '')

    values = [format_sql_value(value) for value in data.values()]
    values = list2str(values)

    if update_columns:
        if not isinstance(update_columns, (tuple, list)):
            update_columns = [update_columns]
        update_columns_ = ', '.join(["{key}=values({key})".format(key=key) for key in update_columns])
        sql = 'insert%s into {table} {keys} values {values} on duplicate key update %s' % (' ignore' if insert_ignore else '', update_columns_)

    elif auto_update:
        sql = 'replace into {table} {keys} values {values}'
    else:
        sql = 'insert%s into {table} {keys} values {values}' % (' ignore' if insert_ignore else '')

    sql = sql.format(table=table, keys=keys, values=values).replace('None', 'null')
    return sql


def make_update_sql(table, data, condition):
    '''
    @summary: 适用于mysql， oracle数据库时间需要to_date 处理（TODO）
    ---------
    @param table:
    @param data: 表数据 json格式
    @param condition: where 条件
    ---------
    @result:
    '''
    key_values = []

    for key, value in data.items():
        value = format_sql_value(value)
        if isinstance(value, str):
            key_values.append("`{}`='{}'".format(key, value))
        elif value is None:
            key_values.append("`{}`={}".format(key, 'null'))
        else:
            key_values.append("`{}`={}".format(key, value))

    key_values = ', '.join(key_values)

    sql = 'update {table} set {key_values} where {condition}'
    sql = sql.format(table=table, key_values=key_values, condition=condition)
    return sql


def make_batch_sql(table, datas, auto_update=False, update_columns=()):
    '''
    @summary: 生产批量的sql
    ---------
    @param table:
    @param datas: 表数据 [{...}]
    @param auto_update: 使用的是replace into， 为完全覆盖已存在的数据
    @param update_columns: 需要更新的列 默认全部，当指定值时，auto_update设置无效，当duplicate key冲突时更新指定的列
    ---------
    @result:
    '''
    if not datas:
        return

    keys = list(datas[0].keys())
    values_placeholder = ['%s'] * len(keys)

    values = []
    for data in datas:
        value = []
        for key in keys:
            current_data = data.get(key)
            current_data = format_sql_value(current_data)

            value.append(current_data)

        values.append(value)

    keys = ['`{}`'.format(key) for key in keys]
    keys = str(keys).replace('[', '(').replace(']', ')').replace("'", '')
    values_placeholder = str(values_placeholder).replace('[', '(').replace(']', ')').replace("'", '')

    if update_columns:
        if not isinstance(update_columns, (tuple, list)):
            update_columns = [update_columns]
        update_columns_ = ', '.join(["`{key}`=values(`{key}`)".format(key=key) for key in update_columns])
        sql = 'insert into {table} {keys} values {values_placeholder} on duplicate key update {update_columns}'.format(table=table, keys=keys, values_placeholder=values_placeholder, update_columns=update_columns_)
    elif auto_update:
        sql = 'replace into {table} {keys} values {values_placeholder}'.format(table=table, keys=keys, values_placeholder=values_placeholder)
    else:
        sql = 'insert ignore into {table} {keys} values {values_placeholder}'.format(table=table, keys=keys, values_placeholder=values_placeholder)

    return sql, values


##########

def get_mac_address():
    mac = uuid.UUID(int=uuid.getnode()).hex[-12:]
    return ":".join([mac[e:e + 2] for e in range(0, 11, 2)])


def get_md5(*args):
    '''
    @summary: 获取唯一的32位md5
    ---------
    @param *args: 参与联合去重的值
    ---------
    @result: 7c8684bcbdfcea6697650aa53d7b1405
    '''

    m = hashlib.md5()
    for arg in args:
        m.update(str(arg).encode())

    return m.hexdigest()
